{"name": "GradientsCSS", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "tsc && vite build", "vercel-build": "rm -rf node_modules package-lock.json && npm install && npm run build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.2", "@vercel/analytics": "^1.3.1", "animate.css": "^4.1.1", "aos": "^2.3.4", "axios": "^1.11.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.0.0", "dotenv": "^16.4.5", "framer-motion": "^11.5.4", "input-otp": "^1.2.4", "lucide-react": "^0.302.0", "next-themes": "^0.2.1", "qrcode.react": "^4.2.0", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^4.12.0", "react-qr-code": "^2.0.18", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.21.1", "react-scroll": "^1.9.0", "react-select": "^5.8.0", "recharts": "^2.12.7", "sonner": "^1.3.1", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.8.0"}, "devDependencies": {"@types/aos": "^3.0.7", "@types/axios": "^0.9.36", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.18", "@types/react-scroll": "^1.8.10", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "^3.4.0", "ts-node": "^10.9.2", "typescript": "^5.2.2", "vite": "^5.0.8"}}