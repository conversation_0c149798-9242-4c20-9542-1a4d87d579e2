{"extends": ["development"], "hints": {"compat-api/css": ["default", {"ignore": ["@layer"]}], "disown-opener": "off", "typescript-config/consistent-casing": "off", "typescript-config/strict": "off", "axe/name-role-value": ["default", {"button-name": "off"}]}, "browserslist": ["defaults", "not ie 11", "not and_chr <= 122", "not chrome <= 122", "not edge <= 122", "not opera <= 106", "not samsung <= 23"]}