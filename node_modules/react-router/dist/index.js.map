{"version": 3, "file": "index.js", "sources": ["../lib/context.ts", "../lib/hooks.tsx", "../lib/components.tsx", "../index.ts"], "sourcesContent": ["import * as React from \"react\";\nimport type {\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  AgnosticRouteMatch,\n  History,\n  LazyRouteFunction,\n  Location,\n  Action as NavigationType,\n  RelativeRoutingType,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject\n  // Omit `future` since those can be pulled from the `router`\n  // `NavigationContext` needs future since it doesn't have a `router` in all cases\n  extends Omit<NavigationContextObject, \"future\"> {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  unstable_flushSync?: boolean;\n  unstable_viewTransition?: boolean;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n  future: {\n    v7_relativeSplatPath: boolean;\n  };\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n  isDataRoute: boolean;\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n  isDataRoute: false,\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  Blocker,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RevalidationState,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  IDLE_BLOCKER,\n  Action as NavigationType,\n  UNSAFE_convertRouteMatchToUiMatch as convertRouteMatchToUiMatch,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  DataRouteMatch,\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, pathname),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nconst navigateEffectWarning =\n  `You should call navigate() in a React.useEffect(), not when ` +\n  `your component is first rendered.`;\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(\n  cb: Parameters<typeof React.useLayoutEffect>[0]\n) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  let { isDataRoute } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\n\nfunction useNavigateUnstable(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let { basename, future, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our history listener yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history (but only if we're not in a data router,\n      // otherwise it'll prepend the basename inside of the router).\n      // If this is a root navigation, then we navigate to the raw basename\n      // which allows the basename to have full control over the presence of a\n      // trailing slash on root links\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext,\n    ]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { future } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nexport function useRoutesImpl(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string,\n  dataRouterState?: RemixRouter[\"state\"],\n  future?: RemixRouter[\"future\"]\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined ||\n        matches[matches.length - 1].route.Component !== undefined ||\n        matches[matches.length - 1].route.lazy !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" ` +\n        `does not have an element or Component. This means it will render an <Outlet /> with a ` +\n        `null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterState,\n    future\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own <code style={codeStyles}>ErrorBoundary</code> or{\" \"}\n          <code style={codeStyles}>errorElement</code> prop on your route.\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\nconst defaultErrorElement = <DefaultErrorComponent />;\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (\n      state.location !== props.location ||\n      (state.revalidation !== \"idle\" && props.revalidation === \"idle\")\n    ) {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error !== undefined ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    (match.route.errorElement || match.route.ErrorBoundary)\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState: RemixRouter[\"state\"] | null = null,\n  future: RemixRouter[\"future\"] | null = null\n): React.ReactElement | null {\n  if (matches == null) {\n    if (dataRouterState?.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id]\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n\n      if (match.route.id) {\n        let { loaderData, errors } = dataRouterState;\n        let needsToRunLoader =\n          match.route.loader &&\n          loaderData[match.route.id] === undefined &&\n          (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error: any;\n    let shouldRenderHydrateFallback = false;\n    let errorElement: React.ReactNode | null = null;\n    let hydrateFallbackElement: React.ReactNode | null = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\n            \"route-fallback\",\n            false,\n            \"No `HydrateFallback` element provided to render during initial hydration\"\n          );\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children: React.ReactNode;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = <match.route.Component />;\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return (\n        <RenderedRoute\n          match={match}\n          routeContext={{\n            outlet,\n            matches,\n            isDataRoute: dataRouterState != null,\n          }}\n          children={children}\n        />\n      );\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState &&\n      (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        revalidation={dataRouterState.revalidation}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches, isDataRoute: true }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n}\n\nenum DataRouterStateHook {\n  UseBlocker = \"useBlocker\",\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n  UseRouteId = \"useRouteId\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nexport function useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(\n    () => ({\n      revalidate: dataRouterContext.router.revalidate,\n      state: state.revalidation,\n    }),\n    [dataRouterContext.router.revalidate, state.revalidation]\n  );\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches(): UIMatch[] {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () => matches.map((m) => convertRouteMatchToUiMatch(m, loaderData)),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router, basename } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (arg) => {\n      if (typeof shouldBlock !== \"function\") {\n        return !!shouldBlock;\n      }\n      if (basename === \"/\") {\n        return shouldBlock(arg);\n      }\n\n      // If they provided us a function and we've got an active basename, strip\n      // it from the locations we expose to the user to match the behavior of\n      // useLocation\n      let { currentLocation, nextLocation, historyAction } = arg;\n      return shouldBlock({\n        currentLocation: {\n          ...currentLocation,\n          pathname:\n            stripBasename(currentLocation.pathname, basename) ||\n            currentLocation.pathname,\n        },\n        nextLocation: {\n          ...nextLocation,\n          pathname:\n            stripBasename(nextLocation.pathname, basename) ||\n            nextLocation.pathname,\n        },\n        historyAction,\n      });\n    },\n    [basename, shouldBlock]\n  );\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey)\n    ? state.blockers.get(blockerKey)!\n    : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable(): NavigateFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our router subscriber yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n\n  return navigate;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import type {\n  InitialEntry,\n  LazyRouteFunction,\n  Location,\n  MemoryHistory,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RouterState,\n  RouterSubscriber,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\nimport * as React from \"react\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./context\";\nimport {\n  _renderMatches,\n  useAsyncValue,\n  useInRouterContext,\n  useLocation,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  useRoutesImpl,\n} from \"./hooks\";\n\nexport interface FutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_startTransition: boolean;\n}\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n  // Only accept future flags relevant to rendering behavior\n  // routing flags should be accessed via router.future\n  future?: Partial<Pick<FutureConfig, \"v7_startTransition\">>;\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let { v7_startTransition } = future || {};\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (newState: RouterState) => {\n      if (v7_startTransition && startTransitionImpl) {\n        startTransitionImpl(() => setStateImpl(newState));\n      } else {\n        setStateImpl(newState);\n      }\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={basename}\n            location={state.location}\n            navigationType={state.historyAction}\n            navigator={navigator}\n            future={{\n              v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n            }}\n          >\n            {state.initialized || router.future.v7_partialHydration ? (\n              <DataRoutes\n                routes={router.routes}\n                future={router.future}\n                state={state}\n              />\n            ) : (\n              fallbackElement\n            )}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  future?: Partial<FutureConfig>;\n}\n\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n  future,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  let { future, static: isStatic } = React.useContext(NavigationContext);\n\n  warning(\n    !isStatic,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(\n    to,\n    getResolveToMatches(matches, future.v7_relativeSplatPath),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n\n  React.useEffect(\n    () => navigate(JSON.parse(jsonPath), { replace, state, relative }),\n    [navigate, jsonPath, relative, replace, state]\n  );\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n  future?: Partial<Pick<FutureConfig, \"v7_relativeSplatPath\">>;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n  future,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({\n      basename,\n      navigator,\n      static: staticProp,\n      future: {\n        v7_relativeSplatPath: false,\n        ...future,\n      },\n    }),\n    [basename, future, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key,\n      },\n      navigationType,\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (locationContext == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider children={children} value={locationContext} />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        promise._error !== undefined\n          ? AwaitRenderStatus.error\n          : promise._data !== undefined\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    let treePath = [...parentPath, index];\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary:\n        element.props.ErrorBoundary != null ||\n        element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "import * as React from \"react\";\nimport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  ErrorResponse,\n  Fetcher,\n  HydrationState,\n  InitialEntry,\n  JsonFunction,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  RedirectFunction,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  FutureConfig as RouterFutureConfig,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  resolvePath,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  FutureConfig,\n  IndexRouteProps,\n  LayoutRouteProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  PathRouteProps,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n} from \"./lib/components\";\nimport {\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createRoutesFromChildren,\n  renderMatches,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  NavigateOptions,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteId,\n  useRouteLoaderData,\n  useRoutes,\n  useRoutesImpl,\n} from \"./lib/hooks\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  DataRouteMatch,\n  DataRouteObject,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  Pathname,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  Blocker,\n  BlockerFunction,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  renderMatches,\n  resolvePath,\n  useBlocker,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nfunction mapRouteProperties(route: RouteObject) {\n  let updates: Partial<RouteObject> & { hasErrorBoundary: boolean } = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null,\n  };\n\n  if (route.Component) {\n    if (__DEV__) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - \" +\n            \"`Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React.createElement(route.Component),\n      Component: undefined,\n    });\n  }\n\n  if (route.HydrateFallback) {\n    if (__DEV__) {\n      if (route.hydrateFallbackElement) {\n        warning(\n          false,\n          \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" +\n            \"`HydrateFallback` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined,\n    });\n  }\n\n  if (route.ErrorBoundary) {\n    if (__DEV__) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" +\n            \"`ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined,\n    });\n  }\n\n  return updates;\n}\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    mapRouteProperties,\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  LocationContext as UNSAFE_LocationContext,\n  NavigationContext as UNSAFE_NavigationContext,\n  RouteContext as UNSAFE_RouteContext,\n  mapRouteProperties as UNSAFE_mapRouteProperties,\n  useRouteId as UNSAFE_useRouteId,\n  useRoutesImpl as UNSAFE_useRoutesImpl,\n};\n"], "names": ["DataRouterContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "isDataRoute", "RouteErrorContext", "useHref", "to", "_temp", "relative", "useInRouterContext", "invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "navigateEffectWarning", "useIsomorphicLayoutEffect", "cb", "isStatic", "static", "useLayoutEffect", "useNavigate", "useNavigateStable", "useNavigateUnstable", "dataRouterContext", "future", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getResolveToMatches", "v7_relativeSplatPath", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "createElement", "Provider", "value", "useParams", "routeMatch", "length", "params", "_temp2", "useRoutes", "routes", "locationArg", "useRoutesImpl", "dataRouterState", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "pathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "parentSegments", "split", "segments", "slice", "join", "matchRoutes", "element", "undefined", "Component", "lazy", "renderedMatches", "_renderMatches", "map", "match", "Object", "assign", "encodeLocation", "_extends", "key", "NavigationType", "Pop", "DefaultErrorComponent", "error", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "console", "Fragment", "style", "fontStyle", "defaultErrorElement", "RenderErrorBoundary", "constructor", "props", "revalidation", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "children", "component", "RenderedRoute", "_ref", "staticContext", "errorElement", "Error<PERSON>ou<PERSON><PERSON>", "_deepestRenderedBoundaryId", "id", "_dataRouterState2", "_dataRouterState", "errors", "errorIndex", "findIndex", "m", "keys", "Math", "min", "renderFallback", "fallbackIndex", "v7_partialHydration", "i", "HydrateFallback", "hydrateFallbackElement", "loaderData", "needsToRunLoader", "loader", "reduceRight", "index", "shouldRenderHydrateFallback", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useRouteId", "UseRouteId", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "useMatches", "UseMatches", "convertRouteMatchToUiMatch", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "actionData", "_state$errors", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "set<PERSON><PERSON>er<PERSON>ey", "useState", "blockerFunction", "arg", "currentLocation", "nextLocation", "historyAction", "stripBasename", "useEffect", "String", "deleteBlocker", "get<PERSON><PERSON>er", "blockers", "has", "get", "IDLE_BLOCKER", "UseNavigateStable", "fromRouteId", "alreadyWarned", "cond", "START_TRANSITION", "startTransitionImpl", "RouterProvider", "fallbackElement", "setStateImpl", "v7_startTransition", "setState", "newState", "subscribe", "n", "opts", "preventScrollReset", "Router", "initialized", "DataRoutes", "_ref2", "MemoryRouter", "_ref3", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "action", "listen", "Navigate", "_ref4", "jsonPath", "Outlet", "Route", "_props", "_ref5", "basenameProp", "locationProp", "staticProp", "navigationContext", "locationContext", "trailingPathname", "Routes", "_ref6", "createRoutesFromChildren", "Await", "_ref7", "resolve", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ResolveAwait", "AwaitRenderStatus", "neverSettledPromise", "Promise", "promise", "pending", "success", "defineProperty", "renderError", "reject", "catch", "_tracked", "then", "data", "Aborted<PERSON>eferredError", "_ref8", "to<PERSON><PERSON>", "Children", "for<PERSON>ach", "isValidElement", "treePath", "type", "apply", "name", "caseSensitive", "hasErrorBou<PERSON>ry", "shouldRevalidate", "handle", "renderMatches", "mapRouteProperties", "updates", "createMemoryRouter", "createRouter", "v7_prependBasename", "hydrationData", "initialize"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA;AACA;AA+DO,MAAMA,iBAAiB,gBAC5BC,KAAK,CAACC,aAAa,CAAiC,IAAI,EAAC;AAC3D,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXL,iBAAiB,CAACM,WAAW,GAAG,YAAY,CAAA;AAC9C,CAAA;AAEO,MAAMC,sBAAsB,gBAAGN,KAAK,CAACC,aAAa,CAEvD,IAAI,EAAC;AACP,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXE,sBAAsB,CAACD,WAAW,GAAG,iBAAiB,CAAA;AACxD,CAAA;AAEO,MAAME,YAAY,gBAAGP,KAAK,CAACC,aAAa,CAAwB,IAAI,CAAC,CAAA;AAC5E,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXG,YAAY,CAACF,WAAW,GAAG,OAAO,CAAA;AACpC,CAAA;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAmBO,MAAMG,iBAAiB,gBAAGR,KAAK,CAACC,aAAa,CAClD,IACF,EAAC;AAED,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXI,iBAAiB,CAACH,WAAW,GAAG,YAAY,CAAA;AAC9C,CAAA;AAOO,MAAMI,eAAe,gBAAGT,KAAK,CAACC,aAAa,CAChD,IACF,EAAC;AAED,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXK,eAAe,CAACJ,WAAW,GAAG,UAAU,CAAA;AAC1C,CAAA;MAQaK,YAAY,gBAAGV,KAAK,CAACC,aAAa,CAAqB;AAClEU,EAAAA,MAAM,EAAE,IAAI;AACZC,EAAAA,OAAO,EAAE,EAAE;AACXC,EAAAA,WAAW,EAAE,KAAA;AACf,CAAC,EAAC;AAEF,IAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXM,YAAY,CAACL,WAAW,GAAG,OAAO,CAAA;AACpC,CAAA;AAEO,MAAMS,iBAAiB,gBAAGd,KAAK,CAACC,aAAa,CAAM,IAAI,CAAC,CAAA;AAE/D,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;EACXU,iBAAiB,CAACT,WAAW,GAAG,YAAY,CAAA;AAC9C;;AC9HA;AACA;AACA;AACA;AACA;AACA;AACO,SAASU,OAAOA,CACrBC,EAAM,EAAAC,KAAA,EAEE;EAAA,IADR;AAAEC,IAAAA,QAAAA;AAA6C,GAAC,GAAAD,KAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,KAAA,CAAA;EAErD,CACEE,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtBgB,YAAAA,GAAAA,gBAAS,CAEP,KAAA;AACA;AAAA,EAAA,oEAAA,CAAA,GAHFA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;EAOT,IAAI;IAAEC,QAAQ;AAAEC,IAAAA,SAAAA;AAAU,GAAC,GAAGtB,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC,CAAA;EACjE,IAAI;IAAEgB,IAAI;IAAEC,QAAQ;AAAEC,IAAAA,MAAAA;AAAO,GAAC,GAAGC,eAAe,CAACX,EAAE,EAAE;AAAEE,IAAAA,QAAAA;AAAS,GAAC,CAAC,CAAA;EAElE,IAAIU,cAAc,GAAGH,QAAQ,CAAA;;AAE7B;AACA;AACA;AACA;EACA,IAAIJ,QAAQ,KAAK,GAAG,EAAE;AACpBO,IAAAA,cAAc,GACZH,QAAQ,KAAK,GAAG,GAAGJ,QAAQ,GAAGQ,SAAS,CAAC,CAACR,QAAQ,EAAEI,QAAQ,CAAC,CAAC,CAAA;AACjE,GAAA;EAEA,OAAOH,SAAS,CAACQ,UAAU,CAAC;AAAEL,IAAAA,QAAQ,EAAEG,cAAc;IAAEF,MAAM;AAAEF,IAAAA,IAAAA;AAAK,GAAC,CAAC,CAAA;AACzE,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASL,kBAAkBA,GAAY;AAC5C,EAAA,OAAOnB,KAAK,CAACuB,UAAU,CAACd,eAAe,CAAC,IAAI,IAAI,CAAA;AAClD,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASsB,WAAWA,GAAa;EACtC,CACEZ,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtBgB,YAAAA,GAAAA,gBAAS,CAEP,KAAA;AACA;AAAA,EAAA,wEAAA,CAAA,GAHFA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAOT,EAAA,OAAOpB,KAAK,CAACuB,UAAU,CAACd,eAAe,CAAC,CAACuB,QAAQ,CAAA;AACnD,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,iBAAiBA,GAAmB;AAClD,EAAA,OAAOjC,KAAK,CAACuB,UAAU,CAACd,eAAe,CAAC,CAACyB,cAAc,CAAA;AACzD,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,QAAQA,CAGtBC,OAAiC,EAA8B;EAC/D,CACEjB,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtBgB,YAAAA,GAAAA,gBAAS,CAEP,KAAA;AACA;AAAA,EAAA,qEAAA,CAAA,GAHFA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;EAOT,IAAI;AAAEK,IAAAA,QAAAA;GAAU,GAAGM,WAAW,EAAE,CAAA;AAChC,EAAA,OAAO/B,KAAK,CAACqC,OAAO,CAClB,MAAMC,SAAS,CAAiBF,OAAO,EAAEX,QAAQ,CAAC,EAClD,CAACA,QAAQ,EAAEW,OAAO,CACpB,CAAC,CAAA;AACH,CAAA;;AAEA;AACA;AACA;;AAMA,MAAMG,qBAAqB,GACzB,8DACmC,GAAA,mCAAA,CAAA;;AAErC;AACA,SAASC,yBAAyBA,CAChCC,EAA+C,EAC/C;EACA,IAAIC,QAAQ,GAAG1C,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC,CAACmC,MAAM,CAAA;EACzD,IAAI,CAACD,QAAQ,EAAE;AACb;AACA;AACA;AACA1C,IAAAA,KAAK,CAAC4C,eAAe,CAACH,EAAE,CAAC,CAAA;AAC3B,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,WAAWA,GAAqB;EAC9C,IAAI;AAAEhC,IAAAA,WAAAA;AAAY,GAAC,GAAGb,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC,CAAA;AACpD;AACA;EACA,OAAOG,WAAW,GAAGiC,iBAAiB,EAAE,GAAGC,mBAAmB,EAAE,CAAA;AAClE,CAAA;AAEA,SAASA,mBAAmBA,GAAqB;EAC/C,CACE5B,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtBgB,YAAAA,GAAAA,gBAAS,CAEP,KAAA;AACA;AAAA,EAAA,wEAAA,CAAA,GAHFA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAOT,EAAA,IAAI4B,iBAAiB,GAAGhD,KAAK,CAACuB,UAAU,CAACxB,iBAAiB,CAAC,CAAA;EAC3D,IAAI;IAAEsB,QAAQ;IAAE4B,MAAM;AAAE3B,IAAAA,SAAAA;AAAU,GAAC,GAAGtB,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC,CAAA;EACzE,IAAI;AAAEI,IAAAA,OAAAA;AAAQ,GAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC,CAAA;EAChD,IAAI;AAAEe,IAAAA,QAAQ,EAAEyB,gBAAAA;GAAkB,GAAGnB,WAAW,EAAE,CAAA;AAElD,EAAA,IAAIoB,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACrCC,0BAAmB,CAAC1C,OAAO,EAAEqC,MAAM,CAACM,oBAAoB,CAC1D,CAAC,CAAA;AAED,EAAA,IAAIC,SAAS,GAAGxD,KAAK,CAACyD,MAAM,CAAC,KAAK,CAAC,CAAA;AACnCjB,EAAAA,yBAAyB,CAAC,MAAM;IAC9BgB,SAAS,CAACE,OAAO,GAAG,IAAI,CAAA;AAC1B,GAAC,CAAC,CAAA;EAEF,IAAIC,QAA0B,GAAG3D,KAAK,CAAC4D,WAAW,CAChD,UAAC5C,EAAe,EAAE6C,OAAwB,EAAU;AAAA,IAAA,IAAlCA,OAAwB,KAAA,KAAA,CAAA,EAAA;MAAxBA,OAAwB,GAAG,EAAE,CAAA;AAAA,KAAA;AAC7C3D,IAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA0D,YAAAA,GAAAA,cAAO,CAACN,SAAS,CAACE,OAAO,EAAEnB,qBAAqB,CAAC,GAAA,KAAA,CAAA,CAAA;;AAEjD;AACA;AACA,IAAA,IAAI,CAACiB,SAAS,CAACE,OAAO,EAAE,OAAA;AAExB,IAAA,IAAI,OAAO1C,EAAE,KAAK,QAAQ,EAAE;AAC1BM,MAAAA,SAAS,CAACyC,EAAE,CAAC/C,EAAE,CAAC,CAAA;AAChB,MAAA,OAAA;AACF,KAAA;IAEA,IAAIgD,IAAI,GAAGC,SAAS,CAClBjD,EAAE,EACFoC,IAAI,CAACc,KAAK,CAACf,kBAAkB,CAAC,EAC9BD,gBAAgB,EAChBW,OAAO,CAAC3C,QAAQ,KAAK,MACvB,CAAC,CAAA;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAI8B,iBAAiB,IAAI,IAAI,IAAI3B,QAAQ,KAAK,GAAG,EAAE;MACjD2C,IAAI,CAACvC,QAAQ,GACXuC,IAAI,CAACvC,QAAQ,KAAK,GAAG,GACjBJ,QAAQ,GACRQ,SAAS,CAAC,CAACR,QAAQ,EAAE2C,IAAI,CAACvC,QAAQ,CAAC,CAAC,CAAA;AAC5C,KAAA;IAEA,CAAC,CAAC,CAACoC,OAAO,CAACM,OAAO,GAAG7C,SAAS,CAAC6C,OAAO,GAAG7C,SAAS,CAAC8C,IAAI,EACrDJ,IAAI,EACJH,OAAO,CAACQ,KAAK,EACbR,OACF,CAAC,CAAA;AACH,GAAC,EACD,CACExC,QAAQ,EACRC,SAAS,EACT6B,kBAAkB,EAClBD,gBAAgB,EAChBF,iBAAiB,CAErB,CAAC,CAAA;AAED,EAAA,OAAOW,QAAQ,CAAA;AACjB,CAAA;AAEA,MAAMW,aAAa,gBAAGtE,KAAK,CAACC,aAAa,CAAU,IAAI,CAAC,CAAA;;AAExD;AACA;AACA;AACA;AACA;AACO,SAASsE,gBAAgBA,GAA+B;AAC7D,EAAA,OAAOvE,KAAK,CAACuB,UAAU,CAAC+C,aAAa,CAAC,CAAA;AACxC,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,SAASA,CAACC,OAAiB,EAA6B;EACtE,IAAI9D,MAAM,GAAGX,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC,CAACC,MAAM,CAAA;AAClD,EAAA,IAAIA,MAAM,EAAE;AACV,IAAA,oBACEX,KAAA,CAAA0E,aAAA,CAACJ,aAAa,CAACK,QAAQ,EAAA;AAACC,MAAAA,KAAK,EAAEH,OAAAA;AAAQ,KAAA,EAAE9D,MAA+B,CAAC,CAAA;AAE7E,GAAA;AACA,EAAA,OAAOA,MAAM,CAAA;AACf,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASkE,SAASA,GAIvB;EACA,IAAI;AAAEjE,IAAAA,OAAAA;AAAQ,GAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC,CAAA;EAChD,IAAIoE,UAAU,GAAGlE,OAAO,CAACA,OAAO,CAACmE,MAAM,GAAG,CAAC,CAAC,CAAA;AAC5C,EAAA,OAAOD,UAAU,GAAIA,UAAU,CAACE,MAAM,GAAW,EAAE,CAAA;AACrD,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASrD,eAAeA,CAC7BX,EAAM,EAAAiE,MAAA,EAEA;EAAA,IADN;AAAE/D,IAAAA,QAAAA;AAA6C,GAAC,GAAA+D,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;EAErD,IAAI;AAAEhC,IAAAA,MAAAA;AAAO,GAAC,GAAGjD,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC,CAAA;EACpD,IAAI;AAAEI,IAAAA,OAAAA;AAAQ,GAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC,CAAA;EAChD,IAAI;AAAEe,IAAAA,QAAQ,EAAEyB,gBAAAA;GAAkB,GAAGnB,WAAW,EAAE,CAAA;AAClD,EAAA,IAAIoB,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACrCC,0BAAmB,CAAC1C,OAAO,EAAEqC,MAAM,CAACM,oBAAoB,CAC1D,CAAC,CAAA;AAED,EAAA,OAAOvD,KAAK,CAACqC,OAAO,CAClB,MACE4B,SAAS,CACPjD,EAAE,EACFoC,IAAI,CAACc,KAAK,CAACf,kBAAkB,CAAC,EAC9BD,gBAAgB,EAChBhC,QAAQ,KAAK,MACf,CAAC,EACH,CAACF,EAAE,EAAEmC,kBAAkB,EAAED,gBAAgB,EAAEhC,QAAQ,CACrD,CAAC,CAAA;AACH,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASgE,SAASA,CACvBC,MAAqB,EACrBC,WAAwC,EACb;AAC3B,EAAA,OAAOC,aAAa,CAACF,MAAM,EAAEC,WAAW,CAAC,CAAA;AAC3C,CAAA;;AAEA;AACO,SAASC,aAAaA,CAC3BF,MAAqB,EACrBC,WAAwC,EACxCE,eAAsC,EACtCrC,MAA8B,EACH;EAC3B,CACE9B,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtBgB,YAAAA,GAAAA,gBAAS,CAEP,KAAA;AACA;AAAA,EAAA,sEAAA,CAAA,GAHFA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;EAOT,IAAI;AAAEE,IAAAA,SAAAA;AAAU,GAAC,GAAGtB,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC,CAAA;EACvD,IAAI;AAAEI,IAAAA,OAAO,EAAE2E,aAAAA;AAAc,GAAC,GAAGvF,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC,CAAA;EAC/D,IAAIoE,UAAU,GAAGS,aAAa,CAACA,aAAa,CAACR,MAAM,GAAG,CAAC,CAAC,CAAA;EACxD,IAAIS,YAAY,GAAGV,UAAU,GAAGA,UAAU,CAACE,MAAM,GAAG,EAAE,CAAA;EACtD,IAAIS,cAAc,GAAGX,UAAU,GAAGA,UAAU,CAACrD,QAAQ,GAAG,GAAG,CAAA;EAC3D,IAAIiE,kBAAkB,GAAGZ,UAAU,GAAGA,UAAU,CAACa,YAAY,GAAG,GAAG,CAAA;AACnE,EAAA,IAAIC,WAAW,GAAGd,UAAU,IAAIA,UAAU,CAACe,KAAK,CAAA;AAEhD,EAAA,IAAA3F,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA,IAAI0F,UAAU,GAAIF,WAAW,IAAIA,WAAW,CAAC5B,IAAI,IAAK,EAAE,CAAA;AACxD+B,IAAAA,WAAW,CACTN,cAAc,EACd,CAACG,WAAW,IAAIE,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,EACxC,gEAAA,IAAA,IAAA,GACMP,cAAc,GAAyBK,0BAAAA,GAAAA,UAAU,GAAc,eAAA,CAAA,GAAA,sEACC,GACH,iEAAA,GAAA,+BAClC,IACUA,yCAAAA,GAAAA,UAAU,oBAAe,IACzDA,SAAAA,IAAAA,UAAU,KAAK,GAAG,GAAG,GAAG,GAAMA,UAAU,GAAA,IAAI,WACzD,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,IAAIG,mBAAmB,GAAGlE,WAAW,EAAE,CAAA;AAEvC,EAAA,IAAIC,QAAQ,CAAA;AACZ,EAAA,IAAIoD,WAAW,EAAE;AAAA,IAAA,IAAAc,qBAAA,CAAA;AACf,IAAA,IAAIC,iBAAiB,GACnB,OAAOf,WAAW,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,WAAW,CAAC,GAAGA,WAAW,CAAA;AAExE,IAAA,EACEM,kBAAkB,KAAK,GAAG,KAAA,CAAAQ,qBAAA,GACxBC,iBAAiB,CAAC1E,QAAQ,qBAA1ByE,qBAAA,CAA4BG,UAAU,CAACX,kBAAkB,CAAC,CAAA,CAAA,GAAAxF,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAF9DgB,YAAAA,GAAAA,gBAAS,QAGP,2FACmF,GAAA,iFAAA,IAAA,+DAAA,GAClBsE,kBAAkB,GAAA,KAAA,CAAI,wBACpES,iBAAiB,CAAC1E,QAAQ,GAAA,sCAAA,CAAuC,IANtFL,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AASTY,IAAAA,QAAQ,GAAGmE,iBAAiB,CAAA;AAC9B,GAAC,MAAM;AACLnE,IAAAA,QAAQ,GAAGiE,mBAAmB,CAAA;AAChC,GAAA;AAEA,EAAA,IAAIxE,QAAQ,GAAGO,QAAQ,CAACP,QAAQ,IAAI,GAAG,CAAA;EAEvC,IAAI6E,iBAAiB,GAAG7E,QAAQ,CAAA;EAChC,IAAIiE,kBAAkB,KAAK,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAIa,cAAc,GAAGb,kBAAkB,CAACvB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACqC,KAAK,CAAC,GAAG,CAAC,CAAA;AACrE,IAAA,IAAIC,QAAQ,GAAGhF,QAAQ,CAAC0C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACqC,KAAK,CAAC,GAAG,CAAC,CAAA;AACrDF,IAAAA,iBAAiB,GAAG,GAAG,GAAGG,QAAQ,CAACC,KAAK,CAACH,cAAc,CAACxB,MAAM,CAAC,CAAC4B,IAAI,CAAC,GAAG,CAAC,CAAA;AAC3E,GAAA;AAEA,EAAA,IAAI/F,OAAO,GAAGgG,WAAW,CAACzB,MAAM,EAAE;AAAE1D,IAAAA,QAAQ,EAAE6E,iBAAAA;AAAkB,GAAC,CAAC,CAAA;AAElE,EAAA,IAAApG,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;IACXF,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GAAA0D,cAAO,CACL8B,WAAW,IAAIhF,OAAO,IAAI,IAAI,oCACCoB,QAAQ,CAACP,QAAQ,GAAGO,QAAQ,CAACN,MAAM,GAAGM,QAAQ,CAACR,IAAI,GAAA,KACpF,CAAC,GAAA,KAAA,CAAA,CAAA;AAEDtB,IAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACLlD,OAAO,IAAI,IAAI,IACbA,OAAO,CAACA,OAAO,CAACmE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACgB,OAAO,KAAKC,SAAS,IACvDlG,OAAO,CAACA,OAAO,CAACmE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACkB,SAAS,KAAKD,SAAS,IACzDlG,OAAO,CAACA,OAAO,CAACmE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACmB,IAAI,KAAKF,SAAS,EACtD,mCAAA,GAAmC9E,QAAQ,CAACP,QAAQ,GAAGO,QAAQ,CAACN,MAAM,GAAGM,QAAQ,CAACR,IAAI,GAAA,KAAA,GAAA,wFACI,0DAE5F,CAAC,GAAA,KAAA,CAAA,CAAA;AACH,GAAA;EAEA,IAAIyF,eAAe,GAAGC,cAAc,CAClCtG,OAAO,IACLA,OAAO,CAACuG,GAAG,CAAEC,KAAK,IAChBC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEF,KAAK,EAAE;AACvBpC,IAAAA,MAAM,EAAEqC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE9B,YAAY,EAAE4B,KAAK,CAACpC,MAAM,CAAC;AACrDvD,IAAAA,QAAQ,EAAEI,SAAS,CAAC,CAClB6D,kBAAkB;AAClB;AACApE,IAAAA,SAAS,CAACiG,cAAc,GACpBjG,SAAS,CAACiG,cAAc,CAACH,KAAK,CAAC3F,QAAQ,CAAC,CAACA,QAAQ,GACjD2F,KAAK,CAAC3F,QAAQ,CACnB,CAAC;AACFkE,IAAAA,YAAY,EACVyB,KAAK,CAACzB,YAAY,KAAK,GAAG,GACtBD,kBAAkB,GAClB7D,SAAS,CAAC,CACR6D,kBAAkB;AAClB;AACApE,IAAAA,SAAS,CAACiG,cAAc,GACpBjG,SAAS,CAACiG,cAAc,CAACH,KAAK,CAACzB,YAAY,CAAC,CAAClE,QAAQ,GACrD2F,KAAK,CAACzB,YAAY,CACvB,CAAA;GACR,CACH,CAAC,EACHJ,aAAa,EACbD,eAAe,EACfrC,MACF,CAAC,CAAA;;AAED;AACA;AACA;EACA,IAAImC,WAAW,IAAI6B,eAAe,EAAE;AAClC,IAAA,oBACEjH,KAAA,CAAA0E,aAAA,CAACjE,eAAe,CAACkE,QAAQ,EAAA;AACvBC,MAAAA,KAAK,EAAE;AACL5C,QAAAA,QAAQ,EAAAwF,QAAA,CAAA;AACN/F,UAAAA,QAAQ,EAAE,GAAG;AACbC,UAAAA,MAAM,EAAE,EAAE;AACVF,UAAAA,IAAI,EAAE,EAAE;AACR6C,UAAAA,KAAK,EAAE,IAAI;AACXoD,UAAAA,GAAG,EAAE,SAAA;AAAS,SAAA,EACXzF,QAAQ,CACZ;QACDE,cAAc,EAAEwF,MAAc,CAACC,GAAAA;AACjC,OAAA;AAAE,KAAA,EAEDV,eACuB,CAAC,CAAA;AAE/B,GAAA;AAEA,EAAA,OAAOA,eAAe,CAAA;AACxB,CAAA;AAEA,SAASW,qBAAqBA,GAAG;AAC/B,EAAA,IAAIC,KAAK,GAAGC,aAAa,EAAE,CAAA;AAC3B,EAAA,IAAIC,OAAO,GAAGC,oBAAoB,CAACH,KAAK,CAAC,GAClCA,KAAK,CAACI,MAAM,GAAIJ,GAAAA,GAAAA,KAAK,CAACK,UAAU,GACnCL,KAAK,YAAYM,KAAK,GACtBN,KAAK,CAACE,OAAO,GACb3E,IAAI,CAACC,SAAS,CAACwE,KAAK,CAAC,CAAA;EACzB,IAAIO,KAAK,GAAGP,KAAK,YAAYM,KAAK,GAAGN,KAAK,CAACO,KAAK,GAAG,IAAI,CAAA;EACvD,IAAIC,SAAS,GAAG,wBAAwB,CAAA;AACxC,EAAA,IAAIC,SAAS,GAAG;AAAEC,IAAAA,OAAO,EAAE,QAAQ;AAAEC,IAAAA,eAAe,EAAEH,SAAAA;GAAW,CAAA;AACjE,EAAA,IAAII,UAAU,GAAG;AAAEF,IAAAA,OAAO,EAAE,SAAS;AAAEC,IAAAA,eAAe,EAAEH,SAAAA;GAAW,CAAA;EAEnE,IAAIK,OAAO,GAAG,IAAI,CAAA;AAClB,EAAA,IAAAxI,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;AACXuI,IAAAA,OAAO,CAACd,KAAK,CACX,sDAAsD,EACtDA,KACF,CAAC,CAAA;IAEDa,OAAO,gBACL1I,KAAA,CAAA0E,aAAA,CAAA1E,KAAA,CAAA4I,QAAA,EACE5I,IAAAA,eAAAA,KAAA,CAAA0E,aAAA,YAAG,yCAAsB,CAAC,eAC1B1E,KAAA,CAAA0E,aAAA,YAAG,8FAEqB,eAAA1E,KAAA,CAAA0E,aAAA,CAAA,MAAA,EAAA;AAAMmE,MAAAA,KAAK,EAAEJ,UAAAA;KAAY,EAAA,eAAmB,CAAC,EAAG,KAAA,EAAC,GAAG,eAC1EzI,KAAA,CAAA0E,aAAA,CAAA,MAAA,EAAA;AAAMmE,MAAAA,KAAK,EAAEJ,UAAAA;AAAW,KAAA,EAAC,cAAkB,CAAC,EAC3C,sBAAA,CACH,CACH,CAAA;AACH,GAAA;AAEA,EAAA,oBACEzI,KAAA,CAAA0E,aAAA,CAAA1E,KAAA,CAAA4I,QAAA,EAAA,IAAA,eACE5I,KAAA,CAAA0E,aAAA,CAAI,IAAA,EAAA,IAAA,EAAA,+BAAiC,CAAC,eACtC1E,KAAA,CAAA0E,aAAA,CAAA,IAAA,EAAA;AAAImE,IAAAA,KAAK,EAAE;AAAEC,MAAAA,SAAS,EAAE,QAAA;AAAS,KAAA;AAAE,GAAA,EAAEf,OAAY,CAAC,EACjDK,KAAK,gBAAGpI,KAAA,CAAA0E,aAAA,CAAA,KAAA,EAAA;AAAKmE,IAAAA,KAAK,EAAEP,SAAAA;AAAU,GAAA,EAAEF,KAAW,CAAC,GAAG,IAAI,EACnDM,OACD,CAAC,CAAA;AAEP,CAAA;AAEA,MAAMK,mBAAmB,gBAAG/I,KAAA,CAAA0E,aAAA,CAACkD,qBAAqB,MAAE,CAAC,CAAA;AAgB9C,MAAMoB,mBAAmB,SAAShJ,KAAK,CAAC+G,SAAS,CAGtD;EACAkC,WAAWA,CAACC,KAA+B,EAAE;IAC3C,KAAK,CAACA,KAAK,CAAC,CAAA;IACZ,IAAI,CAAC7E,KAAK,GAAG;MACXrC,QAAQ,EAAEkH,KAAK,CAAClH,QAAQ;MACxBmH,YAAY,EAAED,KAAK,CAACC,YAAY;MAChCtB,KAAK,EAAEqB,KAAK,CAACrB,KAAAA;KACd,CAAA;AACH,GAAA;EAEA,OAAOuB,wBAAwBA,CAACvB,KAAU,EAAE;IAC1C,OAAO;AAAEA,MAAAA,KAAK,EAAEA,KAAAA;KAAO,CAAA;AACzB,GAAA;AAEA,EAAA,OAAOwB,wBAAwBA,CAC7BH,KAA+B,EAC/B7E,KAA+B,EAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IACEA,KAAK,CAACrC,QAAQ,KAAKkH,KAAK,CAAClH,QAAQ,IAChCqC,KAAK,CAAC8E,YAAY,KAAK,MAAM,IAAID,KAAK,CAACC,YAAY,KAAK,MAAO,EAChE;MACA,OAAO;QACLtB,KAAK,EAAEqB,KAAK,CAACrB,KAAK;QAClB7F,QAAQ,EAAEkH,KAAK,CAAClH,QAAQ;QACxBmH,YAAY,EAAED,KAAK,CAACC,YAAAA;OACrB,CAAA;AACH,KAAA;;AAEA;AACA;AACA;AACA;IACA,OAAO;AACLtB,MAAAA,KAAK,EAAEqB,KAAK,CAACrB,KAAK,KAAKf,SAAS,GAAGoC,KAAK,CAACrB,KAAK,GAAGxD,KAAK,CAACwD,KAAK;MAC5D7F,QAAQ,EAAEqC,KAAK,CAACrC,QAAQ;AACxBmH,MAAAA,YAAY,EAAED,KAAK,CAACC,YAAY,IAAI9E,KAAK,CAAC8E,YAAAA;KAC3C,CAAA;AACH,GAAA;AAEAG,EAAAA,iBAAiBA,CAACzB,KAAU,EAAE0B,SAAc,EAAE;IAC5CZ,OAAO,CAACd,KAAK,CACX,uDAAuD,EACvDA,KAAK,EACL0B,SACF,CAAC,CAAA;AACH,GAAA;AAEAC,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO,IAAI,CAACnF,KAAK,CAACwD,KAAK,KAAKf,SAAS,gBACnC9G,KAAA,CAAA0E,aAAA,CAAChE,YAAY,CAACiE,QAAQ,EAAA;AAACC,MAAAA,KAAK,EAAE,IAAI,CAACsE,KAAK,CAACO,YAAAA;AAAa,KAAA,eACpDzJ,KAAA,CAAA0E,aAAA,CAAC5D,iBAAiB,CAAC6D,QAAQ,EAAA;AACzBC,MAAAA,KAAK,EAAE,IAAI,CAACP,KAAK,CAACwD,KAAM;AACxB6B,MAAAA,QAAQ,EAAE,IAAI,CAACR,KAAK,CAACS,SAAAA;AAAU,KAChC,CACoB,CAAC,GAExB,IAAI,CAACT,KAAK,CAACQ,QACZ,CAAA;AACH,GAAA;AACF,CAAA;AAQA,SAASE,aAAaA,CAAAC,IAAA,EAAwD;EAAA,IAAvD;IAAEJ,YAAY;IAAErC,KAAK;AAAEsC,IAAAA,QAAAA;AAA6B,GAAC,GAAAG,IAAA,CAAA;AAC1E,EAAA,IAAI7G,iBAAiB,GAAGhD,KAAK,CAACuB,UAAU,CAACxB,iBAAiB,CAAC,CAAA;;AAE3D;AACA;EACA,IACEiD,iBAAiB,IACjBA,iBAAiB,CAACL,MAAM,IACxBK,iBAAiB,CAAC8G,aAAa,KAC9B1C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAI3C,KAAK,CAACvB,KAAK,CAACmE,aAAa,CAAC,EACvD;IACAhH,iBAAiB,CAAC8G,aAAa,CAACG,0BAA0B,GAAG7C,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAA;AAC7E,GAAA;AAEA,EAAA,oBACElK,KAAA,CAAA0E,aAAA,CAAChE,YAAY,CAACiE,QAAQ,EAAA;AAACC,IAAAA,KAAK,EAAE6E,YAAAA;AAAa,GAAA,EACxCC,QACoB,CAAC,CAAA;AAE5B,CAAA;AAEO,SAASxC,cAAcA,CAC5BtG,OAA4B,EAC5B2E,aAA2B,EAC3BD,eAA4C,EAC5CrC,MAAoC,EACT;AAAA,EAAA,IAAAkH,iBAAA,CAAA;AAAA,EAAA,IAH3B5E,aAA2B,KAAA,KAAA,CAAA,EAAA;AAA3BA,IAAAA,aAA2B,GAAG,EAAE,CAAA;AAAA,GAAA;AAAA,EAAA,IAChCD,eAA4C,KAAA,KAAA,CAAA,EAAA;AAA5CA,IAAAA,eAA4C,GAAG,IAAI,CAAA;AAAA,GAAA;AAAA,EAAA,IACnDrC,MAAoC,KAAA,KAAA,CAAA,EAAA;AAApCA,IAAAA,MAAoC,GAAG,IAAI,CAAA;AAAA,GAAA;EAE3C,IAAIrC,OAAO,IAAI,IAAI,EAAE;AAAA,IAAA,IAAAwJ,gBAAA,CAAA;AACnB,IAAA,IAAA,CAAAA,gBAAA,GAAI9E,eAAe,aAAf8E,gBAAA,CAAiBC,MAAM,EAAE;AAC3B;AACA;MACAzJ,OAAO,GAAG0E,eAAe,CAAC1E,OAA2B,CAAA;AACvD,KAAC,MAAM;AACL,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;EAEA,IAAIqG,eAAe,GAAGrG,OAAO,CAAA;;AAE7B;EACA,IAAIyJ,MAAM,IAAAF,iBAAA,GAAG7E,eAAe,KAAf6E,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,iBAAA,CAAiBE,MAAM,CAAA;EACpC,IAAIA,MAAM,IAAI,IAAI,EAAE;IAClB,IAAIC,UAAU,GAAGrD,eAAe,CAACsD,SAAS,CACvCC,CAAC,IAAKA,CAAC,CAAC3E,KAAK,CAACqE,EAAE,KAAIG,MAAM,IAANA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAAGG,CAAC,CAAC3E,KAAK,CAACqE,EAAE,CAAC,CAC3C,CAAC,CAAA;IACD,EACEI,UAAU,IAAI,CAAC,CAAApK,GAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GADjBgB,gBAAS,CAAA,KAAA,EAAA,2DAAA,GAEqDiG,MAAM,CAACoD,IAAI,CACrEJ,MACF,CAAC,CAAC1D,IAAI,CAAC,GAAG,CAAC,CAAA,GAJbvF,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAMT6F,IAAAA,eAAe,GAAGA,eAAe,CAACP,KAAK,CACrC,CAAC,EACDgE,IAAI,CAACC,GAAG,CAAC1D,eAAe,CAAClC,MAAM,EAAEuF,UAAU,GAAG,CAAC,CACjD,CAAC,CAAA;AACH,GAAA;;AAEA;AACA;EACA,IAAIM,cAAc,GAAG,KAAK,CAAA;EAC1B,IAAIC,aAAa,GAAG,CAAC,CAAC,CAAA;AACtB,EAAA,IAAIvF,eAAe,IAAIrC,MAAM,IAAIA,MAAM,CAAC6H,mBAAmB,EAAE;AAC3D,IAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9D,eAAe,CAAClC,MAAM,EAAEgG,CAAC,EAAE,EAAE;AAC/C,MAAA,IAAI3D,KAAK,GAAGH,eAAe,CAAC8D,CAAC,CAAC,CAAA;AAC9B;MACA,IAAI3D,KAAK,CAACvB,KAAK,CAACmF,eAAe,IAAI5D,KAAK,CAACvB,KAAK,CAACoF,sBAAsB,EAAE;AACrEJ,QAAAA,aAAa,GAAGE,CAAC,CAAA;AACnB,OAAA;AAEA,MAAA,IAAI3D,KAAK,CAACvB,KAAK,CAACqE,EAAE,EAAE;QAClB,IAAI;UAAEgB,UAAU;AAAEb,UAAAA,MAAAA;AAAO,SAAC,GAAG/E,eAAe,CAAA;AAC5C,QAAA,IAAI6F,gBAAgB,GAClB/D,KAAK,CAACvB,KAAK,CAACuF,MAAM,IAClBF,UAAU,CAAC9D,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,KAAKpD,SAAS,KACvC,CAACuD,MAAM,IAAIA,MAAM,CAACjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,KAAKpD,SAAS,CAAC,CAAA;AACnD,QAAA,IAAIM,KAAK,CAACvB,KAAK,CAACmB,IAAI,IAAImE,gBAAgB,EAAE;AACxC;AACA;AACA;AACAP,UAAAA,cAAc,GAAG,IAAI,CAAA;UACrB,IAAIC,aAAa,IAAI,CAAC,EAAE;YACtB5D,eAAe,GAAGA,eAAe,CAACP,KAAK,CAAC,CAAC,EAAEmE,aAAa,GAAG,CAAC,CAAC,CAAA;AAC/D,WAAC,MAAM;AACL5D,YAAAA,eAAe,GAAG,CAACA,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;AACxC,WAAA;AACA,UAAA,MAAA;AACF,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;EAEA,OAAOA,eAAe,CAACoE,WAAW,CAAC,CAAC1K,MAAM,EAAEyG,KAAK,EAAEkE,KAAK,KAAK;AAC3D;AACA,IAAA,IAAIzD,KAAU,CAAA;IACd,IAAI0D,2BAA2B,GAAG,KAAK,CAAA;IACvC,IAAIxB,YAAoC,GAAG,IAAI,CAAA;IAC/C,IAAIkB,sBAA8C,GAAG,IAAI,CAAA;AACzD,IAAA,IAAI3F,eAAe,EAAE;AACnBuC,MAAAA,KAAK,GAAGwC,MAAM,IAAIjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,GAAGG,MAAM,CAACjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,GAAGpD,SAAS,CAAA;AACrEiD,MAAAA,YAAY,GAAG3C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAIhB,mBAAmB,CAAA;AAE9D,MAAA,IAAI6B,cAAc,EAAE;AAClB,QAAA,IAAIC,aAAa,GAAG,CAAC,IAAIS,KAAK,KAAK,CAAC,EAAE;AACpCvF,UAAAA,WAAW,CACT,gBAAgB,EAChB,KAAK,EACL,0EACF,CAAC,CAAA;AACDwF,UAAAA,2BAA2B,GAAG,IAAI,CAAA;AAClCN,UAAAA,sBAAsB,GAAG,IAAI,CAAA;AAC/B,SAAC,MAAM,IAAIJ,aAAa,KAAKS,KAAK,EAAE;AAClCC,UAAAA,2BAA2B,GAAG,IAAI,CAAA;AAClCN,UAAAA,sBAAsB,GAAG7D,KAAK,CAACvB,KAAK,CAACoF,sBAAsB,IAAI,IAAI,CAAA;AACrE,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,IAAIrK,OAAO,GAAG2E,aAAa,CAACiG,MAAM,CAACvE,eAAe,CAACP,KAAK,CAAC,CAAC,EAAE4E,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;IACvE,IAAIG,WAAW,GAAGA,MAAM;AACtB,MAAA,IAAI/B,QAAyB,CAAA;AAC7B,MAAA,IAAI7B,KAAK,EAAE;AACT6B,QAAAA,QAAQ,GAAGK,YAAY,CAAA;OACxB,MAAM,IAAIwB,2BAA2B,EAAE;AACtC7B,QAAAA,QAAQ,GAAGuB,sBAAsB,CAAA;AACnC,OAAC,MAAM,IAAI7D,KAAK,CAACvB,KAAK,CAACkB,SAAS,EAAE;AAChC;AACA;AACA;AACA;AACA;AACA;QACA2C,QAAQ,gBAAG1J,KAAA,CAAA0E,aAAA,CAAC0C,KAAK,CAACvB,KAAK,CAACkB,SAAS,EAAA,IAAE,CAAC,CAAA;AACtC,OAAC,MAAM,IAAIK,KAAK,CAACvB,KAAK,CAACgB,OAAO,EAAE;AAC9B6C,QAAAA,QAAQ,GAAGtC,KAAK,CAACvB,KAAK,CAACgB,OAAO,CAAA;AAChC,OAAC,MAAM;AACL6C,QAAAA,QAAQ,GAAG/I,MAAM,CAAA;AACnB,OAAA;AACA,MAAA,oBACEX,KAAA,CAAA0E,aAAA,CAACkF,aAAa,EAAA;AACZxC,QAAAA,KAAK,EAAEA,KAAM;AACbqC,QAAAA,YAAY,EAAE;UACZ9I,MAAM;UACNC,OAAO;UACPC,WAAW,EAAEyE,eAAe,IAAI,IAAA;SAChC;AACFoE,QAAAA,QAAQ,EAAEA,QAAAA;AAAS,OACpB,CAAC,CAAA;KAEL,CAAA;AACD;AACA;AACA;IACA,OAAOpE,eAAe,KACnB8B,KAAK,CAACvB,KAAK,CAACmE,aAAa,IAAI5C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAIuB,KAAK,KAAK,CAAC,CAAC,gBACtEtL,KAAA,CAAA0E,aAAA,CAACsE,mBAAmB,EAAA;MAClBhH,QAAQ,EAAEsD,eAAe,CAACtD,QAAS;MACnCmH,YAAY,EAAE7D,eAAe,CAAC6D,YAAa;AAC3CQ,MAAAA,SAAS,EAAEI,YAAa;AACxBlC,MAAAA,KAAK,EAAEA,KAAM;MACb6B,QAAQ,EAAE+B,WAAW,EAAG;AACxBhC,MAAAA,YAAY,EAAE;AAAE9I,QAAAA,MAAM,EAAE,IAAI;QAAEC,OAAO;AAAEC,QAAAA,WAAW,EAAE,IAAA;AAAK,OAAA;AAAE,KAC5D,CAAC,GAEF4K,WAAW,EACZ,CAAA;GACF,EAAE,IAAiC,CAAC,CAAA;AACvC,CAAA;AAAC,IAEIC,cAAc,0BAAdA,cAAc,EAAA;EAAdA,cAAc,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;EAAdA,cAAc,CAAA,gBAAA,CAAA,GAAA,gBAAA,CAAA;EAAdA,cAAc,CAAA,mBAAA,CAAA,GAAA,aAAA,CAAA;AAAA,EAAA,OAAdA,cAAc,CAAA;AAAA,CAAA,CAAdA,cAAc,IAAA,EAAA,CAAA,CAAA;AAAA,IAMdC,mBAAmB,0BAAnBA,mBAAmB,EAAA;EAAnBA,mBAAmB,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;EAAnBA,mBAAmB,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;EAAnBA,mBAAmB,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;EAAnBA,mBAAmB,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;EAAnBA,mBAAmB,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;EAAnBA,mBAAmB,CAAA,oBAAA,CAAA,GAAA,oBAAA,CAAA;EAAnBA,mBAAmB,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;EAAnBA,mBAAmB,CAAA,gBAAA,CAAA,GAAA,gBAAA,CAAA;EAAnBA,mBAAmB,CAAA,mBAAA,CAAA,GAAA,aAAA,CAAA;EAAnBA,mBAAmB,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;AAAA,EAAA,OAAnBA,mBAAmB,CAAA;AAAA,CAAA,CAAnBA,mBAAmB,IAAA,EAAA,CAAA,CAAA;AAaxB,SAASC,yBAAyBA,CAChCC,QAA8C,EAC9C;AACA,EAAA,OAAUA,QAAQ,GAAA,4FAAA,CAAA;AACpB,CAAA;AAEA,SAASC,oBAAoBA,CAACD,QAAwB,EAAE;AACtD,EAAA,IAAIE,GAAG,GAAG/L,KAAK,CAACuB,UAAU,CAACxB,iBAAiB,CAAC,CAAA;AAC7C,EAAA,CAAUgM,GAAG,GAAA7L,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAbgB,YAAAA,GAAAA,gBAAS,QAAMwK,yBAAyB,CAACC,QAAQ,CAAC,IAAlDzK,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,OAAO2K,GAAG,CAAA;AACZ,CAAA;AAEA,SAASC,kBAAkBA,CAACH,QAA6B,EAAE;AACzD,EAAA,IAAIxH,KAAK,GAAGrE,KAAK,CAACuB,UAAU,CAACjB,sBAAsB,CAAC,CAAA;AACpD,EAAA,CAAU+D,KAAK,GAAAnE,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAfgB,YAAAA,GAAAA,gBAAS,QAAQwK,yBAAyB,CAACC,QAAQ,CAAC,IAApDzK,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,OAAOiD,KAAK,CAAA;AACd,CAAA;AAEA,SAAS4H,eAAeA,CAACJ,QAA6B,EAAE;AACtD,EAAA,IAAIhG,KAAK,GAAG7F,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC,CAAA;AAC1C,EAAA,CAAUmF,KAAK,GAAA3F,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAfgB,YAAAA,GAAAA,gBAAS,QAAQwK,yBAAyB,CAACC,QAAQ,CAAC,IAApDzK,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,OAAOyE,KAAK,CAAA;AACd,CAAA;;AAEA;AACA,SAASqG,iBAAiBA,CAACL,QAA6B,EAAE;AACxD,EAAA,IAAIhG,KAAK,GAAGoG,eAAe,CAACJ,QAAQ,CAAC,CAAA;AACrC,EAAA,IAAIM,SAAS,GAAGtG,KAAK,CAACjF,OAAO,CAACiF,KAAK,CAACjF,OAAO,CAACmE,MAAM,GAAG,CAAC,CAAC,CAAA;AACvD,EAAA,CACEoH,SAAS,CAACtG,KAAK,CAACqE,EAAE,GAAAhK,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADpBgB,YAAAA,GAAAA,gBAAS,CAEJyK,KAAAA,EAAAA,QAAQ,iEAFbzK,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAIT,EAAA,OAAO+K,SAAS,CAACtG,KAAK,CAACqE,EAAE,CAAA;AAC3B,CAAA;;AAEA;AACA;AACA;AACO,SAASkC,UAAUA,GAAG;AAC3B,EAAA,OAAOF,iBAAiB,CAACP,mBAAmB,CAACU,UAAU,CAAC,CAAA;AAC1D,CAAA;;AAEA;AACA;AACA;AACA;AACO,SAASC,aAAaA,GAAG;AAC9B,EAAA,IAAIjI,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAACY,aAAa,CAAC,CAAA;EACjE,OAAOlI,KAAK,CAACmI,UAAU,CAAA;AACzB,CAAA;;AAEA;AACA;AACA;AACA;AACO,SAASC,cAAcA,GAAG;AAC/B,EAAA,IAAIzJ,iBAAiB,GAAG8I,oBAAoB,CAACJ,cAAc,CAACgB,cAAc,CAAC,CAAA;AAC3E,EAAA,IAAIrI,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAACe,cAAc,CAAC,CAAA;AAClE,EAAA,OAAO1M,KAAK,CAACqC,OAAO,CAClB,OAAO;AACLsK,IAAAA,UAAU,EAAE3J,iBAAiB,CAAC4J,MAAM,CAACD,UAAU;IAC/CtI,KAAK,EAAEA,KAAK,CAAC8E,YAAAA;AACf,GAAC,CAAC,EACF,CAACnG,iBAAiB,CAAC4J,MAAM,CAACD,UAAU,EAAEtI,KAAK,CAAC8E,YAAY,CAC1D,CAAC,CAAA;AACH,CAAA;;AAEA;AACA;AACA;AACA;AACO,SAAS0D,UAAUA,GAAc;EACtC,IAAI;IAAEjM,OAAO;AAAEsK,IAAAA,UAAAA;AAAW,GAAC,GAAGc,kBAAkB,CAC9CL,mBAAmB,CAACmB,UACtB,CAAC,CAAA;EACD,OAAO9M,KAAK,CAACqC,OAAO,CAClB,MAAMzB,OAAO,CAACuG,GAAG,CAAEqD,CAAC,IAAKuC,iCAA0B,CAACvC,CAAC,EAAEU,UAAU,CAAC,CAAC,EACnE,CAACtK,OAAO,EAAEsK,UAAU,CACtB,CAAC,CAAA;AACH,CAAA;;AAEA;AACA;AACA;AACO,SAAS8B,aAAaA,GAAY;AACvC,EAAA,IAAI3I,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAACsB,aAAa,CAAC,CAAA;AACjE,EAAA,IAAIC,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAACsB,aAAa,CAAC,CAAA;AAElE,EAAA,IAAI5I,KAAK,CAACgG,MAAM,IAAIhG,KAAK,CAACgG,MAAM,CAAC6C,OAAO,CAAC,IAAI,IAAI,EAAE;AACjDvE,IAAAA,OAAO,CAACd,KAAK,CACkDqF,0DAAAA,GAAAA,OAAO,MACtE,CAAC,CAAA;AACD,IAAA,OAAOpG,SAAS,CAAA;AAClB,GAAA;AACA,EAAA,OAAOzC,KAAK,CAAC6G,UAAU,CAACgC,OAAO,CAAC,CAAA;AAClC,CAAA;;AAEA;AACA;AACA;AACO,SAASC,kBAAkBA,CAACD,OAAe,EAAW;AAC3D,EAAA,IAAI7I,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAACyB,kBAAkB,CAAC,CAAA;AACtE,EAAA,OAAO/I,KAAK,CAAC6G,UAAU,CAACgC,OAAO,CAAC,CAAA;AAClC,CAAA;;AAEA;AACA;AACA;AACO,SAASG,aAAaA,GAAY;AACvC,EAAA,IAAIhJ,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAAC2B,aAAa,CAAC,CAAA;AACjE,EAAA,IAAIJ,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAACsB,aAAa,CAAC,CAAA;EAClE,OAAO5I,KAAK,CAACkJ,UAAU,GAAGlJ,KAAK,CAACkJ,UAAU,CAACL,OAAO,CAAC,GAAGpG,SAAS,CAAA;AACjE,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASgB,aAAaA,GAAY;AAAA,EAAA,IAAA0F,aAAA,CAAA;AACvC,EAAA,IAAI3F,KAAK,GAAG7H,KAAK,CAACuB,UAAU,CAACT,iBAAiB,CAAC,CAAA;AAC/C,EAAA,IAAIuD,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAAC8B,aAAa,CAAC,CAAA;AACjE,EAAA,IAAIP,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAAC8B,aAAa,CAAC,CAAA;;AAElE;AACA;EACA,IAAI5F,KAAK,KAAKf,SAAS,EAAE;AACvB,IAAA,OAAOe,KAAK,CAAA;AACd,GAAA;;AAEA;EACA,OAAA2F,CAAAA,aAAA,GAAOnJ,KAAK,CAACgG,MAAM,KAAZmD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,aAAA,CAAeN,OAAO,CAAC,CAAA;AAChC,CAAA;;AAEA;AACA;AACA;AACO,SAASQ,aAAaA,GAAY;AACvC,EAAA,IAAI9I,KAAK,GAAG5E,KAAK,CAACuB,UAAU,CAAChB,YAAY,CAAC,CAAA;AAC1C,EAAA,OAAOqE,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAE+I,KAAK,CAAA;AACrB,CAAA;;AAEA;AACA;AACA;AACO,SAASC,aAAaA,GAAY;AACvC,EAAA,IAAIhJ,KAAK,GAAG5E,KAAK,CAACuB,UAAU,CAAChB,YAAY,CAAC,CAAA;AAC1C,EAAA,OAAOqE,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAEiJ,MAAM,CAAA;AACtB,CAAA;AAEA,IAAIC,SAAS,GAAG,CAAC,CAAA;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,UAAUA,CAACC,WAAsC,EAAW;EAC1E,IAAI;IAAEpB,MAAM;AAAEvL,IAAAA,QAAAA;AAAS,GAAC,GAAGyK,oBAAoB,CAACJ,cAAc,CAACuC,UAAU,CAAC,CAAA;AAC1E,EAAA,IAAI5J,KAAK,GAAG2H,kBAAkB,CAACL,mBAAmB,CAACsC,UAAU,CAAC,CAAA;EAE9D,IAAI,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnO,KAAK,CAACoO,QAAQ,CAAC,EAAE,CAAC,CAAA;AACpD,EAAA,IAAIC,eAAe,GAAGrO,KAAK,CAAC4D,WAAW,CACpC0K,GAAG,IAAK;AACP,IAAA,IAAI,OAAON,WAAW,KAAK,UAAU,EAAE;MACrC,OAAO,CAAC,CAACA,WAAW,CAAA;AACtB,KAAA;IACA,IAAI3M,QAAQ,KAAK,GAAG,EAAE;MACpB,OAAO2M,WAAW,CAACM,GAAG,CAAC,CAAA;AACzB,KAAA;;AAEA;AACA;AACA;IACA,IAAI;MAAEC,eAAe;MAAEC,YAAY;AAAEC,MAAAA,aAAAA;AAAc,KAAC,GAAGH,GAAG,CAAA;AAC1D,IAAA,OAAON,WAAW,CAAC;MACjBO,eAAe,EAAA/G,QAAA,CAAA,EAAA,EACV+G,eAAe,EAAA;QAClB9M,QAAQ,EACNiN,aAAa,CAACH,eAAe,CAAC9M,QAAQ,EAAEJ,QAAQ,CAAC,IACjDkN,eAAe,CAAC9M,QAAAA;OACnB,CAAA;MACD+M,YAAY,EAAAhH,QAAA,CAAA,EAAA,EACPgH,YAAY,EAAA;QACf/M,QAAQ,EACNiN,aAAa,CAACF,YAAY,CAAC/M,QAAQ,EAAEJ,QAAQ,CAAC,IAC9CmN,YAAY,CAAC/M,QAAAA;OAChB,CAAA;AACDgN,MAAAA,aAAAA;AACF,KAAC,CAAC,CAAA;AACJ,GAAC,EACD,CAACpN,QAAQ,EAAE2M,WAAW,CACxB,CAAC,CAAA;;AAED;AACA;EACAhO,KAAK,CAAC2O,SAAS,CAAC,MAAM;AACpB,IAAA,IAAIlH,GAAG,GAAGmH,MAAM,CAAC,EAAEd,SAAS,CAAC,CAAA;IAC7BK,aAAa,CAAC1G,GAAG,CAAC,CAAA;AAClB,IAAA,OAAO,MAAMmF,MAAM,CAACiC,aAAa,CAACpH,GAAG,CAAC,CAAA;AACxC,GAAC,EAAE,CAACmF,MAAM,CAAC,CAAC,CAAA;;AAEZ;AACA;AACA;AACA;EACA5M,KAAK,CAAC2O,SAAS,CAAC,MAAM;IACpB,IAAIT,UAAU,KAAK,EAAE,EAAE;AACrBtB,MAAAA,MAAM,CAACkC,UAAU,CAACZ,UAAU,EAAEG,eAAe,CAAC,CAAA;AAChD,KAAA;GACD,EAAE,CAACzB,MAAM,EAAEsB,UAAU,EAAEG,eAAe,CAAC,CAAC,CAAA;;AAEzC;AACA;EACA,OAAOH,UAAU,IAAI7J,KAAK,CAAC0K,QAAQ,CAACC,GAAG,CAACd,UAAU,CAAC,GAC/C7J,KAAK,CAAC0K,QAAQ,CAACE,GAAG,CAACf,UAAU,CAAC,GAC9BgB,YAAY,CAAA;AAClB,CAAA;;AAEA;AACA;AACA;AACA;AACA,SAASpM,iBAAiBA,GAAqB;EAC7C,IAAI;AAAE8J,IAAAA,MAAAA;AAAO,GAAC,GAAGd,oBAAoB,CAACJ,cAAc,CAACyD,iBAAiB,CAAC,CAAA;AACvE,EAAA,IAAIjF,EAAE,GAAGgC,iBAAiB,CAACP,mBAAmB,CAACwD,iBAAiB,CAAC,CAAA;AAEjE,EAAA,IAAI3L,SAAS,GAAGxD,KAAK,CAACyD,MAAM,CAAC,KAAK,CAAC,CAAA;AACnCjB,EAAAA,yBAAyB,CAAC,MAAM;IAC9BgB,SAAS,CAACE,OAAO,GAAG,IAAI,CAAA;AAC1B,GAAC,CAAC,CAAA;EAEF,IAAIC,QAA0B,GAAG3D,KAAK,CAAC4D,WAAW,CAChD,UAAC5C,EAAe,EAAE6C,OAAwB,EAAU;AAAA,IAAA,IAAlCA,OAAwB,KAAA,KAAA,CAAA,EAAA;MAAxBA,OAAwB,GAAG,EAAE,CAAA;AAAA,KAAA;AAC7C3D,IAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA0D,YAAAA,GAAAA,cAAO,CAACN,SAAS,CAACE,OAAO,EAAEnB,qBAAqB,CAAC,GAAA,KAAA,CAAA,CAAA;;AAEjD;AACA;AACA,IAAA,IAAI,CAACiB,SAAS,CAACE,OAAO,EAAE,OAAA;AAExB,IAAA,IAAI,OAAO1C,EAAE,KAAK,QAAQ,EAAE;AAC1B4L,MAAAA,MAAM,CAACjJ,QAAQ,CAAC3C,EAAE,CAAC,CAAA;AACrB,KAAC,MAAM;AACL4L,MAAAA,MAAM,CAACjJ,QAAQ,CAAC3C,EAAE,EAAAwG,QAAA,CAAA;AAAI4H,QAAAA,WAAW,EAAElF,EAAAA;OAAOrG,EAAAA,OAAO,CAAE,CAAC,CAAA;AACtD,KAAA;AACF,GAAC,EACD,CAAC+I,MAAM,EAAE1C,EAAE,CACb,CAAC,CAAA;AAED,EAAA,OAAOvG,QAAQ,CAAA;AACjB,CAAA;AAEA,MAAM0L,aAAsC,GAAG,EAAE,CAAA;AAEjD,SAAStJ,WAAWA,CAAC0B,GAAW,EAAE6H,IAAa,EAAEvH,OAAe,EAAE;EAChE,IAAI,CAACuH,IAAI,IAAI,CAACD,aAAa,CAAC5H,GAAG,CAAC,EAAE;AAChC4H,IAAAA,aAAa,CAAC5H,GAAG,CAAC,GAAG,IAAI,CAAA;IACzBvH,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GAAA0D,cAAO,CAAC,KAAK,EAAEiE,OAAO,CAAC,GAAA,KAAA,CAAA,CAAA;AACzB,GAAA;AACF;;AC9gCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwH,gBAAgB,GAAG,iBAAiB,CAAA;AAC1C,MAAMC,mBAAmB,GAAGxP,KAAK,CAACuP,gBAAgB,CAAC,CAAA;;AAEnD;AACA;AACA;AACO,SAASE,cAAcA,CAAA5F,IAAA,EAIc;EAAA,IAJb;IAC7B6F,eAAe;IACf9C,MAAM;AACN3J,IAAAA,MAAAA;AACmB,GAAC,GAAA4G,IAAA,CAAA;AACpB,EAAA,IAAI,CAACxF,KAAK,EAAEsL,YAAY,CAAC,GAAG3P,KAAK,CAACoO,QAAQ,CAACxB,MAAM,CAACvI,KAAK,CAAC,CAAA;EACxD,IAAI;AAAEuL,IAAAA,kBAAAA;AAAmB,GAAC,GAAG3M,MAAM,IAAI,EAAE,CAAA;AAEzC,EAAA,IAAI4M,QAAQ,GAAG7P,KAAK,CAAC4D,WAAW,CAC7BkM,QAAqB,IAAK;IACzB,IAAIF,kBAAkB,IAAIJ,mBAAmB,EAAE;AAC7CA,MAAAA,mBAAmB,CAAC,MAAMG,YAAY,CAACG,QAAQ,CAAC,CAAC,CAAA;AACnD,KAAC,MAAM;MACLH,YAAY,CAACG,QAAQ,CAAC,CAAA;AACxB,KAAA;AACF,GAAC,EACD,CAACH,YAAY,EAAEC,kBAAkB,CACnC,CAAC,CAAA;;AAED;AACA;AACA5P,EAAAA,KAAK,CAAC4C,eAAe,CAAC,MAAMgK,MAAM,CAACmD,SAAS,CAACF,QAAQ,CAAC,EAAE,CAACjD,MAAM,EAAEiD,QAAQ,CAAC,CAAC,CAAA;EAE3E7P,KAAK,CAAC2O,SAAS,CAAC,MAAM;IACpBzO,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACL4L,eAAe,IAAI,IAAI,IAAI,CAAC9C,MAAM,CAAC3J,MAAM,CAAC6H,mBAAmB,EAC7D,8DAA8D,GAC5D,kEACJ,CAAC,GAAA,KAAA,CAAA,CAAA;AACD;AACA;GACD,EAAE,EAAE,CAAC,CAAA;AAEN,EAAA,IAAIxJ,SAAS,GAAGtB,KAAK,CAACqC,OAAO,CAAC,MAAiB;IAC7C,OAAO;MACLP,UAAU,EAAE8K,MAAM,CAAC9K,UAAU;MAC7ByF,cAAc,EAAEqF,MAAM,CAACrF,cAAc;MACrCxD,EAAE,EAAGiM,CAAC,IAAKpD,MAAM,CAACjJ,QAAQ,CAACqM,CAAC,CAAC;AAC7B5L,MAAAA,IAAI,EAAEA,CAACpD,EAAE,EAAEqD,KAAK,EAAE4L,IAAI,KACpBrD,MAAM,CAACjJ,QAAQ,CAAC3C,EAAE,EAAE;QAClBqD,KAAK;AACL6L,QAAAA,kBAAkB,EAAED,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEC,kBAAAA;AAC5B,OAAC,CAAC;AACJ/L,MAAAA,OAAO,EAAEA,CAACnD,EAAE,EAAEqD,KAAK,EAAE4L,IAAI,KACvBrD,MAAM,CAACjJ,QAAQ,CAAC3C,EAAE,EAAE;AAClBmD,QAAAA,OAAO,EAAE,IAAI;QACbE,KAAK;AACL6L,QAAAA,kBAAkB,EAAED,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEC,kBAAAA;OAC3B,CAAA;KACJ,CAAA;AACH,GAAC,EAAE,CAACtD,MAAM,CAAC,CAAC,CAAA;AAEZ,EAAA,IAAIvL,QAAQ,GAAGuL,MAAM,CAACvL,QAAQ,IAAI,GAAG,CAAA;AAErC,EAAA,IAAI2B,iBAAiB,GAAGhD,KAAK,CAACqC,OAAO,CACnC,OAAO;IACLuK,MAAM;IACNtL,SAAS;AACTqB,IAAAA,MAAM,EAAE,KAAK;AACbtB,IAAAA,QAAAA;GACD,CAAC,EACF,CAACuL,MAAM,EAAEtL,SAAS,EAAED,QAAQ,CAC9B,CAAC,CAAA;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,oBACErB,KAAA,CAAA0E,aAAA,CAAA1E,KAAA,CAAA4I,QAAA,EACE5I,IAAAA,eAAAA,KAAA,CAAA0E,aAAA,CAAC3E,iBAAiB,CAAC4E,QAAQ,EAAA;AAACC,IAAAA,KAAK,EAAE5B,iBAAAA;AAAkB,GAAA,eACnDhD,KAAA,CAAA0E,aAAA,CAACpE,sBAAsB,CAACqE,QAAQ,EAAA;AAACC,IAAAA,KAAK,EAAEP,KAAAA;AAAM,GAAA,eAC5CrE,KAAA,CAAA0E,aAAA,CAACyL,MAAM,EAAA;AACL9O,IAAAA,QAAQ,EAAEA,QAAS;IACnBW,QAAQ,EAAEqC,KAAK,CAACrC,QAAS;IACzBE,cAAc,EAAEmC,KAAK,CAACoK,aAAc;AACpCnN,IAAAA,SAAS,EAAEA,SAAU;AACrB2B,IAAAA,MAAM,EAAE;AACNM,MAAAA,oBAAoB,EAAEqJ,MAAM,CAAC3J,MAAM,CAACM,oBAAAA;AACtC,KAAA;AAAE,GAAA,EAEDc,KAAK,CAAC+L,WAAW,IAAIxD,MAAM,CAAC3J,MAAM,CAAC6H,mBAAmB,gBACrD9K,KAAA,CAAA0E,aAAA,CAAC2L,UAAU,EAAA;IACTlL,MAAM,EAAEyH,MAAM,CAACzH,MAAO;IACtBlC,MAAM,EAAE2J,MAAM,CAAC3J,MAAO;AACtBoB,IAAAA,KAAK,EAAEA,KAAAA;GACR,CAAC,GAEFqL,eAEI,CACuB,CACP,CAAC,EAC5B,IACD,CAAC,CAAA;AAEP,CAAA;AAEA,SAASW,UAAUA,CAAAC,KAAA,EAQW;EAAA,IARV;IAClBnL,MAAM;IACNlC,MAAM;AACNoB,IAAAA,KAAAA;AAKF,GAAC,GAAAiM,KAAA,CAAA;EACC,OAAOjL,aAAa,CAACF,MAAM,EAAE2B,SAAS,EAAEzC,KAAK,EAAEpB,MAAM,CAAC,CAAA;AACxD,CAAA;AAUA;AACA;AACA;AACA;AACA;AACO,SAASsN,YAAYA,CAAAC,KAAA,EAMc;EAAA,IANb;IAC3BnP,QAAQ;IACRqI,QAAQ;IACR+G,cAAc;IACdC,YAAY;AACZzN,IAAAA,MAAAA;AACiB,GAAC,GAAAuN,KAAA,CAAA;AAClB,EAAA,IAAIG,UAAU,GAAG3Q,KAAK,CAACyD,MAAM,EAAiB,CAAA;AAC9C,EAAA,IAAIkN,UAAU,CAACjN,OAAO,IAAI,IAAI,EAAE;AAC9BiN,IAAAA,UAAU,CAACjN,OAAO,GAAGkN,mBAAmB,CAAC;MACvCH,cAAc;MACdC,YAAY;AACZG,MAAAA,QAAQ,EAAE,IAAA;AACZ,KAAC,CAAC,CAAA;AACJ,GAAA;AAEA,EAAA,IAAIC,OAAO,GAAGH,UAAU,CAACjN,OAAO,CAAA;EAChC,IAAI,CAACW,KAAK,EAAEsL,YAAY,CAAC,GAAG3P,KAAK,CAACoO,QAAQ,CAAC;IACzC2C,MAAM,EAAED,OAAO,CAACC,MAAM;IACtB/O,QAAQ,EAAE8O,OAAO,CAAC9O,QAAAA;AACpB,GAAC,CAAC,CAAA;EACF,IAAI;AAAE4N,IAAAA,kBAAAA;AAAmB,GAAC,GAAG3M,MAAM,IAAI,EAAE,CAAA;AACzC,EAAA,IAAI4M,QAAQ,GAAG7P,KAAK,CAAC4D,WAAW,CAC7BkM,QAAwD,IAAK;AAC5DF,IAAAA,kBAAkB,IAAIJ,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMG,YAAY,CAACG,QAAQ,CAAC,CAAC,GACjDH,YAAY,CAACG,QAAQ,CAAC,CAAA;AAC5B,GAAC,EACD,CAACH,YAAY,EAAEC,kBAAkB,CACnC,CAAC,CAAA;AAED5P,EAAAA,KAAK,CAAC4C,eAAe,CAAC,MAAMkO,OAAO,CAACE,MAAM,CAACnB,QAAQ,CAAC,EAAE,CAACiB,OAAO,EAAEjB,QAAQ,CAAC,CAAC,CAAA;AAE1E,EAAA,oBACE7P,KAAA,CAAA0E,aAAA,CAACyL,MAAM,EAAA;AACL9O,IAAAA,QAAQ,EAAEA,QAAS;AACnBqI,IAAAA,QAAQ,EAAEA,QAAS;IACnB1H,QAAQ,EAAEqC,KAAK,CAACrC,QAAS;IACzBE,cAAc,EAAEmC,KAAK,CAAC0M,MAAO;AAC7BzP,IAAAA,SAAS,EAAEwP,OAAQ;AACnB7N,IAAAA,MAAM,EAAEA,MAAAA;AAAO,GAChB,CAAC,CAAA;AAEN,CAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASgO,QAAQA,CAAAC,KAAA,EAKA;EAAA,IALC;IACvBlQ,EAAE;IACFmD,OAAO;IACPE,KAAK;AACLnD,IAAAA,QAAAA;AACa,GAAC,GAAAgQ,KAAA,CAAA;EACd,CACE/P,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtBgB,YAAAA,GAAAA,gBAAS,CAEP,KAAA;AACA;AAAA,EAAA,qEAAA,CAAA,GAHFA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;EAOT,IAAI;IAAE6B,MAAM;AAAEN,IAAAA,MAAM,EAAED,QAAAA;AAAS,GAAC,GAAG1C,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC,CAAA;AAEtEN,EAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA0D,YAAAA,GAAAA,cAAO,CACL,CAACpB,QAAQ,EACT,yEAC0E,GAAA,wEAAA,GAAA,0EAE5E,CAAC,GAAA,KAAA,CAAA,CAAA;EAED,IAAI;AAAE9B,IAAAA,OAAAA;AAAQ,GAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC,CAAA;EAChD,IAAI;AAAEe,IAAAA,QAAQ,EAAEyB,gBAAAA;GAAkB,GAAGnB,WAAW,EAAE,CAAA;AAClD,EAAA,IAAI4B,QAAQ,GAAGd,WAAW,EAAE,CAAA;;AAE5B;AACA;EACA,IAAImB,IAAI,GAAGC,SAAS,CAClBjD,EAAE,EACFsC,0BAAmB,CAAC1C,OAAO,EAAEqC,MAAM,CAACM,oBAAoB,CAAC,EACzDL,gBAAgB,EAChBhC,QAAQ,KAAK,MACf,CAAC,CAAA;AACD,EAAA,IAAIiQ,QAAQ,GAAG/N,IAAI,CAACC,SAAS,CAACW,IAAI,CAAC,CAAA;AAEnChE,EAAAA,KAAK,CAAC2O,SAAS,CACb,MAAMhL,QAAQ,CAACP,IAAI,CAACc,KAAK,CAACiN,QAAQ,CAAC,EAAE;IAAEhN,OAAO;IAAEE,KAAK;AAAEnD,IAAAA,QAAAA;AAAS,GAAC,CAAC,EAClE,CAACyC,QAAQ,EAAEwN,QAAQ,EAAEjQ,QAAQ,EAAEiD,OAAO,EAAEE,KAAK,CAC/C,CAAC,CAAA;AAED,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAMA;AACA;AACA;AACA;AACA;AACO,SAAS+M,MAAMA,CAAClI,KAAkB,EAA6B;AACpE,EAAA,OAAO1E,SAAS,CAAC0E,KAAK,CAACzE,OAAO,CAAC,CAAA;AACjC,CAAA;AA8CA;AACA;AACA;AACA;AACA;AACO,SAAS4M,KAAKA,CAACC,MAAkB,EAA6B;AACnE,EACOpR,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GADPgB,gBAAS,CAAA,KAAA,EAEP,sEACoE,GAAA,kEAAA,CAAA,GAHtEA,gBAAS,CAAA,KAAA,CAAA,CAAA,CAAA;AAKX,CAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS+O,MAAMA,CAAAoB,KAAA,EAQqB;EAAA,IARpB;IACrBlQ,QAAQ,EAAEmQ,YAAY,GAAG,GAAG;AAC5B9H,IAAAA,QAAQ,GAAG,IAAI;AACf1H,IAAAA,QAAQ,EAAEyP,YAAY;IACtBvP,cAAc,GAAGwF,MAAc,CAACC,GAAG;IACnCrG,SAAS;IACTqB,MAAM,EAAE+O,UAAU,GAAG,KAAK;AAC1BzO,IAAAA,MAAAA;AACW,GAAC,GAAAsO,KAAA,CAAA;AACZ,EAAA,CACE,CAACpQ,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADvBgB,YAAAA,GAAAA,gBAAS,CAEP,KAAA,EAAA,uDAAA,GAAA,mDACqD,IAHvDA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;;AAMT;AACA;EACA,IAAIC,QAAQ,GAAGmQ,YAAY,CAACrN,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;AAChD,EAAA,IAAIwN,iBAAiB,GAAG3R,KAAK,CAACqC,OAAO,CACnC,OAAO;IACLhB,QAAQ;IACRC,SAAS;AACTqB,IAAAA,MAAM,EAAE+O,UAAU;AAClBzO,IAAAA,MAAM,EAAAuE,QAAA,CAAA;AACJjE,MAAAA,oBAAoB,EAAE,KAAA;AAAK,KAAA,EACxBN,MAAM,CAAA;GAEZ,CAAC,EACF,CAAC5B,QAAQ,EAAE4B,MAAM,EAAE3B,SAAS,EAAEoQ,UAAU,CAC1C,CAAC,CAAA;AAED,EAAA,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;AACpCA,IAAAA,YAAY,GAAGrL,SAAS,CAACqL,YAAY,CAAC,CAAA;AACxC,GAAA;EAEA,IAAI;AACFhQ,IAAAA,QAAQ,GAAG,GAAG;AACdC,IAAAA,MAAM,GAAG,EAAE;AACXF,IAAAA,IAAI,GAAG,EAAE;AACT6C,IAAAA,KAAK,GAAG,IAAI;AACZoD,IAAAA,GAAG,GAAG,SAAA;AACR,GAAC,GAAGgK,YAAY,CAAA;AAEhB,EAAA,IAAIG,eAAe,GAAG5R,KAAK,CAACqC,OAAO,CAAC,MAAM;AACxC,IAAA,IAAIwP,gBAAgB,GAAGnD,aAAa,CAACjN,QAAQ,EAAEJ,QAAQ,CAAC,CAAA;IAExD,IAAIwQ,gBAAgB,IAAI,IAAI,EAAE;AAC5B,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;IAEA,OAAO;AACL7P,MAAAA,QAAQ,EAAE;AACRP,QAAAA,QAAQ,EAAEoQ,gBAAgB;QAC1BnQ,MAAM;QACNF,IAAI;QACJ6C,KAAK;AACLoD,QAAAA,GAAAA;OACD;AACDvF,MAAAA,cAAAA;KACD,CAAA;AACH,GAAC,EAAE,CAACb,QAAQ,EAAEI,QAAQ,EAAEC,MAAM,EAAEF,IAAI,EAAE6C,KAAK,EAAEoD,GAAG,EAAEvF,cAAc,CAAC,CAAC,CAAA;EAElEhC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA0D,cAAO,CACL8N,eAAe,IAAI,IAAI,EACvB,qBAAqBvQ,GAAAA,QAAQ,iDACvBI,QAAQ,GAAGC,MAAM,GAAGF,IAAI,GAAuC,wCAAA,CAAA,GAAA,kDAEvE,CAAC,GAAA,KAAA,CAAA,CAAA;EAED,IAAIoQ,eAAe,IAAI,IAAI,EAAE;AAC3B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA,EAAA,oBACE5R,KAAA,CAAA0E,aAAA,CAAClE,iBAAiB,CAACmE,QAAQ,EAAA;AAACC,IAAAA,KAAK,EAAE+M,iBAAAA;AAAkB,GAAA,eACnD3R,KAAA,CAAA0E,aAAA,CAACjE,eAAe,CAACkE,QAAQ,EAAA;AAAC+E,IAAAA,QAAQ,EAAEA,QAAS;AAAC9E,IAAAA,KAAK,EAAEgN,eAAAA;AAAgB,GAAE,CAC7C,CAAC,CAAA;AAEjC,CAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,MAAMA,CAAAC,KAAA,EAGqB;EAAA,IAHpB;IACrBrI,QAAQ;AACR1H,IAAAA,QAAAA;AACW,GAAC,GAAA+P,KAAA,CAAA;EACZ,OAAO7M,SAAS,CAAC8M,wBAAwB,CAACtI,QAAQ,CAAC,EAAE1H,QAAQ,CAAC,CAAA;AAChE,CAAA;AAYA;AACA;AACA;AACA;AACO,SAASiQ,KAAKA,CAAAC,KAAA,EAAkD;EAAA,IAAjD;IAAExI,QAAQ;IAAEK,YAAY;AAAEoI,IAAAA,OAAAA;AAAoB,GAAC,GAAAD,KAAA,CAAA;AACnE,EAAA,oBACElS,KAAA,CAAA0E,aAAA,CAAC0N,kBAAkB,EAAA;AAACD,IAAAA,OAAO,EAAEA,OAAQ;AAACpI,IAAAA,YAAY,EAAEA,YAAAA;GAClD/J,eAAAA,KAAA,CAAA0E,aAAA,CAAC2N,YAAY,EAAE3I,IAAAA,EAAAA,QAAuB,CACpB,CAAC,CAAA;AAEzB,CAAA;AAAC,IAWI4I,iBAAiB,0BAAjBA,iBAAiB,EAAA;AAAjBA,EAAAA,iBAAiB,CAAjBA,iBAAiB,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;AAAjBA,EAAAA,iBAAiB,CAAjBA,iBAAiB,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;AAAjBA,EAAAA,iBAAiB,CAAjBA,iBAAiB,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAA,CAAA;AAAA,EAAA,OAAjBA,iBAAiB,CAAA;AAAA,CAAA,CAAjBA,iBAAiB,IAAA,EAAA,CAAA,CAAA;AAMtB,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;AAEjD,MAAMJ,kBAAkB,SAASpS,KAAK,CAAC+G,SAAS,CAG9C;EACAkC,WAAWA,CAACC,KAA8B,EAAE;IAC1C,KAAK,CAACA,KAAK,CAAC,CAAA;IACZ,IAAI,CAAC7E,KAAK,GAAG;AAAEwD,MAAAA,KAAK,EAAE,IAAA;KAAM,CAAA;AAC9B,GAAA;EAEA,OAAOuB,wBAAwBA,CAACvB,KAAU,EAAE;IAC1C,OAAO;AAAEA,MAAAA,KAAAA;KAAO,CAAA;AAClB,GAAA;AAEAyB,EAAAA,iBAAiBA,CAACzB,KAAU,EAAE0B,SAAc,EAAE;IAC5CZ,OAAO,CAACd,KAAK,CACX,kDAAkD,EAClDA,KAAK,EACL0B,SACF,CAAC,CAAA;AACH,GAAA;AAEAC,EAAAA,MAAMA,GAAG;IACP,IAAI;MAAEE,QAAQ;MAAEK,YAAY;AAAEoI,MAAAA,OAAAA;KAAS,GAAG,IAAI,CAACjJ,KAAK,CAAA;IAEpD,IAAIuJ,OAA8B,GAAG,IAAI,CAAA;AACzC,IAAA,IAAIxK,MAAyB,GAAGqK,iBAAiB,CAACI,OAAO,CAAA;AAEzD,IAAA,IAAI,EAAEP,OAAO,YAAYK,OAAO,CAAC,EAAE;AACjC;MACAvK,MAAM,GAAGqK,iBAAiB,CAACK,OAAO,CAAA;AAClCF,MAAAA,OAAO,GAAGD,OAAO,CAACL,OAAO,EAAE,CAAA;AAC3B9K,MAAAA,MAAM,CAACuL,cAAc,CAACH,OAAO,EAAE,UAAU,EAAE;QAAExD,GAAG,EAAEA,MAAM,IAAA;AAAK,OAAC,CAAC,CAAA;AAC/D5H,MAAAA,MAAM,CAACuL,cAAc,CAACH,OAAO,EAAE,OAAO,EAAE;QAAExD,GAAG,EAAEA,MAAMkD,OAAAA;AAAQ,OAAC,CAAC,CAAA;AACjE,KAAC,MAAM,IAAI,IAAI,CAAC9N,KAAK,CAACwD,KAAK,EAAE;AAC3B;MACAI,MAAM,GAAGqK,iBAAiB,CAACzK,KAAK,CAAA;AAChC,MAAA,IAAIgL,WAAW,GAAG,IAAI,CAACxO,KAAK,CAACwD,KAAK,CAAA;AAClC4K,MAAAA,OAAO,GAAGD,OAAO,CAACM,MAAM,EAAE,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;AAC3C1L,MAAAA,MAAM,CAACuL,cAAc,CAACH,OAAO,EAAE,UAAU,EAAE;QAAExD,GAAG,EAAEA,MAAM,IAAA;AAAK,OAAC,CAAC,CAAA;AAC/D5H,MAAAA,MAAM,CAACuL,cAAc,CAACH,OAAO,EAAE,QAAQ,EAAE;QAAExD,GAAG,EAAEA,MAAM4D,WAAAA;AAAY,OAAC,CAAC,CAAA;AACtE,KAAC,MAAM,IAAKV,OAAO,CAAoBa,QAAQ,EAAE;AAC/C;AACAP,MAAAA,OAAO,GAAGN,OAAO,CAAA;MACjBlK,MAAM,GACJwK,OAAO,CAAC5E,MAAM,KAAK/G,SAAS,GACxBwL,iBAAiB,CAACzK,KAAK,GACvB4K,OAAO,CAAC9E,KAAK,KAAK7G,SAAS,GAC3BwL,iBAAiB,CAACK,OAAO,GACzBL,iBAAiB,CAACI,OAAO,CAAA;AACjC,KAAC,MAAM;AACL;MACAzK,MAAM,GAAGqK,iBAAiB,CAACI,OAAO,CAAA;AAClCrL,MAAAA,MAAM,CAACuL,cAAc,CAACT,OAAO,EAAE,UAAU,EAAE;QAAElD,GAAG,EAAEA,MAAM,IAAA;AAAK,OAAC,CAAC,CAAA;AAC/DwD,MAAAA,OAAO,GAAGN,OAAO,CAACc,IAAI,CACnBC,IAAS,IACR7L,MAAM,CAACuL,cAAc,CAACT,OAAO,EAAE,OAAO,EAAE;QAAElD,GAAG,EAAEA,MAAMiE,IAAAA;OAAM,CAAC,EAC7DrL,KAAU,IACTR,MAAM,CAACuL,cAAc,CAACT,OAAO,EAAE,QAAQ,EAAE;QAAElD,GAAG,EAAEA,MAAMpH,KAAAA;AAAM,OAAC,CACjE,CAAC,CAAA;AACH,KAAA;IAEA,IACEI,MAAM,KAAKqK,iBAAiB,CAACzK,KAAK,IAClC4K,OAAO,CAAC5E,MAAM,YAAYsF,oBAAoB,EAC9C;AACA;AACA,MAAA,MAAMZ,mBAAmB,CAAA;AAC3B,KAAA;IAEA,IAAItK,MAAM,KAAKqK,iBAAiB,CAACzK,KAAK,IAAI,CAACkC,YAAY,EAAE;AACvD;MACA,MAAM0I,OAAO,CAAC5E,MAAM,CAAA;AACtB,KAAA;AAEA,IAAA,IAAI5F,MAAM,KAAKqK,iBAAiB,CAACzK,KAAK,EAAE;AACtC;AACA,MAAA,oBAAO7H,KAAA,CAAA0E,aAAA,CAACnE,YAAY,CAACoE,QAAQ,EAAA;AAACC,QAAAA,KAAK,EAAE6N,OAAQ;AAAC/I,QAAAA,QAAQ,EAAEK,YAAAA;AAAa,OAAE,CAAC,CAAA;AAC1E,KAAA;AAEA,IAAA,IAAI9B,MAAM,KAAKqK,iBAAiB,CAACK,OAAO,EAAE;AACxC;AACA,MAAA,oBAAO3S,KAAA,CAAA0E,aAAA,CAACnE,YAAY,CAACoE,QAAQ,EAAA;AAACC,QAAAA,KAAK,EAAE6N,OAAQ;AAAC/I,QAAAA,QAAQ,EAAEA,QAAAA;AAAS,OAAE,CAAC,CAAA;AACtE,KAAA;;AAEA;AACA,IAAA,MAAM+I,OAAO,CAAA;AACf,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACA;AACA,SAASJ,YAAYA,CAAAe,KAAA,EAIlB;EAAA,IAJmB;AACpB1J,IAAAA,QAAAA;AAGF,GAAC,GAAA0J,KAAA,CAAA;AACC,EAAA,IAAIF,IAAI,GAAGxF,aAAa,EAAE,CAAA;AAC1B,EAAA,IAAI2F,QAAQ,GAAG,OAAO3J,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACwJ,IAAI,CAAC,GAAGxJ,QAAQ,CAAA;EACzE,oBAAO1J,KAAA,CAAA0E,aAAA,CAAA1E,KAAA,CAAA4I,QAAA,EAAGyK,IAAAA,EAAAA,QAAW,CAAC,CAAA;AACxB,CAAA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASrB,wBAAwBA,CACtCtI,QAAyB,EACzB5D,UAAoB,EACL;AAAA,EAAA,IADfA,UAAoB,KAAA,KAAA,CAAA,EAAA;AAApBA,IAAAA,UAAoB,GAAG,EAAE,CAAA;AAAA,GAAA;EAEzB,IAAIX,MAAqB,GAAG,EAAE,CAAA;EAE9BnF,KAAK,CAACsT,QAAQ,CAACC,OAAO,CAAC7J,QAAQ,EAAE,CAAC7C,OAAO,EAAEyE,KAAK,KAAK;AACnD,IAAA,IAAI,eAACtL,KAAK,CAACwT,cAAc,CAAC3M,OAAO,CAAC,EAAE;AAClC;AACA;AACA,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAAI4M,QAAQ,GAAG,CAAC,GAAG3N,UAAU,EAAEwF,KAAK,CAAC,CAAA;AAErC,IAAA,IAAIzE,OAAO,CAAC6M,IAAI,KAAK1T,KAAK,CAAC4I,QAAQ,EAAE;AACnC;AACAzD,MAAAA,MAAM,CAACf,IAAI,CAACuP,KAAK,CACfxO,MAAM,EACN6M,wBAAwB,CAACnL,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EAAE+J,QAAQ,CAC3D,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,EACE5M,OAAO,CAAC6M,IAAI,KAAKrC,KAAK,CAAA,GAAAnR,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GADxBgB,gBAAS,CAGL,KAAA,EAAA,GAAA,IAAA,OAAOyF,OAAO,CAAC6M,IAAI,KAAK,QAAQ,GAAG7M,OAAO,CAAC6M,IAAI,GAAG7M,OAAO,CAAC6M,IAAI,CAACE,IAAI,gHAHvExS,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;IAOT,EACE,CAACyF,OAAO,CAACqC,KAAK,CAACoC,KAAK,IAAI,CAACzE,OAAO,CAACqC,KAAK,CAACQ,QAAQ,CAAAxJ,GAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADjDgB,gBAAS,CAAA,KAAA,EAEP,0CAA0C,CAAA,GAF5CA,gBAAS,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAKT,IAAA,IAAIyE,KAAkB,GAAG;AACvBqE,MAAAA,EAAE,EAAErD,OAAO,CAACqC,KAAK,CAACgB,EAAE,IAAIuJ,QAAQ,CAAC9M,IAAI,CAAC,GAAG,CAAC;AAC1CkN,MAAAA,aAAa,EAAEhN,OAAO,CAACqC,KAAK,CAAC2K,aAAa;AAC1ChN,MAAAA,OAAO,EAAEA,OAAO,CAACqC,KAAK,CAACrC,OAAO;AAC9BE,MAAAA,SAAS,EAAEF,OAAO,CAACqC,KAAK,CAACnC,SAAS;AAClCuE,MAAAA,KAAK,EAAEzE,OAAO,CAACqC,KAAK,CAACoC,KAAK;AAC1BtH,MAAAA,IAAI,EAAE6C,OAAO,CAACqC,KAAK,CAAClF,IAAI;AACxBoH,MAAAA,MAAM,EAAEvE,OAAO,CAACqC,KAAK,CAACkC,MAAM;AAC5B2F,MAAAA,MAAM,EAAElK,OAAO,CAACqC,KAAK,CAAC6H,MAAM;AAC5BhH,MAAAA,YAAY,EAAElD,OAAO,CAACqC,KAAK,CAACa,YAAY;AACxCC,MAAAA,aAAa,EAAEnD,OAAO,CAACqC,KAAK,CAACc,aAAa;AAC1C8J,MAAAA,gBAAgB,EACdjN,OAAO,CAACqC,KAAK,CAACc,aAAa,IAAI,IAAI,IACnCnD,OAAO,CAACqC,KAAK,CAACa,YAAY,IAAI,IAAI;AACpCgK,MAAAA,gBAAgB,EAAElN,OAAO,CAACqC,KAAK,CAAC6K,gBAAgB;AAChDC,MAAAA,MAAM,EAAEnN,OAAO,CAACqC,KAAK,CAAC8K,MAAM;AAC5BhN,MAAAA,IAAI,EAAEH,OAAO,CAACqC,KAAK,CAAClC,IAAAA;KACrB,CAAA;AAED,IAAA,IAAIH,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EAAE;AAC1B7D,MAAAA,KAAK,CAAC6D,QAAQ,GAAGsI,wBAAwB,CACvCnL,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EACtB+J,QACF,CAAC,CAAA;AACH,KAAA;AAEAtO,IAAAA,MAAM,CAACf,IAAI,CAACyB,KAAK,CAAC,CAAA;AACpB,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOV,MAAM,CAAA;AACf,CAAA;;AAEA;AACA;AACA;AACO,SAAS8O,aAAaA,CAC3BrT,OAA4B,EACD;EAC3B,OAAOsG,cAAc,CAACtG,OAAO,CAAC,CAAA;AAChC;;ACjgBA,SAASsT,kBAAkBA,CAACrO,KAAkB,EAAE;AAC9C,EAAA,IAAIsO,OAA6D,GAAG;AAClE;AACA;IACAL,gBAAgB,EAAEjO,KAAK,CAACmE,aAAa,IAAI,IAAI,IAAInE,KAAK,CAACkE,YAAY,IAAI,IAAA;GACxE,CAAA;EAED,IAAIlE,KAAK,CAACkB,SAAS,EAAE;AACnB,IAAA,IAAA7G,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;MACX,IAAIyF,KAAK,CAACgB,OAAO,EAAE;AACjB3G,QAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA0D,YAAAA,GAAAA,cAAO,CACL,KAAK,EACL,wEAAwE,GACtE,2BACJ,CAAC,GAAA,KAAA,CAAA,CAAA;AACH,OAAA;AACF,KAAA;AACAuD,IAAAA,MAAM,CAACC,MAAM,CAAC6M,OAAO,EAAE;MACrBtN,OAAO,eAAE7G,KAAK,CAAC0E,aAAa,CAACmB,KAAK,CAACkB,SAAS,CAAC;AAC7CA,MAAAA,SAAS,EAAED,SAAAA;AACb,KAAC,CAAC,CAAA;AACJ,GAAA;EAEA,IAAIjB,KAAK,CAACmF,eAAe,EAAE;AACzB,IAAA,IAAA9K,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;MACX,IAAIyF,KAAK,CAACoF,sBAAsB,EAAE;AAChC/K,QAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA0D,YAAAA,GAAAA,cAAO,CACL,KAAK,EACL,6FAA6F,GAC3F,iCACJ,CAAC,GAAA,KAAA,CAAA,CAAA;AACH,OAAA;AACF,KAAA;AACAuD,IAAAA,MAAM,CAACC,MAAM,CAAC6M,OAAO,EAAE;MACrBlJ,sBAAsB,eAAEjL,KAAK,CAAC0E,aAAa,CAACmB,KAAK,CAACmF,eAAe,CAAC;AAClEA,MAAAA,eAAe,EAAElE,SAAAA;AACnB,KAAC,CAAC,CAAA;AACJ,GAAA;EAEA,IAAIjB,KAAK,CAACmE,aAAa,EAAE;AACvB,IAAA,IAAA9J,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa,YAAA,EAAA;MACX,IAAIyF,KAAK,CAACkE,YAAY,EAAE;AACtB7J,QAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA0D,YAAAA,GAAAA,cAAO,CACL,KAAK,EACL,iFAAiF,GAC/E,+BACJ,CAAC,GAAA,KAAA,CAAA,CAAA;AACH,OAAA;AACF,KAAA;AACAuD,IAAAA,MAAM,CAACC,MAAM,CAAC6M,OAAO,EAAE;MACrBpK,YAAY,eAAE/J,KAAK,CAAC0E,aAAa,CAACmB,KAAK,CAACmE,aAAa,CAAC;AACtDA,MAAAA,aAAa,EAAElD,SAAAA;AACjB,KAAC,CAAC,CAAA;AACJ,GAAA;AAEA,EAAA,OAAOqN,OAAO,CAAA;AAChB,CAAA;AAEO,SAASC,kBAAkBA,CAChCjP,MAAqB,EACrB8K,IAMC,EACY;AACb,EAAA,OAAOoE,YAAY,CAAC;AAClBhT,IAAAA,QAAQ,EAAE4O,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAE5O,QAAQ;AACxB4B,IAAAA,MAAM,EAAAuE,QAAA,CAAA,EAAA,EACDyI,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEhN,MAAM,EAAA;AACfqR,MAAAA,kBAAkB,EAAE,IAAA;KACrB,CAAA;IACDxD,OAAO,EAAEF,mBAAmB,CAAC;AAC3BH,MAAAA,cAAc,EAAER,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEQ,cAAc;AACpCC,MAAAA,YAAY,EAAET,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAES,YAAAA;AACtB,KAAC,CAAC;AACF6D,IAAAA,aAAa,EAAEtE,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEsE,aAAa;IAClCpP,MAAM;AACN+O,IAAAA,kBAAAA;AACF,GAAC,CAAC,CAACM,UAAU,EAAE,CAAA;AACjB;;;;"}