{"name": "@emotion/sheet", "version": "1.2.2", "description": "emotion's stylesheet", "main": "dist/emotion-sheet.cjs.js", "module": "dist/emotion-sheet.esm.js", "browser": {"./dist/emotion-sheet.esm.js": "./dist/emotion-sheet.browser.esm.js"}, "exports": {".": {"module": {"browser": "./dist/emotion-sheet.browser.esm.js", "default": "./dist/emotion-sheet.esm.js"}, "import": "./dist/emotion-sheet.cjs.mjs", "default": "./dist/emotion-sheet.cjs.js"}, "./package.json": "./package.json"}, "types": "types/index.d.ts", "license": "MIT", "scripts": {"test:typescript": "dtslint types"}, "repository": "https://github.com/emotion-js/emotion/tree/main/packages/sheet", "publishConfig": {"access": "public"}, "files": ["src", "dist", "types/*.d.ts"], "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "typescript": "^4.5.5"}, "preconstruct": {"exports": {"envConditions": ["browser"]}}}