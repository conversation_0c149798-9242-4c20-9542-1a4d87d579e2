{"version": 3, "file": "router.cjs.js", "sources": ["../history.ts", "../utils.ts", "../router.ts"], "sourcesContent": ["////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n// TODO: (v7) Change the Location generic default from `any` to `unknown` and\n// remove Remix `useLocation` wrapper.\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location<State = any> extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: State;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n\n  /**\n   * The delta between this location and the former location in the history stack\n   */\n  delta: number | null;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. This may be either a URL or the pieces\n * of a URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Returns a URL for the given `to` value\n   *\n   * @param to - The destination URL\n   */\n  createURL(to: To): URL;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLACE navigations for data routers\n   * behave the same as POP\n   *\n   * @param to Unencoded path\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n  idx: number;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  function createHref(to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to: To) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\",\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n\n    // Hash URL should always have a leading / just like window.location.pathname\n    // does, so if an app ends up at a route like /#something then we add a\n    // leading slash so all of our path-matching behaves the same as if it would\n    // in a browser router.  This is particularly important when there exists a\n    // root splat route (<Route path=\"*\">) since that matches internally against\n    // \"/*\" and we'd expect /#something to 404 in a hash router app.\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport function warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience, so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location, index: number): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  let index = getIndex()!;\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n\n  function getIndex(): number {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n\n  function createURL(to: To): URL {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base =\n      window.location.origin !== \"null\"\n        ? window.location.origin\n        : window.location.href;\n\n    let href = typeof to === \"string\" ? to : createPath(to);\n    // Treating this as a full URL will strip any trailing spaces so we need to\n    // pre-encode them since they might be part of a matching splat param from\n    // an ancestor route\n    href = href.replace(/ $/, \"%20\");\n    invariant(\n      base,\n      `No window.location.(origin|href) available to create URL for href: ${href}`\n    );\n    return new URL(href, base);\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { invariant, parsePath, warning } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: any;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  status: number;\n  location: string;\n  revalidate: boolean;\n  reloadDocument?: boolean;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: any;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\ntype LowerCaseFormMethod = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\ntype UpperCaseFormMethod = Uppercase<LowerCaseFormMethod>;\n\n/**\n * Users can specify either lowercase or uppercase form methods on `<Form>`,\n * useSubmit(), `<fetcher.Form>`, etc.\n */\nexport type HTMLFormMethod = LowerCaseFormMethod | UpperCaseFormMethod;\n\n/**\n * Active navigation/fetcher form methods are exposed in lowercase on the\n * RouterState\n */\nexport type FormMethod = LowerCaseFormMethod;\nexport type MutationFormMethod = Exclude<FormMethod, \"get\">;\n\n/**\n * In v7, active navigation/fetcher form methods are exposed in uppercase on the\n * RouterState.  This is to align with the normalization done via fetch().\n */\nexport type V7_FormMethod = UpperCaseFormMethod;\nexport type V7_MutationFormMethod = Exclude<V7_FormMethod, \"GET\">;\n\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\"\n  | \"application/json\"\n  | \"text/plain\";\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport type Submission =\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: FormData;\n      json: undefined;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: JsonValue;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: undefined;\n      text: string;\n    };\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs<Context> {\n  request: Request;\n  params: Params;\n  context?: Context;\n}\n\n// TODO: (v7) Change the defaults from any to unknown in and remove Remix wrappers:\n//   ActionFunction, ActionFunctionArgs, LoaderFunction, LoaderFunctionArgs\n//   Also, make them a type alias instead of an interface\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Loaders and actions can return anything except `undefined` (`null` is a\n * valid return value if there is no data to return).  Responses are preferred\n * and will ease any future migration to Remix\n */\ntype DataFunctionValue = Response | NonNullable<unknown> | null;\n\n/**\n * Route loader function signature\n */\nexport type LoaderFunction<Context = any> = {\n  (args: LoaderFunctionArgs<Context>):\n    | Promise<DataFunctionValue>\n    | DataFunctionValue;\n} & { hydrate?: boolean };\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction<Context = any> {\n  (args: ActionFunctionArgs<Context>):\n    | Promise<DataFunctionValue>\n    | DataFunctionValue;\n}\n\n/**\n * Arguments passed to shouldRevalidate function\n */\nexport interface ShouldRevalidateFunctionArgs {\n  currentUrl: URL;\n  currentParams: AgnosticDataRouteMatch[\"params\"];\n  nextUrl: URL;\n  nextParams: AgnosticDataRouteMatch[\"params\"];\n  formMethod?: Submission[\"formMethod\"];\n  formAction?: Submission[\"formAction\"];\n  formEncType?: Submission[\"formEncType\"];\n  text?: Submission[\"text\"];\n  formData?: Submission[\"formData\"];\n  json?: Submission[\"json\"];\n  actionResult?: any;\n  defaultShouldRevalidate: boolean;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: ShouldRevalidateFunctionArgs): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set `hasErrorBoundary`\n * from the framework-aware `errorElement` prop\n *\n * @deprecated Use `mapRouteProperties` instead\n */\nexport interface DetectErrorBoundaryFunction {\n  (route: AgnosticRouteObject): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set any framework-specific\n * properties from framework-agnostic properties\n */\nexport interface MapRoutePropertiesFunction {\n  (route: AgnosticRouteObject): {\n    hasErrorBoundary: boolean;\n  } & Record<string, any>;\n}\n\n/**\n * Keys we cannot change from within a lazy() function. We spread all other keys\n * onto the route. Either they're meaningful to the router, or they'll get\n * ignored.\n */\nexport type ImmutableRouteKey =\n  | \"lazy\"\n  | \"caseSensitive\"\n  | \"path\"\n  | \"id\"\n  | \"index\"\n  | \"children\";\n\nexport const immutableRouteKeys = new Set<ImmutableRouteKey>([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"children\",\n]);\n\ntype RequireOne<T, Key = keyof T> = Exclude<\n  {\n    [K in keyof T]: K extends Key ? Omit<T, K> & Required<Pick<T, K>> : never;\n  }[keyof T],\n  undefined\n>;\n\n/**\n * lazy() function to load a route definition, which can add non-matching\n * related properties to a route\n */\nexport interface LazyRouteFunction<R extends AgnosticRouteObject> {\n  (): Promise<RequireOne<Omit<R, ImmutableRouteKey>>>;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction;\n  action?: ActionFunction;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n  lazy?: LazyRouteFunction<AgnosticBaseRouteObject>;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\nexport type RouteManifest = Record<string, AgnosticDataRouteObject | undefined>;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `:${infer Param}`\n    ? Param extends `${infer Optional}?`\n      ? Optional\n      : Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\nexport type PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\" | \"/*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise, return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if you could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary, so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  mapRouteProperties: MapRoutePropertiesFunction,\n  parentPath: number[] = [],\n  manifest: RouteManifest = {}\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, index];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !manifest[id],\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n      };\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n        children: undefined,\n      };\n      manifest[id] = pathOrLayoutRoute;\n\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(\n          route.children,\n          mapRouteProperties,\n          treePath,\n          manifest\n        );\n      }\n\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch<string, RouteObjectType>(branches[i], decoded);\n  }\n\n  return matches;\n}\n\nexport interface UIMatch<Data = unknown, Handle = unknown> {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: Data;\n  handle: Handle;\n}\n\nexport function convertRouteMatchToUiMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UIMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle,\n  };\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  let flattenRoute = (\n    route: RouteObjectType,\n    index: number,\n    relativePath?: string\n  ) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath:\n        relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array, so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta,\n    });\n  };\n  routes.forEach((route, index) => {\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n\n  return branches;\n}\n\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path: string): string[] {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n\n  let [first, ...rest] = segments;\n\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n\n  let result: string[] = [];\n\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children, so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explode _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(\n    ...restExploded.map((subpath) =>\n      subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n\n  // Then, if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map((exploded) =>\n    path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:[\\w-]+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    if (!match) return null;\n\n    Object.assign(matchedParams, match.params);\n\n    let route = meta.route;\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  originalPath: Path,\n  params: {\n    [key in PathParam<Path>]: string | null;\n  } = {} as any\n): string {\n  let path: string = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were ` +\n        `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n        `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n        `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\") as Path;\n  }\n\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n\n  const stringify = (p: any) =>\n    p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n\n  const segments = path\n    .split(/\\/+/)\n    .map((segment, index, array) => {\n      const isLastSegment = index === array.length - 1;\n\n      // only apply the splat if it's the last segment\n      if (isLastSegment && segment === \"*\") {\n        const star = \"*\" as PathParam<Path>;\n        // Apply the splat\n        return stringify(params[star]);\n      }\n\n      const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n      if (keyMatch) {\n        const [, key, optional] = keyMatch;\n        let param = params[key as PathParam<Path>];\n        invariant(optional === \"?\" || param != null, `Missing \":${key}\" param`);\n        return stringify(param);\n      }\n\n      // Remove any optional markers from optional static segments\n      return segment.replace(/\\?$/g, \"\");\n    })\n    // Remove empty segments\n    .filter((segment) => !!segment);\n\n  return prefix + segments.join(\"/\");\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, compiledParams] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = compiledParams.reduce<Mutable<Params>>(\n    (memo, { paramName, isOptional }, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      const value = captureGroups[index];\n      if (isOptional && !value) {\n        memo[paramName] = undefined;\n      } else {\n        memo[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n      }\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\ntype CompiledPathParam = { paramName: string; isOptional?: boolean };\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, CompiledPathParam[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let params: CompiledPathParam[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(\n        /\\/:([\\w-]+)(\\?)?/g,\n        (_: string, paramName: string, isOptional) => {\n          params.push({ paramName, isOptional: isOptional != null });\n          return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n        }\n      );\n\n  if (path.endsWith(\"*\")) {\n    params.push({ paramName: \"*\" });\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex, so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, params];\n}\n\nfunction decodePath(value: string) {\n  try {\n    return value\n      .split(\"/\")\n      .map((v) => decodeURIComponent(v).replace(/\\//g, \"%2F\"))\n      .join(\"/\");\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n// Return the array of pathnames for the current route matches - used to\n// generate the routePathnames input for resolveTo()\nexport function getResolveToMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[], v7_relativeSplatPath: boolean) {\n  let pathMatches = getPathContributingMatches(matches);\n\n  // When v7_relativeSplatPath is enabled, use the full pathname for the leaf\n  // match so we include splat values for \".\" links.  See:\n  // https://github.com/remix-run/react-router/issues/11052#issuecomment-**********\n  if (v7_relativeSplatPath) {\n    return pathMatches.map((match, idx) =>\n      idx === matches.length - 1 ? match.pathname : match.pathnameBase\n    );\n  }\n\n  return pathMatches.map((match) => match.pathnameBase);\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    // With relative=\"route\" (the default), each leading .. segment means\n    // \"go up one route\" instead of \"go up one URL segment\".  This is a key\n    // difference from how <a href> works and a major reason we call this a\n    // \"to\" value instead of a \"href\".\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeysSet: Set<string> = new Set<string>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscribers: Set<(aborted: boolean, settledKey?: string) => void> =\n    new Set();\n  data: Record<string, unknown>;\n  init?: ResponseInit;\n  deferredKeys: string[] = [];\n\n  constructor(data: Record<string, unknown>, responseInit?: ResponseInit) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n\n    this.init = responseInit;\n  }\n\n  private trackPromise(\n    key: string,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, undefined, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeysSet.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\n        `Deferred data for key \"${key}\" resolved/rejected with \\`undefined\\`, ` +\n          `you must resolve/reject with a value or \\`null\\`.`\n      );\n      Object.defineProperty(promise, \"_error\", { get: () => undefinedError });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    this.emit(false, key);\n    return data;\n  }\n\n  private emit(aborted: boolean, settledKey?: string) {\n    this.subscribers.forEach((subscriber) => subscriber(aborted, settledKey));\n  }\n\n  subscribe(fn: (aborted: boolean, settledKey?: string) => void) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport type DeferFunction = (\n  data: Record<string, unknown>,\n  init?: number | ResponseInit\n) => DeferredData;\n\nexport const defer: DeferFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  return new DeferredData(data, responseInit);\n};\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirectDocument: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\n\nexport type ErrorResponse = {\n  status: number;\n  statusText: string;\n  data: any;\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n *\n * We don't export the class for public use since it's an implementation\n * detail, but we export the interface above so folks can build their own\n * abstractions around instances via isRouteErrorResponse()\n */\nexport class ErrorResponseImpl implements ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n  private error?: Error;\n  private internal: boolean;\n\n  constructor(\n    status: number,\n    statusText: string | undefined,\n    data: any,\n    internal = false\n  ) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nexport function isRouteErrorResponse(error: any): error is ErrorResponse {\n  return (\n    error != null &&\n    typeof error.status === \"number\" &&\n    typeof error.statusText === \"string\" &&\n    typeof error.internal === \"boolean\" &&\n    \"data\" in error\n  );\n}\n", "import type { History, Location, Path, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  invariant,\n  parsePath,\n  warning,\n} from \"./history\";\nimport type {\n  ActionFunction,\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  AgnosticRouteObject,\n  DataResult,\n  DeferredData,\n  DeferredResult,\n  DetectErrorBoundaryFunction,\n  ErrorResult,\n  FormEncType,\n  FormMethod,\n  HTMLFormMethod,\n  ImmutableRouteKey,\n  LoaderFunction,\n  MapRoutePropertiesFunction,\n  MutationFormMethod,\n  RedirectResult,\n  RouteData,\n  RouteManifest,\n  ShouldRevalidateFunctionArgs,\n  Submission,\n  SuccessResult,\n  UIMatch,\n  V7_FormMethod,\n  V7_MutationFormMethod,\n} from \"./utils\";\nimport {\n  ErrorResponseImpl,\n  ResultType,\n  convertRouteMatchToUiMatch,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  getResolveToMatches,\n  immutableRouteKeys,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  resolveTo,\n  stripBasename,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the future config for the router\n   */\n  get future(): FutureConfig;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the window associated with the router\n   */\n  get window(): RouterInit[\"window\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): Promise<void>;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To | null, opts?: RouterNavigateOptions): Promise<void>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to URL encode a destination path according to the internal\n   * history implementation\n   * @param to\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get a navigation blocker\n   * @param key The identifier for the blocker\n   * @param fn The blocker function implementation\n   */\n  getBlocker(key: string, fn: BlockerFunction): Blocker;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete a navigation blocker\n   * @param key The identifier for the blocker\n   */\n  deleteBlocker(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * HMR needs to pass in-flight route updates to React Router\n   * TODO: Replace this with granular route update APIs (addRoute, updateRoute, deleteRoute)\n   */\n  _internalSetRoutes(routes: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n\n  /**\n   * Map of current blockers\n   */\n  blockers: Map<string, Blocker>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface FutureConfig {\n  v7_fetcherPersist: boolean;\n  v7_normalizeFormMethod: boolean;\n  v7_partialHydration: boolean;\n  v7_prependBasename: boolean;\n  v7_relativeSplatPath: boolean;\n}\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  routes: AgnosticRouteObject[];\n  history: History;\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<FutureConfig>;\n  hydrationData?: HydrationState;\n  window?: Window;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  basename: Router[\"basename\"];\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  activeDeferreds: Record<string, DeferredData> | null;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(\n    request: Request,\n    opts?: { requestContext?: unknown }\n  ): Promise<StaticHandlerContext | Response>;\n  queryRoute(\n    request: Request,\n    opts?: { routeId?: string; requestContext?: unknown }\n  ): Promise<any>;\n}\n\ntype ViewTransitionOpts = {\n  currentLocation: Location;\n  nextLocation: Location;\n};\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (\n    state: RouterState,\n    opts: {\n      deletedFetchers: string[];\n      unstable_viewTransitionOpts?: ViewTransitionOpts;\n      unstable_flushSync: boolean;\n    }\n  ): void;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UIMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\n// Allowed for any navigation or fetch\ntype BaseNavigateOrFetchOptions = {\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  unstable_flushSync?: boolean;\n};\n\n// Only allowed for navigations\ntype BaseNavigateOptions = BaseNavigateOrFetchOptions & {\n  replace?: boolean;\n  state?: any;\n  fromRouteId?: string;\n  unstable_viewTransition?: boolean;\n};\n\n// Only allowed for submission navigations\ntype BaseSubmissionOptions = {\n  formMethod?: HTMLFormMethod;\n  formEncType?: FormEncType;\n} & (\n  | { formData: FormData; body?: undefined }\n  | { formData?: undefined; body: any }\n);\n\n/**\n * Options for a navigate() call for a normal (non-submission) navigation\n */\ntype LinkNavigateOptions = BaseNavigateOptions;\n\n/**\n * Options for a navigate() call for a submission navigation\n */\ntype SubmissionNavigateOptions = BaseNavigateOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to navigate() for a navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options for a fetch() load\n */\ntype LoadFetchOptions = BaseNavigateOrFetchOptions;\n\n/**\n * Options for a fetch() submission\n */\ntype SubmitFetchOptions = BaseNavigateOrFetchOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions = LoadFetchOptions | SubmitFetchOptions;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    json: undefined;\n    text: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    text: Submission[\"text\"];\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    text: undefined;\n    formData: undefined;\n    json: undefined;\n    data: TData | undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    data: TData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    text: Submission[\"text\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    data: TData | undefined;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface BlockerBlocked {\n  state: \"blocked\";\n  reset(): void;\n  proceed(): void;\n  location: Location;\n}\n\ninterface BlockerUnblocked {\n  state: \"unblocked\";\n  reset: undefined;\n  proceed: undefined;\n  location: undefined;\n}\n\ninterface BlockerProceeding {\n  state: \"proceeding\";\n  reset: undefined;\n  proceed: undefined;\n  location: Location;\n}\n\nexport type Blocker = BlockerUnblocked | BlockerBlocked | BlockerProceeding;\n\nexport type BlockerFunction = (args: {\n  currentLocation: Location;\n  nextLocation: Location;\n  historyAction: HistoryAction;\n}) => boolean;\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Error thrown from the current action, keyed by the route containing the\n   * error boundary to render the error.  To be committed to the state after\n   * loaders have completed\n   */\n  pendingActionError?: RouteData;\n  /**\n   * Data returned from the current action, keyed by the route owning the action.\n   * To be committed to the state after loaders have completed\n   */\n  pendingActionData?: RouteData;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Cached info for active fetcher.load() instances so they can participate\n * in revalidation\n */\ninterface FetchLoadMatch {\n  routeId: string;\n  path: string;\n}\n\n/**\n * Identified fetcher.load() calls that need to be revalidated\n */\ninterface RevalidatingFetcher extends FetchLoadMatch {\n  key: string;\n  match: AgnosticDataRouteMatch | null;\n  matches: AgnosticDataRouteMatch[] | null;\n  controller: AbortController | null;\n}\n\n/**\n * Wrapper object to allow us to throw any response out from callLoaderOrAction\n * for queryRouter while preserving whether or not it was thrown or returned\n * from the loader/action\n */\ninterface QueryRouteResponse {\n  type: ResultType.data | ResultType.error;\n  response: Response;\n}\n\nconst validMutationMethodsArr: MutationFormMethod[] = [\n  \"post\",\n  \"put\",\n  \"patch\",\n  \"delete\",\n];\nconst validMutationMethods = new Set<MutationFormMethod>(\n  validMutationMethodsArr\n);\n\nconst validRequestMethodsArr: FormMethod[] = [\n  \"get\",\n  ...validMutationMethodsArr,\n];\nconst validRequestMethods = new Set<FormMethod>(validRequestMethodsArr);\n\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_BLOCKER: BlockerUnblocked = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined,\n};\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\nconst defaultMapRouteProperties: MapRoutePropertiesFunction = (route) => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary),\n});\n\nconst TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  const routerWindow = init.window\n    ? init.window\n    : typeof window !== \"undefined\"\n    ? window\n    : undefined;\n  const isBrowser =\n    typeof routerWindow !== \"undefined\" &&\n    typeof routerWindow.document !== \"undefined\" &&\n    typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  // Routes keyed by ID\n  let manifest: RouteManifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(\n    init.routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n  let inFlightDataRoutes: AgnosticDataRouteObject[] | undefined;\n  let basename = init.basename || \"/\";\n  // Config driven behavior flags\n  let future: FutureConfig = {\n    v7_fetcherPersist: false,\n    v7_normalizeFormMethod: false,\n    v7_partialHydration: false,\n    v7_prependBasename: false,\n    v7_relativeSplatPath: false,\n    ...init.future,\n  };\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname,\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  let initialized: boolean;\n  let hasLazyRoutes = initialMatches.some((m) => m.route.lazy);\n  let hasLoaders = initialMatches.some((m) => m.route.loader);\n  if (hasLazyRoutes) {\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    initialized = false;\n  } else if (!hasLoaders) {\n    // If we've got no loaders to run, then we're good to go\n    initialized = true;\n  } else if (future.v7_partialHydration) {\n    // If partial hydration is enabled, we're initialized so long as we were\n    // provided with hydrationData for every route with a loader, and no loaders\n    // were marked for explicit hydration\n    let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n    let errors = init.hydrationData ? init.hydrationData.errors : null;\n    initialized = initialMatches.every(\n      (m) =>\n        m.route.loader &&\n        m.route.loader.hydrate !== true &&\n        ((loaderData && loaderData[m.route.id] !== undefined) ||\n          (errors && errors[m.route.id] !== undefined))\n    );\n  } else {\n    // Without partial hydration - we're initialized if we were provided any\n    // hydrationData - which is expected to be complete\n    initialized = init.hydrationData != null;\n  }\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n\n  // Should the current navigation enable document.startViewTransition?\n  let pendingViewTransitionEnabled = false;\n\n  // Store applied view transitions so we can apply them on POP\n  let appliedViewTransitions: Map<string, Set<string>> = new Map<\n    string,\n    Set<string>\n  >();\n\n  // Cleanup function for persisting applied transitions to sessionStorage\n  let removePageHideEventListener: (() => void) | null = null;\n\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: string[] = [];\n\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set<string>();\n\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n\n  // Ref-count mounted fetchers so we know when it's ok to clean them up\n  let activeFetchers = new Map<string, number>();\n\n  // Fetchers that have requested a delete when using v7_fetcherPersist,\n  // they'll be officially removed after they return to idle\n  let deletedFetchers = new Set<string>();\n\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map<string, BlockerFunction>();\n\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let ignoreNextHistoryUpdate = false;\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        // Ignore this event if it was just us resetting the URL from a\n        // blocked POP navigation\n        if (ignoreNextHistoryUpdate) {\n          ignoreNextHistoryUpdate = false;\n          return;\n        }\n\n        warning(\n          blockerFunctions.size === 0 || delta != null,\n          \"You are trying to use a blocker on a POP navigation to a location \" +\n            \"that was not created by @remix-run/router. This will fail silently in \" +\n            \"production. This can happen if you are navigating outside the router \" +\n            \"via `window.history.pushState`/`window.location.hash` instead of using \" +\n            \"router navigation APIs.  This can also happen if you are using \" +\n            \"createHashRouter and the user manually changes the URL.\"\n        );\n\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction,\n        });\n\n        if (blockerKey && delta != null) {\n          // Restore the URL to match the current UI, but don't update router state\n          ignoreNextHistoryUpdate = true;\n          init.history.go(delta * -1);\n\n          // Put the blocker into a blocked state\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey!, {\n                state: \"proceeding\",\n                proceed: undefined,\n                reset: undefined,\n                location,\n              });\n              // Re-do the same POP navigation we just blocked\n              init.history.go(delta);\n            },\n            reset() {\n              let blockers = new Map(state.blockers);\n              blockers.set(blockerKey!, IDLE_BLOCKER);\n              updateState({ blockers });\n            },\n          });\n          return;\n        }\n\n        return startNavigation(historyAction, location);\n      }\n    );\n\n    if (isBrowser) {\n      // FIXME: This feels gross.  How can we cleanup the lines between\n      // scrollRestoration/appliedTransitions persistance?\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () =>\n        persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () =>\n        routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location, {\n        initialHydration: true,\n      });\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(\n    newState: Partial<RouterState>,\n    opts: {\n      flushSync?: boolean;\n      viewTransitionOpts?: ViewTransitionOpts;\n    } = {}\n  ): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n\n    // Prep fetcher cleanup so we can tell the UI which fetcher data entries\n    // can be removed\n    let completedFetchers: string[] = [];\n    let deletedFetchersKeys: string[] = [];\n\n    if (future.v7_fetcherPersist) {\n      state.fetchers.forEach((fetcher, key) => {\n        if (fetcher.state === \"idle\") {\n          if (deletedFetchers.has(key)) {\n            // Unmounted from the UI and can be totally removed\n            deletedFetchersKeys.push(key);\n          } else {\n            // Returned to idle but still mounted in the UI, so semi-remains for\n            // revalidations and such\n            completedFetchers.push(key);\n          }\n        }\n      });\n    }\n\n    // Iterate over a local copy so that if flushSync is used and we end up\n    // removing and adding a new subscriber due to the useCallback dependencies,\n    // we don't get ourselves into a loop calling the new subscriber immediately\n    [...subscribers].forEach((subscriber) =>\n      subscriber(state, {\n        deletedFetchers: deletedFetchersKeys,\n        unstable_viewTransitionOpts: opts.viewTransitionOpts,\n        unstable_flushSync: opts.flushSync === true,\n      })\n    );\n\n    // Remove idle fetchers from state since we only care about in-flight fetchers.\n    if (future.v7_fetcherPersist) {\n      completedFetchers.forEach((key) => state.fetchers.delete(key));\n      deletedFetchersKeys.forEach((key) => deleteFetcher(key));\n    }\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>,\n    { flushSync }: { flushSync?: boolean } = {}\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      isMutationMethod(state.navigation.formMethod) &&\n      state.navigation.state === \"loading\" &&\n      location.state?._isRedirect !== true;\n\n    let actionData: RouteData | null;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData\n      ? mergeLoaderData(\n          state.loaderData,\n          newState.loaderData,\n          newState.matches || [],\n          newState.errors\n        )\n      : state.loaderData;\n\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset =\n      pendingPreventScrollReset === true ||\n      (state.navigation.formMethod != null &&\n        isMutationMethod(state.navigation.formMethod) &&\n        location.state?._isRedirect !== true);\n\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    let viewTransitionOpts: ViewTransitionOpts | undefined;\n\n    // On POP, enable transitions if they were enabled on the original navigation\n    if (pendingAction === HistoryAction.Pop) {\n      // Forward takes precedence so they behave like the original navigation\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location,\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        // If we don't have a previous forward nav, assume we're popping back to\n        // the new location and enable if that location previously enabled\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location,\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      // Store the applied transition on PUSH/REPLACE\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = new Set<string>([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location,\n      };\n    }\n\n    updateState(\n      {\n        ...newState, // matches, errors, fetchers go through as-is\n        actionData,\n        loaderData,\n        historyAction: pendingAction,\n        location,\n        initialized: true,\n        navigation: IDLE_NAVIGATION,\n        revalidation: \"idle\",\n        restoreScrollPosition: getSavedScrollPosition(\n          location,\n          newState.matches || state.matches\n        ),\n        preventScrollReset,\n        blockers,\n      },\n      {\n        viewTransitionOpts,\n        flushSync: flushSync === true,\n      }\n    );\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n    cancelledFetcherLoads = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To | null,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      to,\n      future.v7_relativeSplatPath,\n      opts?.fromRouteId,\n      opts?.relative\n    );\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      false,\n      normalizedPath,\n      opts\n    );\n\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation),\n    };\n\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n\n    let historyAction = HistoryAction.Push;\n\n    if (userReplace === true) {\n      historyAction = HistoryAction.Replace;\n    } else if (userReplace === false) {\n      // no-op\n    } else if (\n      submission != null &&\n      isMutationMethod(submission.formMethod) &&\n      submission.formAction === state.location.pathname + state.location.search\n    ) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = HistoryAction.Replace;\n    }\n\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    let flushSync = (opts && opts.unstable_flushSync) === true;\n\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction,\n    });\n\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey!, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation,\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey!, IDLE_BLOCKER);\n          updateState({ blockers });\n        },\n      });\n      return;\n    }\n\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.unstable_viewTransition,\n      flushSync,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      { overrideNavigation: state.navigation }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      initialHydration?: boolean;\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponseImpl;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n      enableViewTransition?: boolean;\n      flushSync?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(routesToUse);\n      // Cancel all pending deferred on 404s since we don't keep any routes\n      cancelActiveDeferreds();\n      completeNavigation(\n        location,\n        {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        },\n        { flushSync }\n      );\n      return;\n    }\n\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial load will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (\n      state.initialized &&\n      !isRevalidationRequired &&\n      isHashChangeOnly(state.location, location) &&\n      !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))\n    ) {\n      completeNavigation(location, { matches }, { flushSync });\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionData: RouteData | undefined;\n    let pendingError: RouteData | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingError = {\n        [findNearestBoundary(matches).route.id]: opts.pendingError,\n      };\n    } else if (\n      opts &&\n      opts.submission &&\n      isMutationMethod(opts.submission.formMethod)\n    ) {\n      // Call action if we received an action submission\n      let actionOutput = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        { replace: opts.replace, flushSync }\n      );\n\n      if (actionOutput.shortCircuited) {\n        return;\n      }\n\n      pendingActionData = actionOutput.pendingActionData;\n      pendingError = actionOutput.pendingActionError;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n\n      // Create a GET request for the loaders\n      request = new Request(request.url, { signal: request.signal });\n    }\n\n    // Call loaders\n    let { shortCircuited, loaderData, errors } = await handleLoaders(\n      request,\n      location,\n      matches,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.fetcherSubmission,\n      opts && opts.replace,\n      opts && opts.initialHydration === true,\n      flushSync,\n      pendingActionData,\n      pendingError\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches,\n      ...(pendingActionData ? { actionData: pendingActionData } : {}),\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    opts: { replace?: boolean; flushSync?: boolean } = {}\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({ navigation }, { flushSync: opts.flushSync === true });\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id,\n        }),\n      };\n    } else {\n      result = await callLoaderOrAction(\n        \"action\",\n        request,\n        actionMatch,\n        matches,\n        manifest,\n        mapRouteProperties,\n        basename,\n        future.v7_relativeSplatPath\n      );\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace: boolean;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        replace =\n          result.location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(state, result, { submission, replace });\n      return { shortCircuited: true };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions are REPLACE navigations, but if the\n      // action threw an error that'll be rendered in an errorElement, we fall\n      // back to PUSH so that the user can use the back button to get back to\n      // the pre-submission form location to try again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        // Send back an empty object we can use to clear out any prior actionData\n        pendingActionData: {},\n        pendingActionError: { [boundaryMatch.route.id]: result.error },\n      };\n    }\n\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    return {\n      pendingActionData: { [actionMatch.route.id]: result.data },\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    fetcherSubmission?: Submission,\n    replace?: boolean,\n    initialHydration?: boolean,\n    flushSync?: boolean,\n    pendingActionData?: RouteData,\n    pendingError?: RouteData\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation =\n      overrideNavigation || getLoadingNavigation(location, submission);\n\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission =\n      submission ||\n      fetcherSubmission ||\n      getSubmissionFromNavigation(loadingNavigation);\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      future.v7_partialHydration && initialHydration === true,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      pendingActionData,\n      pendingError\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(\n        location,\n        {\n          matches,\n          loaderData: {},\n          // Commit pending error if we're short circuiting\n          errors: pendingError || null,\n          ...(pendingActionData ? { actionData: pendingActionData } : {}),\n          ...(updatedFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n        },\n        { flushSync }\n      );\n      return { shortCircuited: true };\n    }\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    // If we have partialHydration enabled, then don't update the state for the\n    // initial data load since iot's not a \"navigation\"\n    if (\n      !isUninterruptedRevalidation &&\n      (!future.v7_partialHydration || !initialHydration)\n    ) {\n      revalidatingFetchers.forEach((rf) => {\n        let fetcher = state.fetchers.get(rf.key);\n        let revalidatingFetcher = getLoadingFetcher(\n          undefined,\n          fetcher ? fetcher.data : undefined\n        );\n        state.fetchers.set(rf.key, revalidatingFetcher);\n      });\n      let actionData = pendingActionData || state.actionData;\n      updateState(\n        {\n          navigation: loadingNavigation,\n          ...(actionData\n            ? Object.keys(actionData).length === 0\n              ? { actionData: null }\n              : { actionData }\n            : {}),\n          ...(revalidatingFetchers.length > 0\n            ? { fetchers: new Map(state.fetchers) }\n            : {}),\n        },\n        {\n          flushSync,\n        }\n      );\n    }\n\n    revalidatingFetchers.forEach((rf) => {\n      if (fetchControllers.has(rf.key)) {\n        abortFetcher(rf.key);\n      }\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((f) => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    let { results, loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(results);\n    if (redirect) {\n      if (redirect.idx >= matchesToLoad.length) {\n        // If this redirect came from a fetcher make sure we mark it in\n        // fetchRedirectIds so it doesn't get revalidated on the next set of\n        // loader executions\n        let fetcherKey =\n          revalidatingFetchers[redirect.idx - matchesToLoad.length].key;\n        fetchRedirectIds.add(fetcherKey);\n      }\n      await startRedirectNavigation(state, redirect.result, { replace });\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      matchesToLoad,\n      loaderResults,\n      pendingError,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers =\n      updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n\n    return {\n      loaderData,\n      errors,\n      ...(shouldUpdateFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n    };\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    if (fetchControllers.has(key)) abortFetcher(key);\n    let flushSync = (opts && opts.unstable_flushSync) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      href,\n      future.v7_relativeSplatPath,\n      routeId,\n      opts?.relative\n    );\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: normalizedPath }),\n        { flushSync }\n      );\n      return;\n    }\n\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      true,\n      normalizedPath,\n      opts\n    );\n\n    if (error) {\n      setFetcherError(key, routeId, error, { flushSync });\n      return;\n    }\n\n    let match = getTargetMatch(matches, path);\n\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(\n        key,\n        routeId,\n        path,\n        match,\n        matches,\n        flushSync,\n        submission\n      );\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, { routeId, path });\n    handleFetcherLoader(\n      key,\n      routeId,\n      path,\n      match,\n      matches,\n      flushSync,\n      submission\n    );\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    flushSync: boolean,\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    if (!match.route.action && !match.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: submission.formMethod,\n        pathname: path,\n        routeId: routeId,\n      });\n      setFetcherError(key, routeId, error, { flushSync });\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync,\n    });\n\n    // Call the action for the fetcher\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let actionResult = await callLoaderOrAction(\n      \"action\",\n      fetchRequest,\n      match,\n      requestMatches,\n      manifest,\n      mapRouteProperties,\n      basename,\n      future.v7_relativeSplatPath\n    );\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    // When using v7_fetcherPersist, we don't want errors bubbling up to the UI\n    // or redirects processed for unmounted fetchers so we just revert them to\n    // idle\n    if (future.v7_fetcherPersist && deletedFetchers.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      }\n      // Let SuccessResult's fall through for revalidation\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          // A new navigation was kicked off after our action started, so that\n          // should take precedence over this redirect navigation.  We already\n          // set isRevalidationRequired so all loaders for the new route should\n          // fire unless opted out via shouldRevalidate\n          updateFetcherState(key, getDoneFetcher(undefined));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(state, actionResult, {\n            fetcherSubmission: submission,\n          });\n        }\n      }\n\n      // Process any non-redirect errors thrown\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n      nextLocation,\n      abortController.signal\n    );\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(routesToUse, state.navigation.location, basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      false,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      { [match.route.id]: actionResult.data },\n      undefined // No need to send through errors since we short circuit above\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter((rf) => rf.key !== key)\n      .forEach((rf) => {\n        let staleKey = rf.key;\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher = getLoadingFetcher(\n          undefined,\n          existingFetcher ? existingFetcher.data : undefined\n        );\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        if (fetchControllers.has(staleKey)) {\n          abortFetcher(staleKey);\n        }\n        if (rf.controller) {\n          fetchControllers.set(staleKey, rf.controller);\n        }\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((rf) => abortFetcher(rf.key));\n\n    abortController.signal.addEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    let { results, loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    abortController.signal.removeEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n\n    let redirect = findRedirect(results);\n    if (redirect) {\n      if (redirect.idx >= matchesToLoad.length) {\n        // If this redirect came from a fetcher make sure we mark it in\n        // fetchRedirectIds so it doesn't get revalidated on the next set of\n        // loader executions\n        let fetcherKey =\n          revalidatingFetchers[redirect.idx - matchesToLoad.length].key;\n        fetchRedirectIds.add(fetcherKey);\n      }\n      return startRedirectNavigation(state, redirect.result);\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      state.matches,\n      matchesToLoad,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n\n    abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        fetchers: new Map(state.fetchers),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[],\n    flushSync: boolean,\n    submission?: Submission\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(\n      key,\n      getLoadingFetcher(\n        submission,\n        existingFetcher ? existingFetcher.data : undefined\n      ),\n      { flushSync }\n    );\n\n    // Call the loader for this fetcher route match\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let result: DataResult = await callLoaderOrAction(\n      \"loader\",\n      fetchRequest,\n      match,\n      matches,\n      manifest,\n      mapRouteProperties,\n      basename,\n      future.v7_relativeSplatPath\n    );\n\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // We don't want errors bubbling up or redirects followed for unmounted\n    // fetchers, so short circuit here if it was removed from the UI\n    if (deletedFetchers.has(key)) {\n      updateFetcherState(key, getDoneFetcher(undefined));\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(state, result);\n        return;\n      }\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    state: RouterState,\n    redirect: RedirectResult,\n    {\n      submission,\n      fetcherSubmission,\n      replace,\n    }: {\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      replace?: boolean;\n    } = {}\n  ) {\n    if (redirect.revalidate) {\n      isRevalidationRequired = true;\n    }\n\n    let redirectLocation = createLocation(state.location, redirect.location, {\n      _isRedirect: true,\n    });\n    invariant(\n      redirectLocation,\n      \"Expected a location on the redirect navigation\"\n    );\n\n    if (isBrowser) {\n      let isDocumentReload = false;\n\n      if (redirect.reloadDocument) {\n        // Hard reload if the response contained X-Remix-Reload-Document\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(redirect.location)) {\n        const url = init.history.createURL(redirect.location);\n        isDocumentReload =\n          // Hard reload if it's an absolute URL to a new origin\n          url.origin !== routerWindow.location.origin ||\n          // Hard reload if it's an absolute URL that does not match our basename\n          stripBasename(url.pathname, basename) == null;\n      }\n\n      if (isDocumentReload) {\n        if (replace) {\n          routerWindow.location.replace(redirect.location);\n        } else {\n          routerWindow.location.assign(redirect.location);\n        }\n        return;\n      }\n    }\n\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true ? HistoryAction.Replace : HistoryAction.Push;\n\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let { formMethod, formAction, formEncType } = state.navigation;\n    if (\n      !submission &&\n      !fetcherSubmission &&\n      formMethod &&\n      formAction &&\n      formEncType\n    ) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    let activeSubmission = submission || fetcherSubmission;\n    if (\n      redirectPreserveMethodStatusCodes.has(redirect.status) &&\n      activeSubmission &&\n      isMutationMethod(activeSubmission.formMethod)\n    ) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: {\n          ...activeSubmission,\n          formAction: redirect.location,\n        },\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    } else {\n      // If we have a navigation submission, we will preserve it through the\n      // redirect navigation\n      let overrideNavigation = getLoadingNavigation(\n        redirectLocation,\n        submission\n      );\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    }\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    currentMatches: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    // Call all navigation loaders and revalidating fetcher loaders in parallel,\n    // then slice off the results into separate arrays so we can handle them\n    // accordingly\n    let results = await Promise.all([\n      ...matchesToLoad.map((match) =>\n        callLoaderOrAction(\n          \"loader\",\n          request,\n          match,\n          matches,\n          manifest,\n          mapRouteProperties,\n          basename,\n          future.v7_relativeSplatPath\n        )\n      ),\n      ...fetchersToLoad.map((f) => {\n        if (f.matches && f.match && f.controller) {\n          return callLoaderOrAction(\n            \"loader\",\n            createClientSideRequest(init.history, f.path, f.controller.signal),\n            f.match,\n            f.matches,\n            manifest,\n            mapRouteProperties,\n            basename,\n            future.v7_relativeSplatPath\n          );\n        } else {\n          let error: ErrorResult = {\n            type: ResultType.error,\n            error: getInternalRouterError(404, { pathname: f.path }),\n          };\n          return error;\n        }\n      }),\n    ]);\n    let loaderResults = results.slice(0, matchesToLoad.length);\n    let fetcherResults = results.slice(matchesToLoad.length);\n\n    await Promise.all([\n      resolveDeferredResults(\n        currentMatches,\n        matchesToLoad,\n        loaderResults,\n        loaderResults.map(() => request.signal),\n        false,\n        state.loaderData\n      ),\n      resolveDeferredResults(\n        currentMatches,\n        fetchersToLoad.map((f) => f.match),\n        fetcherResults,\n        fetchersToLoad.map((f) => (f.controller ? f.controller.signal : null)),\n        true\n      ),\n    ]);\n\n    return { results, loaderResults, fetcherResults };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.push(key);\n        abortFetcher(key);\n      }\n    });\n  }\n\n  function updateFetcherState(\n    key: string,\n    fetcher: Fetcher,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    state.fetchers.set(key, fetcher);\n    updateState(\n      { fetchers: new Map(state.fetchers) },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function setFetcherError(\n    key: string,\n    routeId: string,\n    error: any,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState(\n      {\n        errors: {\n          [boundaryMatch.route.id]: error,\n        },\n        fetchers: new Map(state.fetchers),\n      },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    if (future.v7_fetcherPersist) {\n      activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n      // If this fetcher was previously marked for deletion, unmark it since we\n      // have a new instance\n      if (deletedFetchers.has(key)) {\n        deletedFetchers.delete(key);\n      }\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  function deleteFetcher(key: string): void {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (\n      fetchControllers.has(key) &&\n      !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))\n    ) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    deletedFetchers.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function deleteFetcherAndUpdateState(key: string): void {\n    if (future.v7_fetcherPersist) {\n      let count = (activeFetchers.get(key) || 0) - 1;\n      if (count <= 0) {\n        activeFetchers.delete(key);\n        deletedFetchers.add(key);\n      } else {\n        activeFetchers.set(key, count);\n      }\n    } else {\n      deleteFetcher(key);\n    }\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, `Expected fetch controller: ${key}`);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): boolean {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function getBlocker(key: string, fn: BlockerFunction) {\n    let blocker: Blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n\n    return blocker;\n  }\n\n  function deleteBlocker(key: string) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key: string, newBlocker: Blocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(\n      (blocker.state === \"unblocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"proceeding\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"unblocked\") ||\n        (blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\"),\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({ blockers });\n  }\n\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction,\n  }: {\n    currentLocation: Location;\n    nextLocation: Location;\n    historyAction: HistoryAction;\n  }): string | undefined {\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return blockerKey;\n    }\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function getScrollKey(location: Location, matches: AgnosticDataRouteMatch[]) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(\n        location,\n        matches.map((m) => convertRouteMatchToUiMatch(m, state.loaderData))\n      );\n      return key || location.key;\n    }\n    return location.key;\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  function _internalSetRoutes(newRoutes: AgnosticDataRouteObject[]) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(\n      newRoutes,\n      mapRouteProperties,\n      undefined,\n      manifest\n    );\n  }\n\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    encodeLocation: (to: To) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: deleteFetcherAndUpdateState,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nexport const UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface StaticHandlerFutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_throwAbortReason: boolean;\n}\n\nexport interface CreateStaticHandlerOptions {\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<StaticHandlerFutureConfig>;\n}\n\nexport function createStaticHandler(\n  routes: AgnosticRouteObject[],\n  opts?: CreateStaticHandlerOptions\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n\n  let manifest: RouteManifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (opts?.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts?.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Config driven behavior flags\n  let future: StaticHandlerFutureConfig = {\n    v7_relativeSplatPath: false,\n    v7_throwAbortReason: false,\n    ...(opts ? opts.future : null),\n  };\n\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   */\n  async function query(\n    request: Request,\n    { requestContext }: { requestContext?: unknown } = {}\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let result = await queryImpl(request, location, matches, requestContext);\n    if (isResponse(result)) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, basename, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   */\n  async function queryRoute(\n    request: Request,\n    {\n      routeId,\n      requestContext,\n    }: { requestContext?: unknown; routeId?: string } = {}\n  ): Promise<any> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId,\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      match\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n\n    if (result.loaderData) {\n      let data = Object.values(result.loaderData)[0];\n      if (result.activeDeferreds?.[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n\n    return undefined;\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    routeMatch?: AgnosticDataRouteMatch\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        routeMatch\n      );\n      return isResponse(result)\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction, we throw\n      // it to bail out and then return or throw here based on whether the user\n      // returned or threw\n      if (isQueryRouteResponse(e)) {\n        if (e.type === ResultType.error) {\n          throw e.response;\n        }\n        return e.response;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    requestContext: unknown,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    let result: DataResult;\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id,\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    } else {\n      result = await callLoaderOrAction(\n        \"action\",\n        request,\n        actionMatch,\n        matches,\n        manifest,\n        mapRouteProperties,\n        basename,\n        future.v7_relativeSplatPath,\n        { isStaticRequest: true, isRouteRequest, requestContext }\n      );\n\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest, future);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.status,\n        headers: {\n          Location: result.location,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, { type: \"defer-action\" });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        undefined,\n        {\n          [boundaryMatch.route.id]: result.error,\n        }\n      );\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal,\n    });\n    let context = await loadRouteData(loaderRequest, matches, requestContext);\n\n    return {\n      ...context,\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      actionHeaders: {\n        ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n      },\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    routeMatch?: AgnosticDataRouteMatch,\n    pendingActionError?: RouteData\n  ): Promise<\n    | Omit<\n        StaticHandlerContext,\n        \"location\" | \"basename\" | \"actionData\" | \"actionHeaders\"\n      >\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (\n      isRouteRequest &&\n      !routeMatch?.route.loader &&\n      !routeMatch?.route.lazy\n    ) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id,\n      });\n    }\n\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : getLoaderMatchesUntilBoundary(\n          matches,\n          Object.keys(pendingActionError || {})[0]\n        );\n    let matchesToLoad = requestMatches.filter(\n      (m) => m.route.loader || m.route.lazy\n    );\n\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce(\n          (acc, m) => Object.assign(acc, { [m.route.id]: null }),\n          {}\n        ),\n        errors: pendingActionError || null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let results = await Promise.all([\n      ...matchesToLoad.map((match) =>\n        callLoaderOrAction(\n          \"loader\",\n          request,\n          match,\n          matches,\n          manifest,\n          mapRouteProperties,\n          basename,\n          future.v7_relativeSplatPath,\n          { isStaticRequest: true, isRouteRequest, requestContext }\n        )\n      ),\n    ]);\n\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest, future);\n    }\n\n    // Process and commit output from loaders\n    let activeDeferreds = new Map<string, DeferredData>();\n    let context = processRouteLoaderData(\n      matches,\n      matchesToLoad,\n      results,\n      pendingActionError,\n      activeDeferreds\n    );\n\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set<string>(\n      matchesToLoad.map((match) => match.route.id)\n    );\n    matches.forEach((match) => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n\n    return {\n      ...context,\n      matches,\n      activeDeferreds:\n        activeDeferreds.size > 0\n          ? Object.fromEntries(activeDeferreds.entries())\n          : null,\n    };\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\nfunction throwStaticHandlerAbortedError(\n  request: Request,\n  isRouteRequest: boolean,\n  future: StaticHandlerFutureConfig\n) {\n  if (future.v7_throwAbortReason && request.signal.reason !== undefined) {\n    throw request.signal.reason;\n  }\n\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(`${method}() call aborted: ${request.method} ${request.url}`);\n}\n\nfunction isSubmissionNavigation(\n  opts: BaseNavigateOrFetchOptions\n): opts is SubmissionNavigateOptions {\n  return (\n    opts != null &&\n    ((\"formData\" in opts && opts.formData != null) ||\n      (\"body\" in opts && opts.body !== undefined))\n  );\n}\n\nfunction normalizeTo(\n  location: Path,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  prependBasename: boolean,\n  to: To | null,\n  v7_relativeSplatPath: boolean,\n  fromRouteId?: string,\n  relative?: RelativeRoutingType\n) {\n  let contextualMatches: AgnosticDataRouteMatch[];\n  let activeRouteMatch: AgnosticDataRouteMatch | undefined;\n  if (fromRouteId) {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n\n  // Resolve the relative path\n  let path = resolveTo(\n    to ? to : \".\",\n    getResolveToMatches(contextualMatches, v7_relativeSplatPath),\n    stripBasename(location.pathname, basename) || location.pathname,\n    relative === \"path\"\n  );\n\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n\n  // Add an ?index param for matched index routes if we don't already have one\n  if (\n    (to == null || to === \"\" || to === \".\") &&\n    activeRouteMatch &&\n    activeRouteMatch.route.index &&\n    !hasNakedIndexQuery(path.search)\n  ) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  normalizeFormMethod: boolean,\n  isFetcher: boolean,\n  path: string,\n  opts?: BaseNavigateOrFetchOptions\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponseImpl;\n} {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod }),\n    };\n  }\n\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, { type: \"invalid-body\" }),\n  });\n\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod\n    ? (rawFormMethod.toUpperCase() as V7_FormMethod)\n    : (rawFormMethod.toLowerCase() as FormMethod);\n  let formAction = stripHashFromPath(path);\n\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      let text =\n        typeof opts.body === \"string\"\n          ? opts.body\n          : opts.body instanceof FormData ||\n            opts.body instanceof URLSearchParams\n          ? // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n            Array.from(opts.body.entries()).reduce(\n              (acc, [name, value]) => `${acc}${name}=${value}\\n`,\n              \"\"\n            )\n          : String(opts.body);\n\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text,\n        },\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      try {\n        let json =\n          typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined,\n          },\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n\n  invariant(\n    typeof FormData === \"function\",\n    \"FormData is not available in this environment\"\n  );\n\n  let searchParams: URLSearchParams;\n  let formData: FormData;\n\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n\n  let submission: Submission = {\n    formMethod,\n    formAction,\n    formEncType:\n      (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined,\n  };\n\n  if (isMutationMethod(submission.formMethod)) {\n    return { path, submission };\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n\n  return { path: createPath(parsedPath), submission };\n}\n\n// Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId?: string\n) {\n  let boundaryMatches = matches;\n  if (boundaryId) {\n    let index = matches.findIndex((m) => m.route.id === boundaryId);\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n  return boundaryMatches;\n}\n\nfunction getMatchesToLoad(\n  history: History,\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  isInitialLoad: boolean,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: string[],\n  deletedFetchers: Set<string>,\n  fetchLoadMatches: Map<string, FetchLoadMatch>,\n  fetchRedirectIds: Set<string>,\n  routesToUse: AgnosticDataRouteObject[],\n  basename: string | undefined,\n  pendingActionData?: RouteData,\n  pendingError?: RouteData\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingError\n    ? Object.values(pendingError)[0]\n    : pendingActionData\n    ? Object.values(pendingActionData)[0]\n    : undefined;\n\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryId = pendingError ? Object.keys(pendingError)[0] : undefined;\n  let boundaryMatches = getLoaderMatchesUntilBoundary(matches, boundaryId);\n\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    let { route } = match;\n    if (route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n\n    if (route.loader == null) {\n      return false;\n    }\n\n    if (isInitialLoad) {\n      if (route.loader.hydrate) {\n        return true;\n      }\n      return (\n        state.loaderData[route.id] === undefined &&\n        // Don't re-run if the loader ran and threw an error\n        (!state.errors || state.errors[route.id] === undefined)\n      );\n    }\n\n    // Always call the loader on new route instances and pending defer cancellations\n    if (\n      isNewLoader(state.loaderData, state.matches[index], match) ||\n      cancelledDeferredRoutes.some((id) => id === match.route.id)\n    ) {\n      return true;\n    }\n\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n\n    return shouldRevalidateLoader(match, {\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params,\n      ...submission,\n      actionResult,\n      defaultShouldRevalidate:\n        // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n        isRevalidationRequired ||\n        // Clicked the same link, resubmitted a GET form\n        currentUrl.pathname + currentUrl.search ===\n          nextUrl.pathname + nextUrl.search ||\n        // Search params affect all loaders\n        currentUrl.search !== nextUrl.search ||\n        isNewRouteInstance(currentRouteMatch, nextRouteMatch),\n    });\n  });\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate:\n    //  - on initial load (shouldn't be any fetchers then anyway)\n    //  - if fetcher won't be present in the subsequent render\n    //    - no longer matches the URL (v7_fetcherPersist=false)\n    //    - was unmounted but persisted due to v7_fetcherPersist=true\n    if (\n      isInitialLoad ||\n      !matches.some((m) => m.route.id === f.routeId) ||\n      deletedFetchers.has(key)\n    ) {\n      return;\n    }\n\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null,\n      });\n      return;\n    }\n\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.includes(key)) {\n      // Always revalidate if the fetcher was cancelled\n      shouldRevalidate = true;\n    } else if (\n      fetcher &&\n      fetcher.state !== \"idle\" &&\n      fetcher.data === undefined\n    ) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, {\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params,\n        ...submission,\n        actionResult,\n        defaultShouldRevalidate: isRevalidationRequired,\n      });\n    }\n\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController(),\n      });\n    }\n  });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath != null &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  loaderMatch: AgnosticDataRouteMatch,\n  arg: ShouldRevalidateFunctionArgs\n) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return arg.defaultShouldRevalidate;\n}\n\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(\n  route: AgnosticDataRouteObject,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  manifest: RouteManifest\n) {\n  if (!route.lazy) {\n    return;\n  }\n\n  let lazyRoute = await route.lazy();\n\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates: Record<string, any> = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue =\n      routeToUpdate[lazyRouteProperty as keyof typeof routeToUpdate];\n\n    let isPropertyStaticallyDefined =\n      staticRouteValue !== undefined &&\n      // This property isn't static since it should always be updated based\n      // on the route updates\n      lazyRouteProperty !== \"hasErrorBoundary\";\n\n    warning(\n      !isPropertyStaticallyDefined,\n      `Route \"${routeToUpdate.id}\" has a static property \"${lazyRouteProperty}\" ` +\n        `defined but its lazy function is also returning a value for this property. ` +\n        `The lazy route property \"${lazyRouteProperty}\" will be ignored.`\n    );\n\n    if (\n      !isPropertyStaticallyDefined &&\n      !immutableRouteKeys.has(lazyRouteProperty as ImmutableRouteKey)\n    ) {\n      routeUpdates[lazyRouteProperty] =\n        lazyRoute[lazyRouteProperty as keyof typeof lazyRoute];\n    }\n  }\n\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, {\n    // To keep things framework agnostic, we use the provided\n    // `mapRouteProperties` (or wrapped `detectErrorBoundary`) function to\n    // set the framework-aware properties (`element`/`hasErrorBoundary`) since\n    // the logic will differ between frameworks.\n    ...mapRouteProperties(routeToUpdate),\n    lazy: undefined,\n  });\n}\n\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  matches: AgnosticDataRouteMatch[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  basename: string,\n  v7_relativeSplatPath: boolean,\n  opts: {\n    isStaticRequest?: boolean;\n    isRouteRequest?: boolean;\n    requestContext?: unknown;\n  } = {}\n): Promise<DataResult> {\n  let resultType;\n  let result;\n  let onReject: (() => void) | undefined;\n\n  let runHandler = (handler: ActionFunction | LoaderFunction) => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject: () => void;\n    let abortPromise = new Promise((_, r) => (reject = r));\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n    return Promise.race([\n      handler({\n        request,\n        params: match.params,\n        context: opts.requestContext,\n      }),\n      abortPromise,\n    ]);\n  };\n\n  try {\n    let handler = match.route[type];\n\n    if (match.route.lazy) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let handlerError;\n        let values = await Promise.all([\n          // If the handler throws, don't let it immediately bubble out,\n          // since we need to let the lazy() execution finish so we know if this\n          // route has a boundary that can handle the error\n          runHandler(handler).catch((e) => {\n            handlerError = e;\n          }),\n          loadLazyRouteModule(match.route, mapRouteProperties, manifest),\n        ]);\n        if (handlerError) {\n          throw handlerError;\n        }\n        result = values[0];\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadLazyRouteModule(match.route, mapRouteProperties, manifest);\n\n        handler = match.route[type];\n        if (handler) {\n          // Handler still run even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id,\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return { type: ResultType.data, data: undefined };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname,\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n\n    invariant(\n      result !== undefined,\n      `You defined ${type === \"action\" ? \"an action\" : \"a loader\"} for route ` +\n        `\"${match.route.id}\" but didn't return anything from your \\`${type}\\` ` +\n        `function. Please return a value or \\`null\\`.`\n    );\n  } catch (e) {\n    resultType = ResultType.error;\n    result = e;\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n\n  if (isResponse(result)) {\n    let status = result.status;\n\n    // Process redirects\n    if (redirectStatusCodes.has(status)) {\n      let location = result.headers.get(\"Location\");\n      invariant(\n        location,\n        \"Redirects returned/thrown from loaders/actions must have a Location header\"\n      );\n\n      // Support relative routing in internal redirects\n      if (!ABSOLUTE_URL_REGEX.test(location)) {\n        location = normalizeTo(\n          new URL(request.url),\n          matches.slice(0, matches.indexOf(match) + 1),\n          basename,\n          true,\n          location,\n          v7_relativeSplatPath\n        );\n      } else if (!opts.isStaticRequest) {\n        // Strip off the protocol+origin for same-origin + same-basename absolute\n        // redirects. If this is a static request, we can let it go back to the\n        // browser as-is\n        let currentUrl = new URL(request.url);\n        let url = location.startsWith(\"//\")\n          ? new URL(currentUrl.protocol + location)\n          : new URL(location);\n        let isSameBasename = stripBasename(url.pathname, basename) != null;\n        if (url.origin === currentUrl.origin && isSameBasename) {\n          location = url.pathname + url.search + url.hash;\n        }\n      }\n\n      // Don't process redirects in the router during static requests requests.\n      // Instead, throw the Response and let the server handle it with an HTTP\n      // redirect.  We also update the Location header in place in this flow so\n      // basename and relative routing is taken into account\n      if (opts.isStaticRequest) {\n        result.headers.set(\"Location\", location);\n        throw result;\n      }\n\n      return {\n        type: ResultType.redirect,\n        status,\n        location,\n        revalidate: result.headers.get(\"X-Remix-Revalidate\") !== null,\n        reloadDocument: result.headers.get(\"X-Remix-Reload-Document\") !== null,\n      };\n    }\n\n    // For SSR single-route requests, we want to hand Responses back directly\n    // without unwrapping.  We do this with the QueryRouteResponse wrapper\n    // interface so we can know whether it was returned or thrown\n    if (opts.isRouteRequest) {\n      let queryRouteResponse: QueryRouteResponse = {\n        type:\n          resultType === ResultType.error ? ResultType.error : ResultType.data,\n        response: result,\n      };\n      throw queryRouteResponse;\n    }\n\n    let data: any;\n\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      // Check between word boundaries instead of startsWith() due to the last\n      // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data = null;\n        } else {\n          data = await result.json();\n        }\n      } else {\n        data = await result.text();\n      }\n    } catch (e) {\n      return { type: ResultType.error, error: e };\n    }\n\n    if (resultType === ResultType.error) {\n      return {\n        type: resultType,\n        error: new ErrorResponseImpl(status, result.statusText, data),\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (resultType === ResultType.error) {\n    return { type: resultType, error: result };\n  }\n\n  if (isDeferredData(result)) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: result.init?.status,\n      headers: result.init?.headers && new Headers(result.init.headers),\n    };\n  }\n\n  return { type: ResultType.data, data: result };\n}\n\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(\n  history: History,\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({ \"Content-Type\": formEncType });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (\n      formEncType === \"application/x-www-form-urlencoded\" &&\n      submission.formData\n    ) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n\n  return searchParams;\n}\n\nfunction convertSearchParamsToFormData(\n  searchParams: URLSearchParams\n): FormData {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingError: RouteData | undefined,\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n\n  // Process loader results into state.loaderData/state.errors\n  results.forEach((result, index) => {\n    let id = matchesToLoad[index].route.id;\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      // Look upwards from the matched route for the closest ancestor\n      // error boundary, defaulting to the root match\n      let boundaryMatch = findNearestBoundary(matches, id);\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError) {\n        error = Object.values(pendingError)[0];\n        pendingError = undefined;\n      }\n\n      errors = errors || {};\n\n      // Prefer higher error values if lower errors bubble to the same boundary\n      if (errors[boundaryMatch.route.id] == null) {\n        errors[boundaryMatch.route.id] = error;\n      }\n\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n      } else {\n        loaderData[id] = result.data;\n      }\n\n      // Error status codes always override success status codes, but if all\n      // loaders are successful we take the deepest status code.\n      if (\n        result.statusCode != null &&\n        result.statusCode !== 200 &&\n        !foundError\n      ) {\n        statusCode = result.statusCode;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError) {\n    errors = pendingError;\n    loaderData[Object.keys(pendingError)[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingError: RouteData | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: DataResult[],\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    matchesToLoad,\n    results,\n    pendingError,\n    activeDeferreds\n  );\n\n  // Process results from our revalidating fetchers\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let { key, match, controller } = revalidatingFetchers[index];\n    invariant(\n      fetcherResults !== undefined && fetcherResults[index] !== undefined,\n      \"Did not find corresponding fetcher result\"\n    );\n    let result = fetcherResults[index];\n\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      continue;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match?.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[],\n  errors: RouteData | null | undefined\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      } else {\n        // No-op - this is so we ignore existing data if we have a key in the\n        // incoming object with an undefined value, which is how we unset a prior\n        // loaderData if we encounter a loader error\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(routes: AgnosticDataRouteObject[]): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route =\n    routes.length === 1\n      ? routes[0]\n      : routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n          id: `__shim-error-route__`,\n        };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n  };\n}\n\nfunction getInternalRouterError(\n  status: number,\n  {\n    pathname,\n    routeId,\n    method,\n    type,\n  }: {\n    pathname?: string;\n    routeId?: string;\n    method?: string;\n    type?: \"defer-action\" | \"invalid-body\";\n  } = {}\n) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method} request to \"${pathname}\" but ` +\n        `did not provide a \\`loader\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method.toUpperCase()} request to \"${pathname}\" but ` +\n        `did not provide an \\`action\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n\n  return new ErrorResponseImpl(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(\n  results: DataResult[]\n): { result: RedirectResult; idx: number } | undefined {\n  for (let i = results.length - 1; i >= 0; i--) {\n    let result = results[i];\n    if (isRedirectResult(result)) {\n      return { result, idx: i };\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nexport function isDeferredData(value: any): value is DeferredData {\n  let deferred: DeferredData = value;\n  return (\n    deferred &&\n    typeof deferred === \"object\" &&\n    typeof deferred.data === \"object\" &&\n    typeof deferred.subscribe === \"function\" &&\n    typeof deferred.cancel === \"function\" &&\n    typeof deferred.resolveData === \"function\"\n  );\n}\n\nfunction isResponse(value: any): value is Response {\n  return (\n    value != null &&\n    typeof value.status === \"number\" &&\n    typeof value.statusText === \"string\" &&\n    typeof value.headers === \"object\" &&\n    typeof value.body !== \"undefined\"\n  );\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isQueryRouteResponse(obj: any): obj is QueryRouteResponse {\n  return (\n    obj &&\n    isResponse(obj.response) &&\n    (obj.type === ResultType.data || obj.type === ResultType.error)\n  );\n}\n\nfunction isValidMethod(method: string): method is FormMethod | V7_FormMethod {\n  return validRequestMethods.has(method.toLowerCase() as FormMethod);\n}\n\nfunction isMutationMethod(\n  method: string\n): method is MutationFormMethod | V7_MutationFormMethod {\n  return validMutationMethods.has(method.toLowerCase() as MutationFormMethod);\n}\n\nasync function resolveDeferredResults(\n  currentMatches: AgnosticDataRouteMatch[],\n  matchesToLoad: (AgnosticDataRouteMatch | null)[],\n  results: DataResult[],\n  signals: (AbortSignal | null)[],\n  isFetcher: boolean,\n  currentLoaderData?: RouteData\n) {\n  for (let index = 0; index < results.length; index++) {\n    let result = results[index];\n    let match = matchesToLoad[index];\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match!.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && (isFetcher || isRevalidatingLoader)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      let signal = signals[index];\n      invariant(\n        signal,\n        \"Expected an AbortSignal for revalidating fetcher deferred result\"\n      );\n      await resolveDeferredData(result, signal, isFetcher).then((result) => {\n        if (result) {\n          results[index] = result || results[index];\n        }\n      });\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n\nfunction getSubmissionFromNavigation(\n  navigation: Navigation\n): Submission | undefined {\n  let { formMethod, formAction, formEncType, text, formData, json } =\n    navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text,\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined,\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined,\n    };\n  }\n}\n\nfunction getLoadingNavigation(\n  location: Location,\n  submission?: Submission\n): NavigationStates[\"Loading\"] {\n  if (submission) {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n    };\n    return navigation;\n  } else {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n    };\n    return navigation;\n  }\n}\n\nfunction getSubmittingNavigation(\n  location: Location,\n  submission: Submission\n): NavigationStates[\"Submitting\"] {\n  let navigation: NavigationStates[\"Submitting\"] = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n  };\n  return navigation;\n}\n\nfunction getLoadingFetcher(\n  submission?: Submission,\n  data?: Fetcher[\"data\"]\n): FetcherStates[\"Loading\"] {\n  if (submission) {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data,\n    };\n    return fetcher;\n  } else {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data,\n    };\n    return fetcher;\n  }\n}\n\nfunction getSubmittingFetcher(\n  submission: Submission,\n  existingFetcher?: Fetcher\n): FetcherStates[\"Submitting\"] {\n  let fetcher: FetcherStates[\"Submitting\"] = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined,\n  };\n  return fetcher;\n}\n\nfunction getDoneFetcher(data: Fetcher[\"data\"]): FetcherStates[\"Idle\"] {\n  let fetcher: FetcherStates[\"Idle\"] = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data,\n  };\n  return fetcher;\n}\n\nfunction restoreAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(\n      TRANSITIONS_STORAGE_KEY\n    );\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n    // no-op, use default empty object\n  }\n}\n\nfunction persistAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  if (transitions.size > 0) {\n    let json: Record<string, string[]> = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(\n        TRANSITIONS_STORAGE_KEY,\n        JSON.stringify(json)\n      );\n    } catch (error) {\n      warning(\n        false,\n        `Failed to save applied view transitions in sessionStorage (${error}).`\n      );\n    }\n  }\n}\n\n//#endregion\n"], "names": ["Action", "PopStateEventType", "createMemoryHistory", "options", "initialEntries", "initialIndex", "v5Compat", "entries", "map", "entry", "index", "createMemoryLocation", "state", "undefined", "clampIndex", "length", "action", "Pop", "listener", "n", "Math", "min", "max", "getCurrentLocation", "to", "key", "location", "createLocation", "pathname", "warning", "char<PERSON>t", "JSON", "stringify", "createHref", "createPath", "history", "createURL", "URL", "encodeLocation", "path", "parsePath", "search", "hash", "push", "<PERSON><PERSON>", "nextLocation", "splice", "delta", "replace", "Replace", "go", "nextIndex", "listen", "fn", "createBrowserHistory", "createBrowserLocation", "window", "globalHistory", "usr", "createBrowserHref", "getUrlBasedHistory", "createHashHistory", "createHashLocation", "substr", "startsWith", "createHashHref", "base", "document", "querySelector", "href", "getAttribute", "url", "hashIndex", "indexOf", "slice", "validateHashLocation", "invariant", "value", "message", "Error", "cond", "console", "warn", "e", "create<PERSON><PERSON>", "random", "toString", "getHistoryState", "idx", "current", "_extends", "_ref", "parsed<PERSON><PERSON>", "searchIndex", "getLocation", "validateLocation", "defaultView", "getIndex", "replaceState", "handlePop", "historyState", "pushState", "error", "DOMException", "name", "assign", "origin", "addEventListener", "removeEventListener", "ResultType", "immutableRouteKeys", "Set", "isIndexRoute", "route", "convertRoutesToDataRoutes", "routes", "mapRouteProperties", "parentPath", "manifest", "treePath", "id", "join", "children", "indexRoute", "pathOrLayoutRoute", "matchRoutes", "locationArg", "basename", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "matches", "i", "decoded", "decodePath", "matchRouteBranch", "convertRouteMatchToUiMatch", "match", "loaderData", "params", "data", "handle", "parents<PERSON>eta", "flattenRoute", "relativePath", "meta", "caseSensitive", "childrenIndex", "joinPaths", "routesMeta", "concat", "score", "computeScore", "for<PERSON>ach", "_route$path", "includes", "exploded", "explodeOptionalSegments", "segments", "split", "first", "rest", "isOptional", "endsWith", "required", "restExploded", "result", "subpath", "sort", "a", "b", "compareIndexes", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "initialScore", "some", "filter", "reduce", "segment", "test", "siblings", "every", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "matchPath", "Object", "pathnameBase", "normalizePathname", "generatePath", "originalPath", "prefix", "p", "String", "array", "isLastSegment", "star", "keyMatch", "optional", "param", "pattern", "matcher", "compiledParams", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "regexpSource", "_", "RegExp", "v", "decodeURIComponent", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "getInvalidPathError", "char", "field", "dest", "getPathContributingMatches", "getResolveToMatches", "v7_relativeSplatPath", "pathMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "isEmptyPath", "from", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "getToPathname", "paths", "json", "init", "responseInit", "status", "headers", "Headers", "has", "set", "Response", "Aborted<PERSON>eferredError", "DeferredData", "constructor", "pendingKeysSet", "subscribers", "deferred<PERSON><PERSON><PERSON>", "Array", "isArray", "reject", "abortPromise", "Promise", "r", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "signal", "acc", "_ref2", "trackPromise", "done", "add", "promise", "race", "then", "onSettle", "catch", "defineProperty", "get", "aborted", "delete", "undefinedError", "emit", "<PERSON><PERSON><PERSON>", "subscriber", "subscribe", "cancel", "abort", "k", "resolveData", "resolve", "size", "unwrappedData", "_ref3", "unwrapTrackedPromise", "<PERSON><PERSON><PERSON><PERSON>", "isTrackedPromise", "_tracked", "_error", "_data", "defer", "redirect", "redirectDocument", "response", "ErrorResponseImpl", "statusText", "internal", "isRouteErrorResponse", "validMutationMethodsArr", "validMutationMethods", "validRequestMethodsArr", "validRequestMethods", "redirectStatusCodes", "redirectPreserveMethodStatusCodes", "IDLE_NAVIGATION", "formMethod", "formAction", "formEncType", "formData", "text", "IDLE_FETCHER", "IDLE_BLOCKER", "proceed", "reset", "ABSOLUTE_URL_REGEX", "defaultMapRouteProperties", "hasErrorBou<PERSON>ry", "Boolean", "TRANSITIONS_STORAGE_KEY", "createRouter", "routerWindow", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "isServer", "detectErrorBoundary", "dataRoutes", "inFlightDataRoutes", "future", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_partialHydration", "v7_prependBasename", "unlistenHistory", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "hydrationData", "initialMatches", "initialErrors", "getInternalRouterError", "getShortCircuitMatches", "initialized", "hasLazyRoutes", "m", "lazy", "<PERSON><PERSON><PERSON><PERSON>", "loader", "errors", "hydrate", "router", "historyAction", "navigation", "restoreScrollPosition", "preventScrollReset", "revalidation", "actionData", "fetchers", "Map", "blockers", "pendingAction", "HistoryAction", "pendingPreventScrollReset", "pendingNavigationController", "pendingViewTransitionEnabled", "appliedViewTransitions", "removePageHideEventListener", "isUninterruptedRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "fetchRedirectIds", "fetchLoadMatches", "activeFetchers", "deletedFetchers", "activeDeferreds", "blockerFunctions", "ignoreNextHistoryUpdate", "initialize", "blockerKey", "shouldBlockNavigation", "currentLocation", "updateBlocker", "updateState", "startNavigation", "restoreAppliedTransitions", "_saveAppliedTransitions", "persistAppliedTransitions", "initialHydration", "dispose", "clear", "deleteFetcher", "deleteBlocker", "newState", "opts", "completedFetchers", "deletedFetchersKeys", "fetcher", "unstable_viewTransitionOpts", "viewTransitionOpts", "unstable_flushSync", "flushSync", "completeNavigation", "_temp", "_location$state", "_location$state2", "isActionReload", "isMutationMethod", "_isRedirect", "keys", "mergeLoaderData", "priorPaths", "toPaths", "getSavedScrollPosition", "navigate", "normalizedPath", "normalizeTo", "fromRouteId", "relative", "submission", "normalizeNavigateOptions", "userReplace", "pendingError", "enableViewTransition", "unstable_viewTransition", "revalidate", "interruptActiveLoads", "startUninterruptedRevalidation", "overrideNavigation", "saveScrollPosition", "routesToUse", "loadingNavigation", "notFoundMatches", "cancelActiveDeferreds", "isHashChangeOnly", "request", "createClientSideRequest", "pendingActionData", "findNearestBoundary", "actionOutput", "handleAction", "shortCircuited", "pendingActionError", "getLoadingNavigation", "Request", "handleLoaders", "fetcherSubmission", "getSubmittingNavigation", "actionMatch", "getTargetMatch", "type", "method", "routeId", "callLoaderOrAction", "isRedirectResult", "startRedirectNavigation", "isErrorResult", "boundaryMatch", "isDeferredResult", "activeSubmission", "getSubmissionFromNavigation", "matchesToLoad", "revalidatingFetchers", "getMatchesToLoad", "updatedFetchers", "markFetchRedirectsDone", "rf", "revalidatingFetcher", "getLoadingFetcher", "abort<PERSON><PERSON><PERSON>", "abortPendingFetchRevalidations", "f", "results", "loaderResults", "fetcherResults", "callLoadersAndMaybeResolveData", "findRedirect", "fetcher<PERSON>ey", "processLoaderData", "deferredData", "didAbortFetchLoads", "abortStaleFetchLoads", "shouldUpdateFetchers", "fetch", "setFetcherError", "handleFetcherAction", "handleFetcherLoader", "requestMatches", "existingFetcher", "updateFetcherState", "getSubmittingFetcher", "abortController", "fetchRequest", "originatingLoadId", "actionResult", "getDoneFetcher", "revalidationRequest", "loadId", "loadFetcher", "staleKey", "done<PERSON>etcher", "resolveDeferredData", "_temp2", "redirectLocation", "isDocumentReload", "reloadDocument", "redirectHistoryAction", "currentMatches", "fetchersToLoad", "all", "resolveDeferredResults", "getFetcher", "deleteFetcherAndUpdateState", "count", "markFetchersDone", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "get<PERSON><PERSON>er", "blocker", "newBlocker", "blockerFunction", "predicate", "cancelledRouteIds", "dfd", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "y", "getScrollKey", "_internalSetRoutes", "newRoutes", "_internalFetchControllers", "_internalActiveDeferreds", "UNSAFE_DEFERRED_SYMBOL", "Symbol", "createStaticHandler", "v7_throwAbortReason", "query", "_temp3", "requestContext", "isValidMethod", "methodNotAllowedMatches", "statusCode", "loaderHeaders", "actionHeaders", "queryImpl", "isResponse", "queryRoute", "_temp4", "find", "values", "_result$activeDeferre", "routeMatch", "submit", "loadRouteData", "isQueryRouteResponse", "isRedirectResponse", "isRouteRequest", "isStaticRequest", "throwStaticHandlerAbortedError", "Location", "context", "loaderRequest", "getLoaderMatchesUntilBoundary", "processRouteLoaderData", "executedLoaders", "fromEntries", "getStaticContextFromError", "newContext", "_deepestRenderedBoundaryId", "reason", "isSubmissionNavigation", "body", "prependBasename", "contextualMatches", "activeRouteMatch", "hasNakedIndexQuery", "normalizeFormMethod", "isFetcher", "getInvalidBodyError", "rawFormMethod", "toUpperCase", "stripHashFromPath", "FormData", "URLSearchParams", "parse", "searchParams", "convertFormDataToSearchParams", "convertSearchParamsToFormData", "append", "boundaryId", "boundaryMatches", "findIndex", "isInitialLoad", "currentUrl", "nextUrl", "navigationMatches", "is<PERSON>ew<PERSON><PERSON>der", "currentRouteMatch", "nextRouteMatch", "shouldRevalidateLoader", "currentParams", "nextParams", "defaultShouldRevalidate", "isNewRouteInstance", "fetcherMatches", "fetcherMatch", "shouldRevalidate", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "currentPath", "loaderMatch", "arg", "routeChoice", "loadLazyRouteModule", "lazyRoute", "routeToUpdate", "routeUpdates", "lazyRouteProperty", "staticRouteValue", "isPropertyStaticallyDefined", "resultType", "onReject", "<PERSON><PERSON><PERSON><PERSON>", "handler", "handlerError", "protocol", "isSameBasename", "queryRouteResponse", "contentType", "isDeferredData", "_result$init", "_result$init2", "deferred", "found<PERSON><PERSON>r", "newLoaderData", "mergedLoaderData", "hasOwnProperty", "eligibleMatches", "reverse", "_temp5", "errorMessage", "obj", "signals", "isRevalidatingLoader", "unwrap", "getAll", "_window", "transitions", "sessionPositions", "sessionStorage", "getItem", "setItem"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACYA,IAAAA,MAAM,0BAANA,MAAM,EAAA;EAANA,MAAM,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA;EAANA,MAAM,CAAA,MAAA,CAAA,GAAA,MAAA,CAAA;EAANA,MAAM,CAAA,SAAA,CAAA,GAAA,SAAA,CAAA;AAAA,EAAA,OAANA,MAAM,CAAA;AAAA,CAAA,CAAA,EAAA,EAAA;;AAwBlB;AACA;AACA;;AAkBA;AACA;AAEA;AACA;AACA;AACA;AAgBA;AACA;AACA;AAkBA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAgFA,MAAMC,iBAAiB,GAAG,UAAU,CAAA;AACpC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACO,SAASC,mBAAmBA,CACjCC,OAA6B,EACd;AAAA,EAAA,IADfA,OAA6B,KAAA,KAAA,CAAA,EAAA;IAA7BA,OAA6B,GAAG,EAAE,CAAA;AAAA,GAAA;EAElC,IAAI;IAAEC,cAAc,GAAG,CAAC,GAAG,CAAC;IAAEC,YAAY;AAAEC,IAAAA,QAAQ,GAAG,KAAA;AAAM,GAAC,GAAGH,OAAO,CAAA;EACxE,IAAII,OAAmB,CAAC;AACxBA,EAAAA,OAAO,GAAGH,cAAc,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KACxCC,oBAAoB,CAClBF,KAAK,EACL,OAAOA,KAAK,KAAK,QAAQ,GAAG,IAAI,GAAGA,KAAK,CAACG,KAAK,EAC9CF,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGG,SAC5B,CACF,CAAC,CAAA;AACD,EAAA,IAAIH,KAAK,GAAGI,UAAU,CACpBT,YAAY,IAAI,IAAI,GAAGE,OAAO,CAACQ,MAAM,GAAG,CAAC,GAAGV,YAC9C,CAAC,CAAA;AACD,EAAA,IAAIW,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;EACvB,IAAIC,QAAyB,GAAG,IAAI,CAAA;EAEpC,SAASJ,UAAUA,CAACK,CAAS,EAAU;AACrC,IAAA,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,CAAC,EAAE,CAAC,CAAC,EAAEZ,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,CAAA;AACrD,GAAA;EACA,SAASQ,kBAAkBA,GAAa;IACtC,OAAOhB,OAAO,CAACG,KAAK,CAAC,CAAA;AACvB,GAAA;AACA,EAAA,SAASC,oBAAoBA,CAC3Ba,EAAM,EACNZ,KAAU,EACVa,GAAY,EACF;AAAA,IAAA,IAFVb,KAAU,KAAA,KAAA,CAAA,EAAA;AAAVA,MAAAA,KAAU,GAAG,IAAI,CAAA;AAAA,KAAA;AAGjB,IAAA,IAAIc,QAAQ,GAAGC,cAAc,CAC3BpB,OAAO,GAAGgB,kBAAkB,EAAE,CAACK,QAAQ,GAAG,GAAG,EAC7CJ,EAAE,EACFZ,KAAK,EACLa,GACF,CAAC,CAAA;AACDI,IAAAA,OAAO,CACLH,QAAQ,CAACE,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,+DACwBC,IAAI,CAACC,SAAS,CACvER,EACF,CACF,CAAC,CAAA;AACD,IAAA,OAAOE,QAAQ,CAAA;AACjB,GAAA;EAEA,SAASO,UAAUA,CAACT,EAAM,EAAE;IAC1B,OAAO,OAAOA,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAA;AACrD,GAAA;AAEA,EAAA,IAAIW,OAAsB,GAAG;IAC3B,IAAIzB,KAAKA,GAAG;AACV,MAAA,OAAOA,KAAK,CAAA;KACb;IACD,IAAIM,MAAMA,GAAG;AACX,MAAA,OAAOA,MAAM,CAAA;KACd;IACD,IAAIU,QAAQA,GAAG;MACb,OAAOH,kBAAkB,EAAE,CAAA;KAC5B;IACDU,UAAU;IACVG,SAASA,CAACZ,EAAE,EAAE;MACZ,OAAO,IAAIa,GAAG,CAACJ,UAAU,CAACT,EAAE,CAAC,EAAE,kBAAkB,CAAC,CAAA;KACnD;IACDc,cAAcA,CAACd,EAAM,EAAE;AACrB,MAAA,IAAIe,IAAI,GAAG,OAAOf,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE,CAAA;MACtD,OAAO;AACLI,QAAAA,QAAQ,EAAEW,IAAI,CAACX,QAAQ,IAAI,EAAE;AAC7Ba,QAAAA,MAAM,EAAEF,IAAI,CAACE,MAAM,IAAI,EAAE;AACzBC,QAAAA,IAAI,EAAEH,IAAI,CAACG,IAAI,IAAI,EAAA;OACpB,CAAA;KACF;AACDC,IAAAA,IAAIA,CAACnB,EAAE,EAAEZ,KAAK,EAAE;MACdI,MAAM,GAAGhB,MAAM,CAAC4C,IAAI,CAAA;AACpB,MAAA,IAAIC,YAAY,GAAGlC,oBAAoB,CAACa,EAAE,EAAEZ,KAAK,CAAC,CAAA;AAClDF,MAAAA,KAAK,IAAI,CAAC,CAAA;MACVH,OAAO,CAACuC,MAAM,CAACpC,KAAK,EAAEH,OAAO,CAACQ,MAAM,EAAE8B,YAAY,CAAC,CAAA;MACnD,IAAIvC,QAAQ,IAAIY,QAAQ,EAAE;AACxBA,QAAAA,QAAQ,CAAC;UAAEF,MAAM;AAAEU,UAAAA,QAAQ,EAAEmB,YAAY;AAAEE,UAAAA,KAAK,EAAE,CAAA;AAAE,SAAC,CAAC,CAAA;AACxD,OAAA;KACD;AACDC,IAAAA,OAAOA,CAACxB,EAAE,EAAEZ,KAAK,EAAE;MACjBI,MAAM,GAAGhB,MAAM,CAACiD,OAAO,CAAA;AACvB,MAAA,IAAIJ,YAAY,GAAGlC,oBAAoB,CAACa,EAAE,EAAEZ,KAAK,CAAC,CAAA;AAClDL,MAAAA,OAAO,CAACG,KAAK,CAAC,GAAGmC,YAAY,CAAA;MAC7B,IAAIvC,QAAQ,IAAIY,QAAQ,EAAE;AACxBA,QAAAA,QAAQ,CAAC;UAAEF,MAAM;AAAEU,UAAAA,QAAQ,EAAEmB,YAAY;AAAEE,UAAAA,KAAK,EAAE,CAAA;AAAE,SAAC,CAAC,CAAA;AACxD,OAAA;KACD;IACDG,EAAEA,CAACH,KAAK,EAAE;MACR/B,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;AACnB,MAAA,IAAIkC,SAAS,GAAGrC,UAAU,CAACJ,KAAK,GAAGqC,KAAK,CAAC,CAAA;AACzC,MAAA,IAAIF,YAAY,GAAGtC,OAAO,CAAC4C,SAAS,CAAC,CAAA;AACrCzC,MAAAA,KAAK,GAAGyC,SAAS,CAAA;AACjB,MAAA,IAAIjC,QAAQ,EAAE;AACZA,QAAAA,QAAQ,CAAC;UAAEF,MAAM;AAAEU,UAAAA,QAAQ,EAAEmB,YAAY;AAAEE,UAAAA,KAAAA;AAAM,SAAC,CAAC,CAAA;AACrD,OAAA;KACD;IACDK,MAAMA,CAACC,EAAY,EAAE;AACnBnC,MAAAA,QAAQ,GAAGmC,EAAE,CAAA;AACb,MAAA,OAAO,MAAM;AACXnC,QAAAA,QAAQ,GAAG,IAAI,CAAA;OAChB,CAAA;AACH,KAAA;GACD,CAAA;AAED,EAAA,OAAOiB,OAAO,CAAA;AAChB,CAAA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASmB,oBAAoBA,CAClCnD,OAA8B,EACd;AAAA,EAAA,IADhBA,OAA8B,KAAA,KAAA,CAAA,EAAA;IAA9BA,OAA8B,GAAG,EAAE,CAAA;AAAA,GAAA;AAEnC,EAAA,SAASoD,qBAAqBA,CAC5BC,MAAc,EACdC,aAAgC,EAChC;IACA,IAAI;MAAE7B,QAAQ;MAAEa,MAAM;AAAEC,MAAAA,IAAAA;KAAM,GAAGc,MAAM,CAAC9B,QAAQ,CAAA;IAChD,OAAOC,cAAc,CACnB,EAAE,EACF;MAAEC,QAAQ;MAAEa,MAAM;AAAEC,MAAAA,IAAAA;KAAM;AAC1B;IACCe,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAAC8C,GAAG,IAAK,IAAI,EACvDD,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAACa,GAAG,IAAK,SACtD,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,SAASkC,iBAAiBA,CAACH,MAAc,EAAEhC,EAAM,EAAE;IACjD,OAAO,OAAOA,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAA;AACrD,GAAA;EAEA,OAAOoC,kBAAkB,CACvBL,qBAAqB,EACrBI,iBAAiB,EACjB,IAAI,EACJxD,OACF,CAAC,CAAA;AACH,CAAA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS0D,iBAAiBA,CAC/B1D,OAA2B,EACd;AAAA,EAAA,IADbA,OAA2B,KAAA,KAAA,CAAA,EAAA;IAA3BA,OAA2B,GAAG,EAAE,CAAA;AAAA,GAAA;AAEhC,EAAA,SAAS2D,kBAAkBA,CACzBN,MAAc,EACdC,aAAgC,EAChC;IACA,IAAI;AACF7B,MAAAA,QAAQ,GAAG,GAAG;AACda,MAAAA,MAAM,GAAG,EAAE;AACXC,MAAAA,IAAI,GAAG,EAAA;AACT,KAAC,GAAGF,SAAS,CAACgB,MAAM,CAAC9B,QAAQ,CAACgB,IAAI,CAACqB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAI,CAACnC,QAAQ,CAACoC,UAAU,CAAC,GAAG,CAAC,IAAI,CAACpC,QAAQ,CAACoC,UAAU,CAAC,GAAG,CAAC,EAAE;MAC1DpC,QAAQ,GAAG,GAAG,GAAGA,QAAQ,CAAA;AAC3B,KAAA;IAEA,OAAOD,cAAc,CACnB,EAAE,EACF;MAAEC,QAAQ;MAAEa,MAAM;AAAEC,MAAAA,IAAAA;KAAM;AAC1B;IACCe,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAAC8C,GAAG,IAAK,IAAI,EACvDD,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAACa,GAAG,IAAK,SACtD,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,SAASwC,cAAcA,CAACT,MAAc,EAAEhC,EAAM,EAAE;IAC9C,IAAI0C,IAAI,GAAGV,MAAM,CAACW,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAAA;IAChD,IAAIC,IAAI,GAAG,EAAE,CAAA;IAEb,IAAIH,IAAI,IAAIA,IAAI,CAACI,YAAY,CAAC,MAAM,CAAC,EAAE;AACrC,MAAA,IAAIC,GAAG,GAAGf,MAAM,CAAC9B,QAAQ,CAAC2C,IAAI,CAAA;AAC9B,MAAA,IAAIG,SAAS,GAAGD,GAAG,CAACE,OAAO,CAAC,GAAG,CAAC,CAAA;AAChCJ,MAAAA,IAAI,GAAGG,SAAS,KAAK,CAAC,CAAC,GAAGD,GAAG,GAAGA,GAAG,CAACG,KAAK,CAAC,CAAC,EAAEF,SAAS,CAAC,CAAA;AACzD,KAAA;AAEA,IAAA,OAAOH,IAAI,GAAG,GAAG,IAAI,OAAO7C,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAC,CAAA;AACpE,GAAA;AAEA,EAAA,SAASmD,oBAAoBA,CAACjD,QAAkB,EAAEF,EAAM,EAAE;AACxDK,IAAAA,OAAO,CACLH,QAAQ,CAACE,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAA,4DAAA,GAC0BC,IAAI,CAACC,SAAS,CACzER,EACF,CAAC,MACH,CAAC,CAAA;AACH,GAAA;EAEA,OAAOoC,kBAAkB,CACvBE,kBAAkB,EAClBG,cAAc,EACdU,oBAAoB,EACpBxE,OACF,CAAC,CAAA;AACH,CAAA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AAMO,SAASyE,SAASA,CAACC,KAAU,EAAEC,OAAgB,EAAE;AACtD,EAAA,IAAID,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;AACrE,IAAA,MAAM,IAAIE,KAAK,CAACD,OAAO,CAAC,CAAA;AAC1B,GAAA;AACF,CAAA;AAEO,SAASjD,OAAOA,CAACmD,IAAS,EAAEF,OAAe,EAAE;EAClD,IAAI,CAACE,IAAI,EAAE;AACT;IACA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAEA,OAAO,CAACC,IAAI,CAACJ,OAAO,CAAC,CAAA;IAEzD,IAAI;AACF;AACA;AACA;AACA;AACA;AACA,MAAA,MAAM,IAAIC,KAAK,CAACD,OAAO,CAAC,CAAA;AACxB;AACF,KAAC,CAAC,OAAOK,CAAC,EAAE,EAAC;AACf,GAAA;AACF,CAAA;AAEA,SAASC,SAASA,GAAG;AACnB,EAAA,OAAOhE,IAAI,CAACiE,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACvB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAChD,CAAA;;AAEA;AACA;AACA;AACA,SAASwB,eAAeA,CAAC7D,QAAkB,EAAEhB,KAAa,EAAgB;EACxE,OAAO;IACLgD,GAAG,EAAEhC,QAAQ,CAACd,KAAK;IACnBa,GAAG,EAAEC,QAAQ,CAACD,GAAG;AACjB+D,IAAAA,GAAG,EAAE9E,KAAAA;GACN,CAAA;AACH,CAAA;;AAEA;AACA;AACA;AACO,SAASiB,cAAcA,CAC5B8D,OAA0B,EAC1BjE,EAAM,EACNZ,KAAU,EACVa,GAAY,EACQ;AAAA,EAAA,IAFpBb,KAAU,KAAA,KAAA,CAAA,EAAA;AAAVA,IAAAA,KAAU,GAAG,IAAI,CAAA;AAAA,GAAA;EAGjB,IAAIc,QAA4B,GAAAgE,QAAA,CAAA;IAC9B9D,QAAQ,EAAE,OAAO6D,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGA,OAAO,CAAC7D,QAAQ;AAClEa,IAAAA,MAAM,EAAE,EAAE;AACVC,IAAAA,IAAI,EAAE,EAAA;GACF,EAAA,OAAOlB,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE,EAAA;IAC/CZ,KAAK;AACL;AACA;AACA;AACA;IACAa,GAAG,EAAGD,EAAE,IAAKA,EAAE,CAAcC,GAAG,IAAKA,GAAG,IAAI2D,SAAS,EAAC;GACvD,CAAA,CAAA;AACD,EAAA,OAAO1D,QAAQ,CAAA;AACjB,CAAA;;AAEA;AACA;AACA;AACO,SAASQ,UAAUA,CAAAyD,IAAA,EAIR;EAAA,IAJS;AACzB/D,IAAAA,QAAQ,GAAG,GAAG;AACda,IAAAA,MAAM,GAAG,EAAE;AACXC,IAAAA,IAAI,GAAG,EAAA;AACM,GAAC,GAAAiD,IAAA,CAAA;EACd,IAAIlD,MAAM,IAAIA,MAAM,KAAK,GAAG,EAC1Bb,QAAQ,IAAIa,MAAM,CAACX,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGW,MAAM,GAAG,GAAG,GAAGA,MAAM,CAAA;EAC9D,IAAIC,IAAI,IAAIA,IAAI,KAAK,GAAG,EACtBd,QAAQ,IAAIc,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGY,IAAI,GAAG,GAAG,GAAGA,IAAI,CAAA;AACxD,EAAA,OAAOd,QAAQ,CAAA;AACjB,CAAA;;AAEA;AACA;AACA;AACO,SAASY,SAASA,CAACD,IAAY,EAAiB;EACrD,IAAIqD,UAAyB,GAAG,EAAE,CAAA;AAElC,EAAA,IAAIrD,IAAI,EAAE;AACR,IAAA,IAAIiC,SAAS,GAAGjC,IAAI,CAACkC,OAAO,CAAC,GAAG,CAAC,CAAA;IACjC,IAAID,SAAS,IAAI,CAAC,EAAE;MAClBoB,UAAU,CAAClD,IAAI,GAAGH,IAAI,CAACwB,MAAM,CAACS,SAAS,CAAC,CAAA;MACxCjC,IAAI,GAAGA,IAAI,CAACwB,MAAM,CAAC,CAAC,EAAES,SAAS,CAAC,CAAA;AAClC,KAAA;AAEA,IAAA,IAAIqB,WAAW,GAAGtD,IAAI,CAACkC,OAAO,CAAC,GAAG,CAAC,CAAA;IACnC,IAAIoB,WAAW,IAAI,CAAC,EAAE;MACpBD,UAAU,CAACnD,MAAM,GAAGF,IAAI,CAACwB,MAAM,CAAC8B,WAAW,CAAC,CAAA;MAC5CtD,IAAI,GAAGA,IAAI,CAACwB,MAAM,CAAC,CAAC,EAAE8B,WAAW,CAAC,CAAA;AACpC,KAAA;AAEA,IAAA,IAAItD,IAAI,EAAE;MACRqD,UAAU,CAAChE,QAAQ,GAAGW,IAAI,CAAA;AAC5B,KAAA;AACF,GAAA;AAEA,EAAA,OAAOqD,UAAU,CAAA;AACnB,CAAA;AASA,SAAShC,kBAAkBA,CACzBkC,WAA2E,EAC3E7D,UAA8C,EAC9C8D,gBAA+D,EAC/D5F,OAA0B,EACd;AAAA,EAAA,IADZA,OAA0B,KAAA,KAAA,CAAA,EAAA;IAA1BA,OAA0B,GAAG,EAAE,CAAA;AAAA,GAAA;EAE/B,IAAI;IAAEqD,MAAM,GAAGW,QAAQ,CAAC6B,WAAY;AAAE1F,IAAAA,QAAQ,GAAG,KAAA;AAAM,GAAC,GAAGH,OAAO,CAAA;AAClE,EAAA,IAAIsD,aAAa,GAAGD,MAAM,CAACrB,OAAO,CAAA;AAClC,EAAA,IAAInB,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;EACvB,IAAIC,QAAyB,GAAG,IAAI,CAAA;AAEpC,EAAA,IAAIR,KAAK,GAAGuF,QAAQ,EAAG,CAAA;AACvB;AACA;AACA;EACA,IAAIvF,KAAK,IAAI,IAAI,EAAE;AACjBA,IAAAA,KAAK,GAAG,CAAC,CAAA;AACT+C,IAAAA,aAAa,CAACyC,YAAY,CAAAR,QAAA,CAAMjC,EAAAA,EAAAA,aAAa,CAAC7C,KAAK,EAAA;AAAE4E,MAAAA,GAAG,EAAE9E,KAAAA;AAAK,KAAA,CAAA,EAAI,EAAE,CAAC,CAAA;AACxE,GAAA;EAEA,SAASuF,QAAQA,GAAW;AAC1B,IAAA,IAAIrF,KAAK,GAAG6C,aAAa,CAAC7C,KAAK,IAAI;AAAE4E,MAAAA,GAAG,EAAE,IAAA;KAAM,CAAA;IAChD,OAAO5E,KAAK,CAAC4E,GAAG,CAAA;AAClB,GAAA;EAEA,SAASW,SAASA,GAAG;IACnBnF,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;AACnB,IAAA,IAAIkC,SAAS,GAAG8C,QAAQ,EAAE,CAAA;IAC1B,IAAIlD,KAAK,GAAGI,SAAS,IAAI,IAAI,GAAG,IAAI,GAAGA,SAAS,GAAGzC,KAAK,CAAA;AACxDA,IAAAA,KAAK,GAAGyC,SAAS,CAAA;AACjB,IAAA,IAAIjC,QAAQ,EAAE;AACZA,MAAAA,QAAQ,CAAC;QAAEF,MAAM;QAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;AAAEqB,QAAAA,KAAAA;AAAM,OAAC,CAAC,CAAA;AACzD,KAAA;AACF,GAAA;AAEA,EAAA,SAASJ,IAAIA,CAACnB,EAAM,EAAEZ,KAAW,EAAE;IACjCI,MAAM,GAAGhB,MAAM,CAAC4C,IAAI,CAAA;IACpB,IAAIlB,QAAQ,GAAGC,cAAc,CAACQ,OAAO,CAACT,QAAQ,EAAEF,EAAE,EAAEZ,KAAK,CAAC,CAAA;AAC1D,IAAA,IAAImF,gBAAgB,EAAEA,gBAAgB,CAACrE,QAAQ,EAAEF,EAAE,CAAC,CAAA;AAEpDd,IAAAA,KAAK,GAAGuF,QAAQ,EAAE,GAAG,CAAC,CAAA;AACtB,IAAA,IAAIG,YAAY,GAAGb,eAAe,CAAC7D,QAAQ,EAAEhB,KAAK,CAAC,CAAA;AACnD,IAAA,IAAI6D,GAAG,GAAGpC,OAAO,CAACF,UAAU,CAACP,QAAQ,CAAC,CAAA;;AAEtC;IACA,IAAI;MACF+B,aAAa,CAAC4C,SAAS,CAACD,YAAY,EAAE,EAAE,EAAE7B,GAAG,CAAC,CAAA;KAC/C,CAAC,OAAO+B,KAAK,EAAE;AACd;AACA;AACA;AACA;MACA,IAAIA,KAAK,YAAYC,YAAY,IAAID,KAAK,CAACE,IAAI,KAAK,gBAAgB,EAAE;AACpE,QAAA,MAAMF,KAAK,CAAA;AACb,OAAA;AACA;AACA;AACA9C,MAAAA,MAAM,CAAC9B,QAAQ,CAAC+E,MAAM,CAAClC,GAAG,CAAC,CAAA;AAC7B,KAAA;IAEA,IAAIjE,QAAQ,IAAIY,QAAQ,EAAE;AACxBA,MAAAA,QAAQ,CAAC;QAAEF,MAAM;QAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;AAAEqB,QAAAA,KAAK,EAAE,CAAA;AAAE,OAAC,CAAC,CAAA;AAC5D,KAAA;AACF,GAAA;AAEA,EAAA,SAASC,OAAOA,CAACxB,EAAM,EAAEZ,KAAW,EAAE;IACpCI,MAAM,GAAGhB,MAAM,CAACiD,OAAO,CAAA;IACvB,IAAIvB,QAAQ,GAAGC,cAAc,CAACQ,OAAO,CAACT,QAAQ,EAAEF,EAAE,EAAEZ,KAAK,CAAC,CAAA;AAC1D,IAAA,IAAImF,gBAAgB,EAAEA,gBAAgB,CAACrE,QAAQ,EAAEF,EAAE,CAAC,CAAA;IAEpDd,KAAK,GAAGuF,QAAQ,EAAE,CAAA;AAClB,IAAA,IAAIG,YAAY,GAAGb,eAAe,CAAC7D,QAAQ,EAAEhB,KAAK,CAAC,CAAA;AACnD,IAAA,IAAI6D,GAAG,GAAGpC,OAAO,CAACF,UAAU,CAACP,QAAQ,CAAC,CAAA;IACtC+B,aAAa,CAACyC,YAAY,CAACE,YAAY,EAAE,EAAE,EAAE7B,GAAG,CAAC,CAAA;IAEjD,IAAIjE,QAAQ,IAAIY,QAAQ,EAAE;AACxBA,MAAAA,QAAQ,CAAC;QAAEF,MAAM;QAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;AAAEqB,QAAAA,KAAK,EAAE,CAAA;AAAE,OAAC,CAAC,CAAA;AAC5D,KAAA;AACF,GAAA;EAEA,SAASX,SAASA,CAACZ,EAAM,EAAO;AAC9B;AACA;AACA;IACA,IAAI0C,IAAI,GACNV,MAAM,CAAC9B,QAAQ,CAACgF,MAAM,KAAK,MAAM,GAC7BlD,MAAM,CAAC9B,QAAQ,CAACgF,MAAM,GACtBlD,MAAM,CAAC9B,QAAQ,CAAC2C,IAAI,CAAA;AAE1B,IAAA,IAAIA,IAAI,GAAG,OAAO7C,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAA;AACvD;AACA;AACA;IACA6C,IAAI,GAAGA,IAAI,CAACrB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;AAChC4B,IAAAA,SAAS,CACPV,IAAI,EACkEG,qEAAAA,GAAAA,IACxE,CAAC,CAAA;AACD,IAAA,OAAO,IAAIhC,GAAG,CAACgC,IAAI,EAAEH,IAAI,CAAC,CAAA;AAC5B,GAAA;AAEA,EAAA,IAAI/B,OAAgB,GAAG;IACrB,IAAInB,MAAMA,GAAG;AACX,MAAA,OAAOA,MAAM,CAAA;KACd;IACD,IAAIU,QAAQA,GAAG;AACb,MAAA,OAAOoE,WAAW,CAACtC,MAAM,EAAEC,aAAa,CAAC,CAAA;KAC1C;IACDL,MAAMA,CAACC,EAAY,EAAE;AACnB,MAAA,IAAInC,QAAQ,EAAE;AACZ,QAAA,MAAM,IAAI6D,KAAK,CAAC,4CAA4C,CAAC,CAAA;AAC/D,OAAA;AACAvB,MAAAA,MAAM,CAACmD,gBAAgB,CAAC1G,iBAAiB,EAAEkG,SAAS,CAAC,CAAA;AACrDjF,MAAAA,QAAQ,GAAGmC,EAAE,CAAA;AAEb,MAAA,OAAO,MAAM;AACXG,QAAAA,MAAM,CAACoD,mBAAmB,CAAC3G,iBAAiB,EAAEkG,SAAS,CAAC,CAAA;AACxDjF,QAAAA,QAAQ,GAAG,IAAI,CAAA;OAChB,CAAA;KACF;IACDe,UAAUA,CAACT,EAAE,EAAE;AACb,MAAA,OAAOS,UAAU,CAACuB,MAAM,EAAEhC,EAAE,CAAC,CAAA;KAC9B;IACDY,SAAS;IACTE,cAAcA,CAACd,EAAE,EAAE;AACjB;AACA,MAAA,IAAI+C,GAAG,GAAGnC,SAAS,CAACZ,EAAE,CAAC,CAAA;MACvB,OAAO;QACLI,QAAQ,EAAE2C,GAAG,CAAC3C,QAAQ;QACtBa,MAAM,EAAE8B,GAAG,CAAC9B,MAAM;QAClBC,IAAI,EAAE6B,GAAG,CAAC7B,IAAAA;OACX,CAAA;KACF;IACDC,IAAI;IACJK,OAAO;IACPE,EAAEA,CAAC/B,CAAC,EAAE;AACJ,MAAA,OAAOsC,aAAa,CAACP,EAAE,CAAC/B,CAAC,CAAC,CAAA;AAC5B,KAAA;GACD,CAAA;AAED,EAAA,OAAOgB,OAAO,CAAA;AAChB,CAAA;;AAEA;;ACtuBA;AACA;AACA;;AAKY0E,IAAAA,UAAU,0BAAVA,UAAU,EAAA;EAAVA,UAAU,CAAA,MAAA,CAAA,GAAA,MAAA,CAAA;EAAVA,UAAU,CAAA,UAAA,CAAA,GAAA,UAAA,CAAA;EAAVA,UAAU,CAAA,UAAA,CAAA,GAAA,UAAA,CAAA;EAAVA,UAAU,CAAA,OAAA,CAAA,GAAA,OAAA,CAAA;AAAA,EAAA,OAAVA,UAAU,CAAA;AAAA,CAAA,CAAA,EAAA,CAAA,CAAA;;AAOtB;AACA;AACA;;AAQA;AACA;AACA;;AAQA;AACA;AACA;;AASA;AACA;AACA;;AAOA;AACA;AACA;;AAUA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA;;AAUA;;AAQA;AACA;AACA;AACA;AACA;;AA2BA;AACA;AACA;AACA;AACA;;AAOA;AACA;AACA;AAEA;AACA;AACA;AAIA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAOA;AACA;AACA;AAOA;AACA;AACA;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AASO,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAoB,CAC3D,MAAM,EACN,eAAe,EACf,MAAM,EACN,IAAI,EACJ,OAAO,EACP,UAAU,CACX,CAAC,CAAA;;AASF;AACA;AACA;AACA;;AAKA;AACA;AACA;;AAaA;AACA;AACA;;AAMA;AACA;AACA;;AAMA;AACA;AACA;AACA;;AAcA;AACA;AACA;;AAOA;;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAWA;AACA;AACA;AAKA;AACA;AACA;AAKA;AACA;AACA;AA0BA,SAASC,YAAYA,CACnBC,KAA0B,EACS;AACnC,EAAA,OAAOA,KAAK,CAACvG,KAAK,KAAK,IAAI,CAAA;AAC7B,CAAA;;AAEA;AACA;AACO,SAASwG,yBAAyBA,CACvCC,MAA6B,EAC7BC,kBAA8C,EAC9CC,UAAoB,EACpBC,QAAuB,EACI;AAAA,EAAA,IAF3BD,UAAoB,KAAA,KAAA,CAAA,EAAA;AAApBA,IAAAA,UAAoB,GAAG,EAAE,CAAA;AAAA,GAAA;AAAA,EAAA,IACzBC,QAAuB,KAAA,KAAA,CAAA,EAAA;IAAvBA,QAAuB,GAAG,EAAE,CAAA;AAAA,GAAA;EAE5B,OAAOH,MAAM,CAAC3G,GAAG,CAAC,CAACyG,KAAK,EAAEvG,KAAK,KAAK;AAClC,IAAA,IAAI6G,QAAQ,GAAG,CAAC,GAAGF,UAAU,EAAE3G,KAAK,CAAC,CAAA;AACrC,IAAA,IAAI8G,EAAE,GAAG,OAAOP,KAAK,CAACO,EAAE,KAAK,QAAQ,GAAGP,KAAK,CAACO,EAAE,GAAGD,QAAQ,CAACE,IAAI,CAAC,GAAG,CAAC,CAAA;AACrE7C,IAAAA,SAAS,CACPqC,KAAK,CAACvG,KAAK,KAAK,IAAI,IAAI,CAACuG,KAAK,CAACS,QAAQ,EAAA,2CAEzC,CAAC,CAAA;IACD9C,SAAS,CACP,CAAC0C,QAAQ,CAACE,EAAE,CAAC,EACb,qCAAqCA,GAAAA,EAAE,GACrC,aAAA,GAAA,wDACJ,CAAC,CAAA;AAED,IAAA,IAAIR,YAAY,CAACC,KAAK,CAAC,EAAE;MACvB,IAAIU,UAAwC,GAAAjC,QAAA,CAAA,EAAA,EACvCuB,KAAK,EACLG,kBAAkB,CAACH,KAAK,CAAC,EAAA;AAC5BO,QAAAA,EAAAA;OACD,CAAA,CAAA;AACDF,MAAAA,QAAQ,CAACE,EAAE,CAAC,GAAGG,UAAU,CAAA;AACzB,MAAA,OAAOA,UAAU,CAAA;AACnB,KAAC,MAAM;MACL,IAAIC,iBAAkD,GAAAlC,QAAA,CAAA,EAAA,EACjDuB,KAAK,EACLG,kBAAkB,CAACH,KAAK,CAAC,EAAA;QAC5BO,EAAE;AACFE,QAAAA,QAAQ,EAAE7G,SAAAA;OACX,CAAA,CAAA;AACDyG,MAAAA,QAAQ,CAACE,EAAE,CAAC,GAAGI,iBAAiB,CAAA;MAEhC,IAAIX,KAAK,CAACS,QAAQ,EAAE;AAClBE,QAAAA,iBAAiB,CAACF,QAAQ,GAAGR,yBAAyB,CACpDD,KAAK,CAACS,QAAQ,EACdN,kBAAkB,EAClBG,QAAQ,EACRD,QACF,CAAC,CAAA;AACH,OAAA;AAEA,MAAA,OAAOM,iBAAiB,CAAA;AAC1B,KAAA;AACF,GAAC,CAAC,CAAA;AACJ,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,WAAWA,CAGzBV,MAAyB,EACzBW,WAAuC,EACvCC,QAAQ,EAC8C;AAAA,EAAA,IADtDA,QAAQ,KAAA,KAAA,CAAA,EAAA;AAARA,IAAAA,QAAQ,GAAG,GAAG,CAAA;AAAA,GAAA;AAEd,EAAA,IAAIrG,QAAQ,GACV,OAAOoG,WAAW,KAAK,QAAQ,GAAGtF,SAAS,CAACsF,WAAW,CAAC,GAAGA,WAAW,CAAA;EAExE,IAAIlG,QAAQ,GAAGoG,aAAa,CAACtG,QAAQ,CAACE,QAAQ,IAAI,GAAG,EAAEmG,QAAQ,CAAC,CAAA;EAEhE,IAAInG,QAAQ,IAAI,IAAI,EAAE;AACpB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA,EAAA,IAAIqG,QAAQ,GAAGC,aAAa,CAACf,MAAM,CAAC,CAAA;EACpCgB,iBAAiB,CAACF,QAAQ,CAAC,CAAA;EAE3B,IAAIG,OAAO,GAAG,IAAI,CAAA;AAClB,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAED,OAAO,IAAI,IAAI,IAAIC,CAAC,GAAGJ,QAAQ,CAAClH,MAAM,EAAE,EAAEsH,CAAC,EAAE;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAIC,OAAO,GAAGC,UAAU,CAAC3G,QAAQ,CAAC,CAAA;IAClCwG,OAAO,GAAGI,gBAAgB,CAA0BP,QAAQ,CAACI,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAA;AAC3E,GAAA;AAEA,EAAA,OAAOF,OAAO,CAAA;AAChB,CAAA;AAUO,SAASK,0BAA0BA,CACxCC,KAA6B,EAC7BC,UAAqB,EACZ;EACT,IAAI;IAAE1B,KAAK;IAAErF,QAAQ;AAAEgH,IAAAA,MAAAA;AAAO,GAAC,GAAGF,KAAK,CAAA;EACvC,OAAO;IACLlB,EAAE,EAAEP,KAAK,CAACO,EAAE;IACZ5F,QAAQ;IACRgH,MAAM;AACNC,IAAAA,IAAI,EAAEF,UAAU,CAAC1B,KAAK,CAACO,EAAE,CAAC;IAC1BsB,MAAM,EAAE7B,KAAK,CAAC6B,MAAAA;GACf,CAAA;AACH,CAAA;AAmBA,SAASZ,aAAaA,CAGpBf,MAAyB,EACzBc,QAAwC,EACxCc,WAAyC,EACzC1B,UAAU,EACsB;AAAA,EAAA,IAHhCY,QAAwC,KAAA,KAAA,CAAA,EAAA;AAAxCA,IAAAA,QAAwC,GAAG,EAAE,CAAA;AAAA,GAAA;AAAA,EAAA,IAC7Cc,WAAyC,KAAA,KAAA,CAAA,EAAA;AAAzCA,IAAAA,WAAyC,GAAG,EAAE,CAAA;AAAA,GAAA;AAAA,EAAA,IAC9C1B,UAAU,KAAA,KAAA,CAAA,EAAA;AAAVA,IAAAA,UAAU,GAAG,EAAE,CAAA;AAAA,GAAA;EAEf,IAAI2B,YAAY,GAAGA,CACjB/B,KAAsB,EACtBvG,KAAa,EACbuI,YAAqB,KAClB;AACH,IAAA,IAAIC,IAAgC,GAAG;MACrCD,YAAY,EACVA,YAAY,KAAKpI,SAAS,GAAGoG,KAAK,CAAC1E,IAAI,IAAI,EAAE,GAAG0G,YAAY;AAC9DE,MAAAA,aAAa,EAAElC,KAAK,CAACkC,aAAa,KAAK,IAAI;AAC3CC,MAAAA,aAAa,EAAE1I,KAAK;AACpBuG,MAAAA,KAAAA;KACD,CAAA;IAED,IAAIiC,IAAI,CAACD,YAAY,CAACjF,UAAU,CAAC,GAAG,CAAC,EAAE;AACrCY,MAAAA,SAAS,CACPsE,IAAI,CAACD,YAAY,CAACjF,UAAU,CAACqD,UAAU,CAAC,EACxC,wBAAA,GAAwB6B,IAAI,CAACD,YAAY,qCACnC5B,UAAU,GAAA,gDAAA,CAA+C,gEAEjE,CAAC,CAAA;AAED6B,MAAAA,IAAI,CAACD,YAAY,GAAGC,IAAI,CAACD,YAAY,CAACvE,KAAK,CAAC2C,UAAU,CAACtG,MAAM,CAAC,CAAA;AAChE,KAAA;IAEA,IAAIwB,IAAI,GAAG8G,SAAS,CAAC,CAAChC,UAAU,EAAE6B,IAAI,CAACD,YAAY,CAAC,CAAC,CAAA;AACrD,IAAA,IAAIK,UAAU,GAAGP,WAAW,CAACQ,MAAM,CAACL,IAAI,CAAC,CAAA;;AAEzC;AACA;AACA;IACA,IAAIjC,KAAK,CAACS,QAAQ,IAAIT,KAAK,CAACS,QAAQ,CAAC3G,MAAM,GAAG,CAAC,EAAE;MAC/C6D,SAAS;AACP;AACA;MACAqC,KAAK,CAACvG,KAAK,KAAK,IAAI,EACpB,yDACuC6B,IAAAA,qCAAAA,GAAAA,IAAI,SAC7C,CAAC,CAAA;MAED2F,aAAa,CAACjB,KAAK,CAACS,QAAQ,EAAEO,QAAQ,EAAEqB,UAAU,EAAE/G,IAAI,CAAC,CAAA;AAC3D,KAAA;;AAEA;AACA;IACA,IAAI0E,KAAK,CAAC1E,IAAI,IAAI,IAAI,IAAI,CAAC0E,KAAK,CAACvG,KAAK,EAAE;AACtC,MAAA,OAAA;AACF,KAAA;IAEAuH,QAAQ,CAACtF,IAAI,CAAC;MACZJ,IAAI;MACJiH,KAAK,EAAEC,YAAY,CAAClH,IAAI,EAAE0E,KAAK,CAACvG,KAAK,CAAC;AACtC4I,MAAAA,UAAAA;AACF,KAAC,CAAC,CAAA;GACH,CAAA;AACDnC,EAAAA,MAAM,CAACuC,OAAO,CAAC,CAACzC,KAAK,EAAEvG,KAAK,KAAK;AAAA,IAAA,IAAAiJ,WAAA,CAAA;AAC/B;AACA,IAAA,IAAI1C,KAAK,CAAC1E,IAAI,KAAK,EAAE,IAAI,GAAAoH,WAAA,GAAC1C,KAAK,CAAC1E,IAAI,aAAVoH,WAAA,CAAYC,QAAQ,CAAC,GAAG,CAAC,CAAE,EAAA;AACnDZ,MAAAA,YAAY,CAAC/B,KAAK,EAAEvG,KAAK,CAAC,CAAA;AAC5B,KAAC,MAAM;MACL,KAAK,IAAImJ,QAAQ,IAAIC,uBAAuB,CAAC7C,KAAK,CAAC1E,IAAI,CAAC,EAAE;AACxDyG,QAAAA,YAAY,CAAC/B,KAAK,EAAEvG,KAAK,EAAEmJ,QAAQ,CAAC,CAAA;AACtC,OAAA;AACF,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,OAAO5B,QAAQ,CAAA;AACjB,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6B,uBAAuBA,CAACvH,IAAY,EAAY;AACvD,EAAA,IAAIwH,QAAQ,GAAGxH,IAAI,CAACyH,KAAK,CAAC,GAAG,CAAC,CAAA;AAC9B,EAAA,IAAID,QAAQ,CAAChJ,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE,CAAA;AAEpC,EAAA,IAAI,CAACkJ,KAAK,EAAE,GAAGC,IAAI,CAAC,GAAGH,QAAQ,CAAA;;AAE/B;AACA,EAAA,IAAII,UAAU,GAAGF,KAAK,CAACG,QAAQ,CAAC,GAAG,CAAC,CAAA;AACpC;EACA,IAAIC,QAAQ,GAAGJ,KAAK,CAACjH,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;AAEvC,EAAA,IAAIkH,IAAI,CAACnJ,MAAM,KAAK,CAAC,EAAE;AACrB;AACA;IACA,OAAOoJ,UAAU,GAAG,CAACE,QAAQ,EAAE,EAAE,CAAC,GAAG,CAACA,QAAQ,CAAC,CAAA;AACjD,GAAA;EAEA,IAAIC,YAAY,GAAGR,uBAAuB,CAACI,IAAI,CAACzC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;EAE1D,IAAI8C,MAAgB,GAAG,EAAE,CAAA;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;EACAA,MAAM,CAAC5H,IAAI,CACT,GAAG2H,YAAY,CAAC9J,GAAG,CAAEgK,OAAO,IAC1BA,OAAO,KAAK,EAAE,GAAGH,QAAQ,GAAG,CAACA,QAAQ,EAAEG,OAAO,CAAC,CAAC/C,IAAI,CAAC,GAAG,CAC1D,CACF,CAAC,CAAA;;AAED;AACA,EAAA,IAAI0C,UAAU,EAAE;AACdI,IAAAA,MAAM,CAAC5H,IAAI,CAAC,GAAG2H,YAAY,CAAC,CAAA;AAC9B,GAAA;;AAEA;EACA,OAAOC,MAAM,CAAC/J,GAAG,CAAEqJ,QAAQ,IACzBtH,IAAI,CAACyB,UAAU,CAAC,GAAG,CAAC,IAAI6F,QAAQ,KAAK,EAAE,GAAG,GAAG,GAAGA,QAClD,CAAC,CAAA;AACH,CAAA;AAEA,SAAS1B,iBAAiBA,CAACF,QAAuB,EAAQ;EACxDA,QAAQ,CAACwC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACjBD,CAAC,CAAClB,KAAK,KAAKmB,CAAC,CAACnB,KAAK,GACfmB,CAAC,CAACnB,KAAK,GAAGkB,CAAC,CAAClB,KAAK;AAAC,IAClBoB,cAAc,CACZF,CAAC,CAACpB,UAAU,CAAC9I,GAAG,CAAE0I,IAAI,IAAKA,IAAI,CAACE,aAAa,CAAC,EAC9CuB,CAAC,CAACrB,UAAU,CAAC9I,GAAG,CAAE0I,IAAI,IAAKA,IAAI,CAACE,aAAa,CAC/C,CACN,CAAC,CAAA;AACH,CAAA;AAEA,MAAMyB,OAAO,GAAG,WAAW,CAAA;AAC3B,MAAMC,mBAAmB,GAAG,CAAC,CAAA;AAC7B,MAAMC,eAAe,GAAG,CAAC,CAAA;AACzB,MAAMC,iBAAiB,GAAG,CAAC,CAAA;AAC3B,MAAMC,kBAAkB,GAAG,EAAE,CAAA;AAC7B,MAAMC,YAAY,GAAG,CAAC,CAAC,CAAA;AACvB,MAAMC,OAAO,GAAIC,CAAS,IAAKA,CAAC,KAAK,GAAG,CAAA;AAExC,SAAS3B,YAAYA,CAAClH,IAAY,EAAE7B,KAA0B,EAAU;AACtE,EAAA,IAAIqJ,QAAQ,GAAGxH,IAAI,CAACyH,KAAK,CAAC,GAAG,CAAC,CAAA;AAC9B,EAAA,IAAIqB,YAAY,GAAGtB,QAAQ,CAAChJ,MAAM,CAAA;AAClC,EAAA,IAAIgJ,QAAQ,CAACuB,IAAI,CAACH,OAAO,CAAC,EAAE;AAC1BE,IAAAA,YAAY,IAAIH,YAAY,CAAA;AAC9B,GAAA;AAEA,EAAA,IAAIxK,KAAK,EAAE;AACT2K,IAAAA,YAAY,IAAIN,eAAe,CAAA;AACjC,GAAA;AAEA,EAAA,OAAOhB,QAAQ,CACZwB,MAAM,CAAEH,CAAC,IAAK,CAACD,OAAO,CAACC,CAAC,CAAC,CAAC,CAC1BI,MAAM,CACL,CAAChC,KAAK,EAAEiC,OAAO,KACbjC,KAAK,IACJqB,OAAO,CAACa,IAAI,CAACD,OAAO,CAAC,GAClBX,mBAAmB,GACnBW,OAAO,KAAK,EAAE,GACdT,iBAAiB,GACjBC,kBAAkB,CAAC,EACzBI,YACF,CAAC,CAAA;AACL,CAAA;AAEA,SAAST,cAAcA,CAACF,CAAW,EAAEC,CAAW,EAAU;AACxD,EAAA,IAAIgB,QAAQ,GACVjB,CAAC,CAAC3J,MAAM,KAAK4J,CAAC,CAAC5J,MAAM,IAAI2J,CAAC,CAAChG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACkH,KAAK,CAAC,CAACzK,CAAC,EAAEkH,CAAC,KAAKlH,CAAC,KAAKwJ,CAAC,CAACtC,CAAC,CAAC,CAAC,CAAA;AAErE,EAAA,OAAOsD,QAAQ;AACX;AACA;AACA;AACA;AACAjB,EAAAA,CAAC,CAACA,CAAC,CAAC3J,MAAM,GAAG,CAAC,CAAC,GAAG4J,CAAC,CAACA,CAAC,CAAC5J,MAAM,GAAG,CAAC,CAAC;AACjC;AACA;EACA,CAAC,CAAA;AACP,CAAA;AAEA,SAASyH,gBAAgBA,CAIvBqD,MAAoC,EACpCjK,QAAgB,EACwC;EACxD,IAAI;AAAE0H,IAAAA,UAAAA;AAAW,GAAC,GAAGuC,MAAM,CAAA;EAE3B,IAAIC,aAAa,GAAG,EAAE,CAAA;EACtB,IAAIC,eAAe,GAAG,GAAG,CAAA;EACzB,IAAI3D,OAAwD,GAAG,EAAE,CAAA;AACjE,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,UAAU,CAACvI,MAAM,EAAE,EAAEsH,CAAC,EAAE;AAC1C,IAAA,IAAIa,IAAI,GAAGI,UAAU,CAACjB,CAAC,CAAC,CAAA;IACxB,IAAI2D,GAAG,GAAG3D,CAAC,KAAKiB,UAAU,CAACvI,MAAM,GAAG,CAAC,CAAA;AACrC,IAAA,IAAIkL,iBAAiB,GACnBF,eAAe,KAAK,GAAG,GACnBnK,QAAQ,GACRA,QAAQ,CAAC8C,KAAK,CAACqH,eAAe,CAAChL,MAAM,CAAC,IAAI,GAAG,CAAA;IACnD,IAAI2H,KAAK,GAAGwD,SAAS,CACnB;MAAE3J,IAAI,EAAE2G,IAAI,CAACD,YAAY;MAAEE,aAAa,EAAED,IAAI,CAACC,aAAa;AAAE6C,MAAAA,GAAAA;KAAK,EACnEC,iBACF,CAAC,CAAA;AAED,IAAA,IAAI,CAACvD,KAAK,EAAE,OAAO,IAAI,CAAA;IAEvByD,MAAM,CAAC1F,MAAM,CAACqF,aAAa,EAAEpD,KAAK,CAACE,MAAM,CAAC,CAAA;AAE1C,IAAA,IAAI3B,KAAK,GAAGiC,IAAI,CAACjC,KAAK,CAAA;IAEtBmB,OAAO,CAACzF,IAAI,CAAC;AACX;AACAiG,MAAAA,MAAM,EAAEkD,aAAiC;MACzClK,QAAQ,EAAEyH,SAAS,CAAC,CAAC0C,eAAe,EAAErD,KAAK,CAAC9G,QAAQ,CAAC,CAAC;AACtDwK,MAAAA,YAAY,EAAEC,iBAAiB,CAC7BhD,SAAS,CAAC,CAAC0C,eAAe,EAAErD,KAAK,CAAC0D,YAAY,CAAC,CACjD,CAAC;AACDnF,MAAAA,KAAAA;AACF,KAAC,CAAC,CAAA;AAEF,IAAA,IAAIyB,KAAK,CAAC0D,YAAY,KAAK,GAAG,EAAE;MAC9BL,eAAe,GAAG1C,SAAS,CAAC,CAAC0C,eAAe,EAAErD,KAAK,CAAC0D,YAAY,CAAC,CAAC,CAAA;AACpE,KAAA;AACF,GAAA;AAEA,EAAA,OAAOhE,OAAO,CAAA;AAChB,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASkE,YAAYA,CAC1BC,YAAkB,EAClB3D,MAEC,EACO;AAAA,EAAA,IAHRA,MAEC,KAAA,KAAA,CAAA,EAAA;IAFDA,MAEC,GAAG,EAAE,CAAA;AAAA,GAAA;EAEN,IAAIrG,IAAY,GAAGgK,YAAY,CAAA;AAC/B,EAAA,IAAIhK,IAAI,CAAC6H,QAAQ,CAAC,GAAG,CAAC,IAAI7H,IAAI,KAAK,GAAG,IAAI,CAACA,IAAI,CAAC6H,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC9DvI,OAAO,CACL,KAAK,EACL,eAAeU,GAAAA,IAAI,GACbA,mCAAAA,IAAAA,IAAAA,GAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAqC,oCAAA,CAAA,GAAA,kEACE,IAChCT,oCAAAA,GAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAA,KAAA,CACjE,CAAC,CAAA;IACDT,IAAI,GAAGA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAS,CAAA;AAC1C,GAAA;;AAEA;EACA,MAAMwJ,MAAM,GAAGjK,IAAI,CAACyB,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAA;EAE9C,MAAMhC,SAAS,GAAIyK,CAAM,IACvBA,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,OAAOA,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGC,MAAM,CAACD,CAAC,CAAC,CAAA;AAExD,EAAA,MAAM1C,QAAQ,GAAGxH,IAAI,CAClByH,KAAK,CAAC,KAAK,CAAC,CACZxJ,GAAG,CAAC,CAACiL,OAAO,EAAE/K,KAAK,EAAEiM,KAAK,KAAK;IAC9B,MAAMC,aAAa,GAAGlM,KAAK,KAAKiM,KAAK,CAAC5L,MAAM,GAAG,CAAC,CAAA;;AAEhD;AACA,IAAA,IAAI6L,aAAa,IAAInB,OAAO,KAAK,GAAG,EAAE;MACpC,MAAMoB,IAAI,GAAG,GAAsB,CAAA;AACnC;AACA,MAAA,OAAO7K,SAAS,CAAC4G,MAAM,CAACiE,IAAI,CAAC,CAAC,CAAA;AAChC,KAAA;AAEA,IAAA,MAAMC,QAAQ,GAAGrB,OAAO,CAAC/C,KAAK,CAAC,kBAAkB,CAAC,CAAA;AAClD,IAAA,IAAIoE,QAAQ,EAAE;AACZ,MAAA,MAAM,GAAGrL,GAAG,EAAEsL,QAAQ,CAAC,GAAGD,QAAQ,CAAA;AAClC,MAAA,IAAIE,KAAK,GAAGpE,MAAM,CAACnH,GAAG,CAAoB,CAAA;MAC1CmD,SAAS,CAACmI,QAAQ,KAAK,GAAG,IAAIC,KAAK,IAAI,IAAI,EAAA,aAAA,GAAevL,GAAG,GAAA,UAAS,CAAC,CAAA;MACvE,OAAOO,SAAS,CAACgL,KAAK,CAAC,CAAA;AACzB,KAAA;;AAEA;AACA,IAAA,OAAOvB,OAAO,CAACzI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;GACnC,CAAA;AACD;AAAA,GACCuI,MAAM,CAAEE,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC,CAAA;AAEjC,EAAA,OAAOe,MAAM,GAAGzC,QAAQ,CAACtC,IAAI,CAAC,GAAG,CAAC,CAAA;AACpC,CAAA;;AAEA;AACA;AACA;;AAmBA;AACA;AACA;;AAwBA;AACA;AACA;AACA;AACA;AACA;AACO,SAASyE,SAASA,CAIvBe,OAAiC,EACjCrL,QAAgB,EACY;AAC5B,EAAA,IAAI,OAAOqL,OAAO,KAAK,QAAQ,EAAE;AAC/BA,IAAAA,OAAO,GAAG;AAAE1K,MAAAA,IAAI,EAAE0K,OAAO;AAAE9D,MAAAA,aAAa,EAAE,KAAK;AAAE6C,MAAAA,GAAG,EAAE,IAAA;KAAM,CAAA;AAC9D,GAAA;AAEA,EAAA,IAAI,CAACkB,OAAO,EAAEC,cAAc,CAAC,GAAGC,WAAW,CACzCH,OAAO,CAAC1K,IAAI,EACZ0K,OAAO,CAAC9D,aAAa,EACrB8D,OAAO,CAACjB,GACV,CAAC,CAAA;AAED,EAAA,IAAItD,KAAK,GAAG9G,QAAQ,CAAC8G,KAAK,CAACwE,OAAO,CAAC,CAAA;AACnC,EAAA,IAAI,CAACxE,KAAK,EAAE,OAAO,IAAI,CAAA;AAEvB,EAAA,IAAIqD,eAAe,GAAGrD,KAAK,CAAC,CAAC,CAAC,CAAA;EAC9B,IAAI0D,YAAY,GAAGL,eAAe,CAAC/I,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;AAC3D,EAAA,IAAIqK,aAAa,GAAG3E,KAAK,CAAChE,KAAK,CAAC,CAAC,CAAC,CAAA;AAClC,EAAA,IAAIkE,MAAc,GAAGuE,cAAc,CAAC3B,MAAM,CACxC,CAAC8B,IAAI,EAAA3H,IAAA,EAA6BjF,KAAK,KAAK;IAAA,IAArC;MAAE6M,SAAS;AAAEpD,MAAAA,UAAAA;AAAW,KAAC,GAAAxE,IAAA,CAAA;AAC9B;AACA;IACA,IAAI4H,SAAS,KAAK,GAAG,EAAE;AACrB,MAAA,IAAIC,UAAU,GAAGH,aAAa,CAAC3M,KAAK,CAAC,IAAI,EAAE,CAAA;MAC3C0L,YAAY,GAAGL,eAAe,CAC3BrH,KAAK,CAAC,CAAC,EAAEqH,eAAe,CAAChL,MAAM,GAAGyM,UAAU,CAACzM,MAAM,CAAC,CACpDiC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;AAC7B,KAAA;AAEA,IAAA,MAAM6B,KAAK,GAAGwI,aAAa,CAAC3M,KAAK,CAAC,CAAA;AAClC,IAAA,IAAIyJ,UAAU,IAAI,CAACtF,KAAK,EAAE;AACxByI,MAAAA,IAAI,CAACC,SAAS,CAAC,GAAG1M,SAAS,CAAA;AAC7B,KAAC,MAAM;AACLyM,MAAAA,IAAI,CAACC,SAAS,CAAC,GAAG,CAAC1I,KAAK,IAAI,EAAE,EAAE7B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;AACtD,KAAA;AACA,IAAA,OAAOsK,IAAI,CAAA;GACZ,EACD,EACF,CAAC,CAAA;EAED,OAAO;IACL1E,MAAM;AACNhH,IAAAA,QAAQ,EAAEmK,eAAe;IACzBK,YAAY;AACZa,IAAAA,OAAAA;GACD,CAAA;AACH,CAAA;AAIA,SAASG,WAAWA,CAClB7K,IAAY,EACZ4G,aAAa,EACb6C,GAAG,EAC4B;AAAA,EAAA,IAF/B7C,aAAa,KAAA,KAAA,CAAA,EAAA;AAAbA,IAAAA,aAAa,GAAG,KAAK,CAAA;AAAA,GAAA;AAAA,EAAA,IACrB6C,GAAG,KAAA,KAAA,CAAA,EAAA;AAAHA,IAAAA,GAAG,GAAG,IAAI,CAAA;AAAA,GAAA;AAEVnK,EAAAA,OAAO,CACLU,IAAI,KAAK,GAAG,IAAI,CAACA,IAAI,CAAC6H,QAAQ,CAAC,GAAG,CAAC,IAAI7H,IAAI,CAAC6H,QAAQ,CAAC,IAAI,CAAC,EAC1D,eAAA,GAAe7H,IAAI,GACbA,mCAAAA,IAAAA,IAAAA,GAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAqC,oCAAA,CAAA,GAAA,kEACE,2CAChCT,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,SACjE,CAAC,CAAA;EAED,IAAI4F,MAA2B,GAAG,EAAE,CAAA;AACpC,EAAA,IAAI6E,YAAY,GACd,GAAG,GACHlL,IAAI,CACDS,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;AAAC,GACvBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AAAC,GACrBA,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC;GACrCA,OAAO,CACN,mBAAmB,EACnB,CAAC0K,CAAS,EAAEH,SAAiB,EAAEpD,UAAU,KAAK;IAC5CvB,MAAM,CAACjG,IAAI,CAAC;MAAE4K,SAAS;MAAEpD,UAAU,EAAEA,UAAU,IAAI,IAAA;AAAK,KAAC,CAAC,CAAA;AAC1D,IAAA,OAAOA,UAAU,GAAG,cAAc,GAAG,YAAY,CAAA;AACnD,GACF,CAAC,CAAA;AAEL,EAAA,IAAI5H,IAAI,CAAC6H,QAAQ,CAAC,GAAG,CAAC,EAAE;IACtBxB,MAAM,CAACjG,IAAI,CAAC;AAAE4K,MAAAA,SAAS,EAAE,GAAA;AAAI,KAAC,CAAC,CAAA;IAC/BE,YAAY,IACVlL,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,IAAI,GACzB,OAAO;MACP,mBAAmB,CAAC;GAC3B,MAAM,IAAIyJ,GAAG,EAAE;AACd;AACAyB,IAAAA,YAAY,IAAI,OAAO,CAAA;GACxB,MAAM,IAAIlL,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK,GAAG,EAAE;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACAkL,IAAAA,YAAY,IAAI,eAAe,CAAA;AACjC,GAAC,MAAM,CACL;AAGF,EAAA,IAAIP,OAAO,GAAG,IAAIS,MAAM,CAACF,YAAY,EAAEtE,aAAa,GAAGtI,SAAS,GAAG,GAAG,CAAC,CAAA;AAEvE,EAAA,OAAO,CAACqM,OAAO,EAAEtE,MAAM,CAAC,CAAA;AAC1B,CAAA;AAEA,SAASL,UAAUA,CAAC1D,KAAa,EAAE;EACjC,IAAI;IACF,OAAOA,KAAK,CACTmF,KAAK,CAAC,GAAG,CAAC,CACVxJ,GAAG,CAAEoN,CAAC,IAAKC,kBAAkB,CAACD,CAAC,CAAC,CAAC5K,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CACvDyE,IAAI,CAAC,GAAG,CAAC,CAAA;GACb,CAAC,OAAOnB,KAAK,EAAE;IACdzE,OAAO,CACL,KAAK,EACL,iBAAA,GAAiBgD,KAAK,GAC2C,6CAAA,GAAA,+DAAA,IAAA,YAAA,GAClDyB,KAAK,GAAA,IAAA,CACtB,CAAC,CAAA;AAED,IAAA,OAAOzB,KAAK,CAAA;AACd,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACO,SAASmD,aAAaA,CAC3BpG,QAAgB,EAChBmG,QAAgB,EACD;AACf,EAAA,IAAIA,QAAQ,KAAK,GAAG,EAAE,OAAOnG,QAAQ,CAAA;AAErC,EAAA,IAAI,CAACA,QAAQ,CAACkM,WAAW,EAAE,CAAC9J,UAAU,CAAC+D,QAAQ,CAAC+F,WAAW,EAAE,CAAC,EAAE;AAC9D,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACA;AACA,EAAA,IAAIC,UAAU,GAAGhG,QAAQ,CAACqC,QAAQ,CAAC,GAAG,CAAC,GACnCrC,QAAQ,CAAChH,MAAM,GAAG,CAAC,GACnBgH,QAAQ,CAAChH,MAAM,CAAA;AACnB,EAAA,IAAIiN,QAAQ,GAAGpM,QAAQ,CAACE,MAAM,CAACiM,UAAU,CAAC,CAAA;AAC1C,EAAA,IAAIC,QAAQ,IAAIA,QAAQ,KAAK,GAAG,EAAE;AAChC;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA,EAAA,OAAOpM,QAAQ,CAAC8C,KAAK,CAACqJ,UAAU,CAAC,IAAI,GAAG,CAAA;AAC1C,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASE,WAAWA,CAACzM,EAAM,EAAE0M,YAAY,EAAc;AAAA,EAAA,IAA1BA,YAAY,KAAA,KAAA,CAAA,EAAA;AAAZA,IAAAA,YAAY,GAAG,GAAG,CAAA;AAAA,GAAA;EACpD,IAAI;AACFtM,IAAAA,QAAQ,EAAEuM,UAAU;AACpB1L,IAAAA,MAAM,GAAG,EAAE;AACXC,IAAAA,IAAI,GAAG,EAAA;GACR,GAAG,OAAOlB,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE,CAAA;EAE/C,IAAII,QAAQ,GAAGuM,UAAU,GACrBA,UAAU,CAACnK,UAAU,CAAC,GAAG,CAAC,GACxBmK,UAAU,GACVC,eAAe,CAACD,UAAU,EAAED,YAAY,CAAC,GAC3CA,YAAY,CAAA;EAEhB,OAAO;IACLtM,QAAQ;AACRa,IAAAA,MAAM,EAAE4L,eAAe,CAAC5L,MAAM,CAAC;IAC/BC,IAAI,EAAE4L,aAAa,CAAC5L,IAAI,CAAA;GACzB,CAAA;AACH,CAAA;AAEA,SAAS0L,eAAeA,CAACnF,YAAoB,EAAEiF,YAAoB,EAAU;AAC3E,EAAA,IAAInE,QAAQ,GAAGmE,YAAY,CAAClL,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACgH,KAAK,CAAC,GAAG,CAAC,CAAA;AAC1D,EAAA,IAAIuE,gBAAgB,GAAGtF,YAAY,CAACe,KAAK,CAAC,GAAG,CAAC,CAAA;AAE9CuE,EAAAA,gBAAgB,CAAC7E,OAAO,CAAE+B,OAAO,IAAK;IACpC,IAAIA,OAAO,KAAK,IAAI,EAAE;AACpB;MACA,IAAI1B,QAAQ,CAAChJ,MAAM,GAAG,CAAC,EAAEgJ,QAAQ,CAACyE,GAAG,EAAE,CAAA;AACzC,KAAC,MAAM,IAAI/C,OAAO,KAAK,GAAG,EAAE;AAC1B1B,MAAAA,QAAQ,CAACpH,IAAI,CAAC8I,OAAO,CAAC,CAAA;AACxB,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,OAAO1B,QAAQ,CAAChJ,MAAM,GAAG,CAAC,GAAGgJ,QAAQ,CAACtC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;AACvD,CAAA;AAEA,SAASgH,mBAAmBA,CAC1BC,IAAY,EACZC,KAAa,EACbC,IAAY,EACZrM,IAAmB,EACnB;AACA,EAAA,OACE,oBAAqBmM,GAAAA,IAAI,GACjBC,sCAAAA,IAAAA,MAAAA,GAAAA,KAAK,iBAAa5M,IAAI,CAACC,SAAS,CACtCO,IACF,CAAC,GAAA,oCAAA,CAAoC,IAC7BqM,MAAAA,GAAAA,IAAI,8DAA2D,GACJ,qEAAA,CAAA;AAEvE,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,0BAA0BA,CAExCzG,OAAY,EAAE;AACd,EAAA,OAAOA,OAAO,CAACmD,MAAM,CACnB,CAAC7C,KAAK,EAAEhI,KAAK,KACXA,KAAK,KAAK,CAAC,IAAKgI,KAAK,CAACzB,KAAK,CAAC1E,IAAI,IAAImG,KAAK,CAACzB,KAAK,CAAC1E,IAAI,CAACxB,MAAM,GAAG,CAClE,CAAC,CAAA;AACH,CAAA;;AAEA;AACA;AACO,SAAS+N,mBAAmBA,CAEjC1G,OAAY,EAAE2G,oBAA6B,EAAE;AAC7C,EAAA,IAAIC,WAAW,GAAGH,0BAA0B,CAACzG,OAAO,CAAC,CAAA;;AAErD;AACA;AACA;AACA,EAAA,IAAI2G,oBAAoB,EAAE;IACxB,OAAOC,WAAW,CAACxO,GAAG,CAAC,CAACkI,KAAK,EAAElD,GAAG,KAChCA,GAAG,KAAK4C,OAAO,CAACrH,MAAM,GAAG,CAAC,GAAG2H,KAAK,CAAC9G,QAAQ,GAAG8G,KAAK,CAAC0D,YACtD,CAAC,CAAA;AACH,GAAA;EAEA,OAAO4C,WAAW,CAACxO,GAAG,CAAEkI,KAAK,IAAKA,KAAK,CAAC0D,YAAY,CAAC,CAAA;AACvD,CAAA;;AAEA;AACA;AACA;AACO,SAAS6C,SAASA,CACvBC,KAAS,EACTC,cAAwB,EACxBC,gBAAwB,EACxBC,cAAc,EACR;AAAA,EAAA,IADNA,cAAc,KAAA,KAAA,CAAA,EAAA;AAAdA,IAAAA,cAAc,GAAG,KAAK,CAAA;AAAA,GAAA;AAEtB,EAAA,IAAI7N,EAAiB,CAAA;AACrB,EAAA,IAAI,OAAO0N,KAAK,KAAK,QAAQ,EAAE;AAC7B1N,IAAAA,EAAE,GAAGgB,SAAS,CAAC0M,KAAK,CAAC,CAAA;AACvB,GAAC,MAAM;AACL1N,IAAAA,EAAE,GAAAkE,QAAA,CAAQwJ,EAAAA,EAAAA,KAAK,CAAE,CAAA;IAEjBtK,SAAS,CACP,CAACpD,EAAE,CAACI,QAAQ,IAAI,CAACJ,EAAE,CAACI,QAAQ,CAACgI,QAAQ,CAAC,GAAG,CAAC,EAC1C6E,mBAAmB,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAEjN,EAAE,CACnD,CAAC,CAAA;IACDoD,SAAS,CACP,CAACpD,EAAE,CAACI,QAAQ,IAAI,CAACJ,EAAE,CAACI,QAAQ,CAACgI,QAAQ,CAAC,GAAG,CAAC,EAC1C6E,mBAAmB,CAAC,GAAG,EAAE,UAAU,EAAE,MAAM,EAAEjN,EAAE,CACjD,CAAC,CAAA;IACDoD,SAAS,CACP,CAACpD,EAAE,CAACiB,MAAM,IAAI,CAACjB,EAAE,CAACiB,MAAM,CAACmH,QAAQ,CAAC,GAAG,CAAC,EACtC6E,mBAAmB,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAEjN,EAAE,CAC/C,CAAC,CAAA;AACH,GAAA;EAEA,IAAI8N,WAAW,GAAGJ,KAAK,KAAK,EAAE,IAAI1N,EAAE,CAACI,QAAQ,KAAK,EAAE,CAAA;EACpD,IAAIuM,UAAU,GAAGmB,WAAW,GAAG,GAAG,GAAG9N,EAAE,CAACI,QAAQ,CAAA;AAEhD,EAAA,IAAI2N,IAAY,CAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,IAAIpB,UAAU,IAAI,IAAI,EAAE;AACtBoB,IAAAA,IAAI,GAAGH,gBAAgB,CAAA;AACzB,GAAC,MAAM;AACL,IAAA,IAAII,kBAAkB,GAAGL,cAAc,CAACpO,MAAM,GAAG,CAAC,CAAA;;AAElD;AACA;AACA;AACA;IACA,IAAI,CAACsO,cAAc,IAAIlB,UAAU,CAACnK,UAAU,CAAC,IAAI,CAAC,EAAE;AAClD,MAAA,IAAIyL,UAAU,GAAGtB,UAAU,CAACnE,KAAK,CAAC,GAAG,CAAC,CAAA;AAEtC,MAAA,OAAOyF,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QAC7BA,UAAU,CAACC,KAAK,EAAE,CAAA;AAClBF,QAAAA,kBAAkB,IAAI,CAAC,CAAA;AACzB,OAAA;MAEAhO,EAAE,CAACI,QAAQ,GAAG6N,UAAU,CAAChI,IAAI,CAAC,GAAG,CAAC,CAAA;AACpC,KAAA;IAEA8H,IAAI,GAAGC,kBAAkB,IAAI,CAAC,GAAGL,cAAc,CAACK,kBAAkB,CAAC,GAAG,GAAG,CAAA;AAC3E,GAAA;AAEA,EAAA,IAAIjN,IAAI,GAAG0L,WAAW,CAACzM,EAAE,EAAE+N,IAAI,CAAC,CAAA;;AAEhC;AACA,EAAA,IAAII,wBAAwB,GAC1BxB,UAAU,IAAIA,UAAU,KAAK,GAAG,IAAIA,UAAU,CAAC/D,QAAQ,CAAC,GAAG,CAAC,CAAA;AAC9D;AACA,EAAA,IAAIwF,uBAAuB,GACzB,CAACN,WAAW,IAAInB,UAAU,KAAK,GAAG,KAAKiB,gBAAgB,CAAChF,QAAQ,CAAC,GAAG,CAAC,CAAA;AACvE,EAAA,IACE,CAAC7H,IAAI,CAACX,QAAQ,CAACwI,QAAQ,CAAC,GAAG,CAAC,KAC3BuF,wBAAwB,IAAIC,uBAAuB,CAAC,EACrD;IACArN,IAAI,CAACX,QAAQ,IAAI,GAAG,CAAA;AACtB,GAAA;AAEA,EAAA,OAAOW,IAAI,CAAA;AACb,CAAA;;AAEA;AACA;AACA;AACO,SAASsN,aAAaA,CAACrO,EAAM,EAAsB;AACxD;EACA,OAAOA,EAAE,KAAK,EAAE,IAAKA,EAAE,CAAUI,QAAQ,KAAK,EAAE,GAC5C,GAAG,GACH,OAAOJ,EAAE,KAAK,QAAQ,GACtBgB,SAAS,CAAChB,EAAE,CAAC,CAACI,QAAQ,GACtBJ,EAAE,CAACI,QAAQ,CAAA;AACjB,CAAA;;AAEA;AACA;AACA;MACayH,SAAS,GAAIyG,KAAe,IACvCA,KAAK,CAACrI,IAAI,CAAC,GAAG,CAAC,CAACzE,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAC;;AAExC;AACA;AACA;MACaqJ,iBAAiB,GAAIzK,QAAgB,IAChDA,QAAQ,CAACoB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,EAAC;;AAEnD;AACA;AACA;AACO,MAAMqL,eAAe,GAAI5L,MAAc,IAC5C,CAACA,MAAM,IAAIA,MAAM,KAAK,GAAG,GACrB,EAAE,GACFA,MAAM,CAACuB,UAAU,CAAC,GAAG,CAAC,GACtBvB,MAAM,GACN,GAAG,GAAGA,MAAM,CAAA;;AAElB;AACA;AACA;AACO,MAAM6L,aAAa,GAAI5L,IAAY,IACxC,CAACA,IAAI,IAAIA,IAAI,KAAK,GAAG,GAAG,EAAE,GAAGA,IAAI,CAACsB,UAAU,CAAC,GAAG,CAAC,GAAGtB,IAAI,GAAG,GAAG,GAAGA,IAAI,CAAA;AAOvE;AACA;AACA;AACA;AACO,MAAMqN,IAAkB,GAAG,SAArBA,IAAkBA,CAAIlH,IAAI,EAAEmH,IAAI,EAAU;AAAA,EAAA,IAAdA,IAAI,KAAA,KAAA,CAAA,EAAA;IAAJA,IAAI,GAAG,EAAE,CAAA;AAAA,GAAA;AAChD,EAAA,IAAIC,YAAY,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG;AAAEE,IAAAA,MAAM,EAAEF,IAAAA;AAAK,GAAC,GAAGA,IAAI,CAAA;EAErE,IAAIG,OAAO,GAAG,IAAIC,OAAO,CAACH,YAAY,CAACE,OAAO,CAAC,CAAA;AAC/C,EAAA,IAAI,CAACA,OAAO,CAACE,GAAG,CAAC,cAAc,CAAC,EAAE;AAChCF,IAAAA,OAAO,CAACG,GAAG,CAAC,cAAc,EAAE,iCAAiC,CAAC,CAAA;AAChE,GAAA;AAEA,EAAA,OAAO,IAAIC,QAAQ,CAACxO,IAAI,CAACC,SAAS,CAAC6G,IAAI,CAAC,EAAAnD,QAAA,CAAA,EAAA,EACnCuK,YAAY,EAAA;AACfE,IAAAA,OAAAA;AAAO,GAAA,CACR,CAAC,CAAA;AACJ,EAAC;AAQM,MAAMK,oBAAoB,SAASzL,KAAK,CAAC,EAAA;AAEzC,MAAM0L,YAAY,CAAC;AAWxBC,EAAAA,WAAWA,CAAC7H,IAA6B,EAAEoH,YAA2B,EAAE;AAAA,IAAA,IAAA,CAVhEU,cAAc,GAAgB,IAAI5J,GAAG,EAAU,CAAA;AAAA,IAAA,IAAA,CAI/C6J,WAAW,GACjB,IAAI7J,GAAG,EAAE,CAAA;IAAA,IAGX8J,CAAAA,YAAY,GAAa,EAAE,CAAA;AAGzBjM,IAAAA,SAAS,CACPiE,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACiI,KAAK,CAACC,OAAO,CAAClI,IAAI,CAAC,EACxD,oCACF,CAAC,CAAA;;AAED;AACA;AACA,IAAA,IAAImI,MAAyC,CAAA;AAC7C,IAAA,IAAI,CAACC,YAAY,GAAG,IAAIC,OAAO,CAAC,CAACxD,CAAC,EAAEyD,CAAC,KAAMH,MAAM,GAAGG,CAAE,CAAC,CAAA;AACvD,IAAA,IAAI,CAACC,UAAU,GAAG,IAAIC,eAAe,EAAE,CAAA;IACvC,IAAIC,OAAO,GAAGA,MACZN,MAAM,CAAC,IAAIR,oBAAoB,CAAC,uBAAuB,CAAC,CAAC,CAAA;AAC3D,IAAA,IAAI,CAACe,mBAAmB,GAAG,MACzB,IAAI,CAACH,UAAU,CAACI,MAAM,CAAC5K,mBAAmB,CAAC,OAAO,EAAE0K,OAAO,CAAC,CAAA;IAC9D,IAAI,CAACF,UAAU,CAACI,MAAM,CAAC7K,gBAAgB,CAAC,OAAO,EAAE2K,OAAO,CAAC,CAAA;AAEzD,IAAA,IAAI,CAACzI,IAAI,GAAGsD,MAAM,CAAC5L,OAAO,CAACsI,IAAI,CAAC,CAAC2C,MAAM,CACrC,CAACiG,GAAG,EAAAC,KAAA,KAAA;AAAA,MAAA,IAAE,CAACjQ,GAAG,EAAEoD,KAAK,CAAC,GAAA6M,KAAA,CAAA;AAAA,MAAA,OAChBvF,MAAM,CAAC1F,MAAM,CAACgL,GAAG,EAAE;QACjB,CAAChQ,GAAG,GAAG,IAAI,CAACkQ,YAAY,CAAClQ,GAAG,EAAEoD,KAAK,CAAA;AACrC,OAAC,CAAC,CAAA;KACJ,EAAA,EACF,CAAC,CAAA;IAED,IAAI,IAAI,CAAC+M,IAAI,EAAE;AACb;MACA,IAAI,CAACL,mBAAmB,EAAE,CAAA;AAC5B,KAAA;IAEA,IAAI,CAACvB,IAAI,GAAGC,YAAY,CAAA;AAC1B,GAAA;AAEQ0B,EAAAA,YAAYA,CAClBlQ,GAAW,EACXoD,KAAiC,EACP;AAC1B,IAAA,IAAI,EAAEA,KAAK,YAAYqM,OAAO,CAAC,EAAE;AAC/B,MAAA,OAAOrM,KAAK,CAAA;AACd,KAAA;AAEA,IAAA,IAAI,CAACgM,YAAY,CAAClO,IAAI,CAAClB,GAAG,CAAC,CAAA;AAC3B,IAAA,IAAI,CAACkP,cAAc,CAACkB,GAAG,CAACpQ,GAAG,CAAC,CAAA;;AAE5B;AACA;IACA,IAAIqQ,OAAuB,GAAGZ,OAAO,CAACa,IAAI,CAAC,CAAClN,KAAK,EAAE,IAAI,CAACoM,YAAY,CAAC,CAAC,CAACe,IAAI,CACxEnJ,IAAI,IAAK,IAAI,CAACoJ,QAAQ,CAACH,OAAO,EAAErQ,GAAG,EAAEZ,SAAS,EAAEgI,IAAe,CAAC,EAChEvC,KAAK,IAAK,IAAI,CAAC2L,QAAQ,CAACH,OAAO,EAAErQ,GAAG,EAAE6E,KAAgB,CACzD,CAAC,CAAA;;AAED;AACA;AACAwL,IAAAA,OAAO,CAACI,KAAK,CAAC,MAAM,EAAE,CAAC,CAAA;AAEvB/F,IAAAA,MAAM,CAACgG,cAAc,CAACL,OAAO,EAAE,UAAU,EAAE;MAAEM,GAAG,EAAEA,MAAM,IAAA;AAAK,KAAC,CAAC,CAAA;AAC/D,IAAA,OAAON,OAAO,CAAA;AAChB,GAAA;EAEQG,QAAQA,CACdH,OAAuB,EACvBrQ,GAAW,EACX6E,KAAc,EACduC,IAAc,EACL;IACT,IACE,IAAI,CAACuI,UAAU,CAACI,MAAM,CAACa,OAAO,IAC9B/L,KAAK,YAAYkK,oBAAoB,EACrC;MACA,IAAI,CAACe,mBAAmB,EAAE,CAAA;AAC1BpF,MAAAA,MAAM,CAACgG,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;QAAEM,GAAG,EAAEA,MAAM9L,KAAAA;AAAM,OAAC,CAAC,CAAA;AAC9D,MAAA,OAAO4K,OAAO,CAACF,MAAM,CAAC1K,KAAK,CAAC,CAAA;AAC9B,KAAA;AAEA,IAAA,IAAI,CAACqK,cAAc,CAAC2B,MAAM,CAAC7Q,GAAG,CAAC,CAAA;IAE/B,IAAI,IAAI,CAACmQ,IAAI,EAAE;AACb;MACA,IAAI,CAACL,mBAAmB,EAAE,CAAA;AAC5B,KAAA;;AAEA;AACA;AACA,IAAA,IAAIjL,KAAK,KAAKzF,SAAS,IAAIgI,IAAI,KAAKhI,SAAS,EAAE;MAC7C,IAAI0R,cAAc,GAAG,IAAIxN,KAAK,CAC5B,0BAA0BtD,GAAAA,GAAG,gGAE/B,CAAC,CAAA;AACD0K,MAAAA,MAAM,CAACgG,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;QAAEM,GAAG,EAAEA,MAAMG,cAAAA;AAAe,OAAC,CAAC,CAAA;AACvE,MAAA,IAAI,CAACC,IAAI,CAAC,KAAK,EAAE/Q,GAAG,CAAC,CAAA;AACrB,MAAA,OAAOyP,OAAO,CAACF,MAAM,CAACuB,cAAc,CAAC,CAAA;AACvC,KAAA;IAEA,IAAI1J,IAAI,KAAKhI,SAAS,EAAE;AACtBsL,MAAAA,MAAM,CAACgG,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;QAAEM,GAAG,EAAEA,MAAM9L,KAAAA;AAAM,OAAC,CAAC,CAAA;AAC9D,MAAA,IAAI,CAACkM,IAAI,CAAC,KAAK,EAAE/Q,GAAG,CAAC,CAAA;AACrB,MAAA,OAAOyP,OAAO,CAACF,MAAM,CAAC1K,KAAK,CAAC,CAAA;AAC9B,KAAA;AAEA6F,IAAAA,MAAM,CAACgG,cAAc,CAACL,OAAO,EAAE,OAAO,EAAE;MAAEM,GAAG,EAAEA,MAAMvJ,IAAAA;AAAK,KAAC,CAAC,CAAA;AAC5D,IAAA,IAAI,CAAC2J,IAAI,CAAC,KAAK,EAAE/Q,GAAG,CAAC,CAAA;AACrB,IAAA,OAAOoH,IAAI,CAAA;AACb,GAAA;AAEQ2J,EAAAA,IAAIA,CAACH,OAAgB,EAAEI,UAAmB,EAAE;AAClD,IAAA,IAAI,CAAC7B,WAAW,CAAClH,OAAO,CAAEgJ,UAAU,IAAKA,UAAU,CAACL,OAAO,EAAEI,UAAU,CAAC,CAAC,CAAA;AAC3E,GAAA;EAEAE,SAASA,CAACtP,EAAmD,EAAE;AAC7D,IAAA,IAAI,CAACuN,WAAW,CAACiB,GAAG,CAACxO,EAAE,CAAC,CAAA;IACxB,OAAO,MAAM,IAAI,CAACuN,WAAW,CAAC0B,MAAM,CAACjP,EAAE,CAAC,CAAA;AAC1C,GAAA;AAEAuP,EAAAA,MAAMA,GAAG;AACP,IAAA,IAAI,CAACxB,UAAU,CAACyB,KAAK,EAAE,CAAA;AACvB,IAAA,IAAI,CAAClC,cAAc,CAACjH,OAAO,CAAC,CAACkE,CAAC,EAAEkF,CAAC,KAAK,IAAI,CAACnC,cAAc,CAAC2B,MAAM,CAACQ,CAAC,CAAC,CAAC,CAAA;AACpE,IAAA,IAAI,CAACN,IAAI,CAAC,IAAI,CAAC,CAAA;AACjB,GAAA;EAEA,MAAMO,WAAWA,CAACvB,MAAmB,EAAE;IACrC,IAAIa,OAAO,GAAG,KAAK,CAAA;AACnB,IAAA,IAAI,CAAC,IAAI,CAACT,IAAI,EAAE;MACd,IAAIN,OAAO,GAAGA,MAAM,IAAI,CAACsB,MAAM,EAAE,CAAA;AACjCpB,MAAAA,MAAM,CAAC7K,gBAAgB,CAAC,OAAO,EAAE2K,OAAO,CAAC,CAAA;AACzCe,MAAAA,OAAO,GAAG,MAAM,IAAInB,OAAO,CAAE8B,OAAO,IAAK;AACvC,QAAA,IAAI,CAACL,SAAS,CAAEN,OAAO,IAAK;AAC1Bb,UAAAA,MAAM,CAAC5K,mBAAmB,CAAC,OAAO,EAAE0K,OAAO,CAAC,CAAA;AAC5C,UAAA,IAAIe,OAAO,IAAI,IAAI,CAACT,IAAI,EAAE;YACxBoB,OAAO,CAACX,OAAO,CAAC,CAAA;AAClB,WAAA;AACF,SAAC,CAAC,CAAA;AACJ,OAAC,CAAC,CAAA;AACJ,KAAA;AACA,IAAA,OAAOA,OAAO,CAAA;AAChB,GAAA;EAEA,IAAIT,IAAIA,GAAG;AACT,IAAA,OAAO,IAAI,CAACjB,cAAc,CAACsC,IAAI,KAAK,CAAC,CAAA;AACvC,GAAA;EAEA,IAAIC,aAAaA,GAAG;AAClBtO,IAAAA,SAAS,CACP,IAAI,CAACiE,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC+I,IAAI,EAC/B,2DACF,CAAC,CAAA;AAED,IAAA,OAAOzF,MAAM,CAAC5L,OAAO,CAAC,IAAI,CAACsI,IAAI,CAAC,CAAC2C,MAAM,CACrC,CAACiG,GAAG,EAAA0B,KAAA,KAAA;AAAA,MAAA,IAAE,CAAC1R,GAAG,EAAEoD,KAAK,CAAC,GAAAsO,KAAA,CAAA;AAAA,MAAA,OAChBhH,MAAM,CAAC1F,MAAM,CAACgL,GAAG,EAAE;AACjB,QAAA,CAAChQ,GAAG,GAAG2R,oBAAoB,CAACvO,KAAK,CAAA;AACnC,OAAC,CAAC,CAAA;KACJ,EAAA,EACF,CAAC,CAAA;AACH,GAAA;EAEA,IAAIwO,WAAWA,GAAG;AAChB,IAAA,OAAOvC,KAAK,CAACvB,IAAI,CAAC,IAAI,CAACoB,cAAc,CAAC,CAAA;AACxC,GAAA;AACF,CAAA;AAEA,SAAS2C,gBAAgBA,CAACzO,KAAU,EAA2B;EAC7D,OACEA,KAAK,YAAYqM,OAAO,IAAKrM,KAAK,CAAoB0O,QAAQ,KAAK,IAAI,CAAA;AAE3E,CAAA;AAEA,SAASH,oBAAoBA,CAACvO,KAAU,EAAE;AACxC,EAAA,IAAI,CAACyO,gBAAgB,CAACzO,KAAK,CAAC,EAAE;AAC5B,IAAA,OAAOA,KAAK,CAAA;AACd,GAAA;EAEA,IAAIA,KAAK,CAAC2O,MAAM,EAAE;IAChB,MAAM3O,KAAK,CAAC2O,MAAM,CAAA;AACpB,GAAA;EACA,OAAO3O,KAAK,CAAC4O,KAAK,CAAA;AACpB,CAAA;AAOO,MAAMC,KAAoB,GAAG,SAAvBA,KAAoBA,CAAI7K,IAAI,EAAEmH,IAAI,EAAU;AAAA,EAAA,IAAdA,IAAI,KAAA,KAAA,CAAA,EAAA;IAAJA,IAAI,GAAG,EAAE,CAAA;AAAA,GAAA;AAClD,EAAA,IAAIC,YAAY,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG;AAAEE,IAAAA,MAAM,EAAEF,IAAAA;AAAK,GAAC,GAAGA,IAAI,CAAA;AAErE,EAAA,OAAO,IAAIS,YAAY,CAAC5H,IAAI,EAAEoH,YAAY,CAAC,CAAA;AAC7C,EAAC;AAOD;AACA;AACA;AACA;AACO,MAAM0D,QAA0B,GAAG,SAA7BA,QAA0BA,CAAIpP,GAAG,EAAEyL,IAAI,EAAW;AAAA,EAAA,IAAfA,IAAI,KAAA,KAAA,CAAA,EAAA;AAAJA,IAAAA,IAAI,GAAG,GAAG,CAAA;AAAA,GAAA;EACxD,IAAIC,YAAY,GAAGD,IAAI,CAAA;AACvB,EAAA,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;AACpCA,IAAAA,YAAY,GAAG;AAAEC,MAAAA,MAAM,EAAED,YAAAA;KAAc,CAAA;GACxC,MAAM,IAAI,OAAOA,YAAY,CAACC,MAAM,KAAK,WAAW,EAAE;IACrDD,YAAY,CAACC,MAAM,GAAG,GAAG,CAAA;AAC3B,GAAA;EAEA,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAACH,YAAY,CAACE,OAAO,CAAC,CAAA;AAC/CA,EAAAA,OAAO,CAACG,GAAG,CAAC,UAAU,EAAE/L,GAAG,CAAC,CAAA;AAE5B,EAAA,OAAO,IAAIgM,QAAQ,CAAC,IAAI,EAAA7K,QAAA,KACnBuK,YAAY,EAAA;AACfE,IAAAA,OAAAA;AAAO,GAAA,CACR,CAAC,CAAA;AACJ,EAAC;;AAED;AACA;AACA;AACA;AACA;MACayD,gBAAkC,GAAGA,CAACrP,GAAG,EAAEyL,IAAI,KAAK;AAC/D,EAAA,IAAI6D,QAAQ,GAAGF,QAAQ,CAACpP,GAAG,EAAEyL,IAAI,CAAC,CAAA;EAClC6D,QAAQ,CAAC1D,OAAO,CAACG,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAA;AACvD,EAAA,OAAOuD,QAAQ,CAAA;AACjB,EAAC;AAQD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,iBAAiB,CAA0B;EAOtDpD,WAAWA,CACTR,MAAc,EACd6D,UAA8B,EAC9BlL,IAAS,EACTmL,QAAQ,EACR;AAAA,IAAA,IADAA,QAAQ,KAAA,KAAA,CAAA,EAAA;AAARA,MAAAA,QAAQ,GAAG,KAAK,CAAA;AAAA,KAAA;IAEhB,IAAI,CAAC9D,MAAM,GAAGA,MAAM,CAAA;AACpB,IAAA,IAAI,CAAC6D,UAAU,GAAGA,UAAU,IAAI,EAAE,CAAA;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ,CAAA;IACxB,IAAInL,IAAI,YAAY9D,KAAK,EAAE;AACzB,MAAA,IAAI,CAAC8D,IAAI,GAAGA,IAAI,CAACvD,QAAQ,EAAE,CAAA;MAC3B,IAAI,CAACgB,KAAK,GAAGuC,IAAI,CAAA;AACnB,KAAC,MAAM;MACL,IAAI,CAACA,IAAI,GAAGA,IAAI,CAAA;AAClB,KAAA;AACF,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACA;AACO,SAASoL,oBAAoBA,CAAC3N,KAAU,EAA0B;EACvE,OACEA,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,CAAC4J,MAAM,KAAK,QAAQ,IAChC,OAAO5J,KAAK,CAACyN,UAAU,KAAK,QAAQ,IACpC,OAAOzN,KAAK,CAAC0N,QAAQ,KAAK,SAAS,IACnC,MAAM,IAAI1N,KAAK,CAAA;AAEnB;;AC1gDA;AACA;AACA;;AAEA;AACA;AACA;AAmNA;AACA;AACA;AACA;AAwEA;AACA;AACA;AAKA;AACA;AACA;AASA;AACA;AACA;AAeA;AACA;AACA;AAeA;AACA;AACA;AAkBA;AACA;AACA;AAYA;AACA;AACA;AACA;AAKA;AACA;AACA;AAOA;AAOA;AAQA;AASA;AACA;AACA;AAGA;AACA;AACA;AAGA;AACA;AACA;AAKA;AACA;AACA;AAGA;AACA;AACA;AAGA;AACA;AACA;AAGA;AACA;AACA;AAsCA;AACA;AACA;AAmGA;AACA;AACA;AACA;AAMA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AAMA,MAAM4N,uBAA6C,GAAG,CACpD,MAAM,EACN,KAAK,EACL,OAAO,EACP,QAAQ,CACT,CAAA;AACD,MAAMC,oBAAoB,GAAG,IAAIpN,GAAG,CAClCmN,uBACF,CAAC,CAAA;AAED,MAAME,sBAAoC,GAAG,CAC3C,KAAK,EACL,GAAGF,uBAAuB,CAC3B,CAAA;AACD,MAAMG,mBAAmB,GAAG,IAAItN,GAAG,CAAaqN,sBAAsB,CAAC,CAAA;AAEvE,MAAME,mBAAmB,GAAG,IAAIvN,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;AAC9D,MAAMwN,iCAAiC,GAAG,IAAIxN,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;AAEtD,MAAMyN,eAAyC,GAAG;AACvD5T,EAAAA,KAAK,EAAE,MAAM;AACbc,EAAAA,QAAQ,EAAEb,SAAS;AACnB4T,EAAAA,UAAU,EAAE5T,SAAS;AACrB6T,EAAAA,UAAU,EAAE7T,SAAS;AACrB8T,EAAAA,WAAW,EAAE9T,SAAS;AACtB+T,EAAAA,QAAQ,EAAE/T,SAAS;AACnBkP,EAAAA,IAAI,EAAElP,SAAS;AACfgU,EAAAA,IAAI,EAAEhU,SAAAA;AACR,EAAC;AAEM,MAAMiU,YAAmC,GAAG;AACjDlU,EAAAA,KAAK,EAAE,MAAM;AACbiI,EAAAA,IAAI,EAAEhI,SAAS;AACf4T,EAAAA,UAAU,EAAE5T,SAAS;AACrB6T,EAAAA,UAAU,EAAE7T,SAAS;AACrB8T,EAAAA,WAAW,EAAE9T,SAAS;AACtB+T,EAAAA,QAAQ,EAAE/T,SAAS;AACnBkP,EAAAA,IAAI,EAAElP,SAAS;AACfgU,EAAAA,IAAI,EAAEhU,SAAAA;AACR,EAAC;AAEM,MAAMkU,YAA8B,GAAG;AAC5CnU,EAAAA,KAAK,EAAE,WAAW;AAClBoU,EAAAA,OAAO,EAAEnU,SAAS;AAClBoU,EAAAA,KAAK,EAAEpU,SAAS;AAChBa,EAAAA,QAAQ,EAAEb,SAAAA;AACZ,EAAC;AAED,MAAMqU,kBAAkB,GAAG,+BAA+B,CAAA;AAE1D,MAAMC,yBAAqD,GAAIlO,KAAK,KAAM;AACxEmO,EAAAA,gBAAgB,EAAEC,OAAO,CAACpO,KAAK,CAACmO,gBAAgB,CAAA;AAClD,CAAC,CAAC,CAAA;AAEF,MAAME,uBAAuB,GAAG,0BAA0B,CAAA;;AAE1D;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACO,SAASC,YAAYA,CAACvF,IAAgB,EAAU;AACrD,EAAA,MAAMwF,YAAY,GAAGxF,IAAI,CAACxM,MAAM,GAC5BwM,IAAI,CAACxM,MAAM,GACX,OAAOA,MAAM,KAAK,WAAW,GAC7BA,MAAM,GACN3C,SAAS,CAAA;EACb,MAAM4U,SAAS,GACb,OAAOD,YAAY,KAAK,WAAW,IACnC,OAAOA,YAAY,CAACrR,QAAQ,KAAK,WAAW,IAC5C,OAAOqR,YAAY,CAACrR,QAAQ,CAACuR,aAAa,KAAK,WAAW,CAAA;EAC5D,MAAMC,QAAQ,GAAG,CAACF,SAAS,CAAA;EAE3B7Q,SAAS,CACPoL,IAAI,CAAC7I,MAAM,CAACpG,MAAM,GAAG,CAAC,EACtB,2DACF,CAAC,CAAA;AAED,EAAA,IAAIqG,kBAA8C,CAAA;EAClD,IAAI4I,IAAI,CAAC5I,kBAAkB,EAAE;IAC3BA,kBAAkB,GAAG4I,IAAI,CAAC5I,kBAAkB,CAAA;AAC9C,GAAC,MAAM,IAAI4I,IAAI,CAAC4F,mBAAmB,EAAE;AACnC;AACA,IAAA,IAAIA,mBAAmB,GAAG5F,IAAI,CAAC4F,mBAAmB,CAAA;IAClDxO,kBAAkB,GAAIH,KAAK,KAAM;MAC/BmO,gBAAgB,EAAEQ,mBAAmB,CAAC3O,KAAK,CAAA;AAC7C,KAAC,CAAC,CAAA;AACJ,GAAC,MAAM;AACLG,IAAAA,kBAAkB,GAAG+N,yBAAyB,CAAA;AAChD,GAAA;;AAEA;EACA,IAAI7N,QAAuB,GAAG,EAAE,CAAA;AAChC;AACA,EAAA,IAAIuO,UAAU,GAAG3O,yBAAyB,CACxC8I,IAAI,CAAC7I,MAAM,EACXC,kBAAkB,EAClBvG,SAAS,EACTyG,QACF,CAAC,CAAA;AACD,EAAA,IAAIwO,kBAAyD,CAAA;AAC7D,EAAA,IAAI/N,QAAQ,GAAGiI,IAAI,CAACjI,QAAQ,IAAI,GAAG,CAAA;AACnC;EACA,IAAIgO,MAAoB,GAAArQ,QAAA,CAAA;AACtBsQ,IAAAA,iBAAiB,EAAE,KAAK;AACxBC,IAAAA,sBAAsB,EAAE,KAAK;AAC7BC,IAAAA,mBAAmB,EAAE,KAAK;AAC1BC,IAAAA,kBAAkB,EAAE,KAAK;AACzBpH,IAAAA,oBAAoB,EAAE,KAAA;GACnBiB,EAAAA,IAAI,CAAC+F,MAAM,CACf,CAAA;AACD;EACA,IAAIK,eAAoC,GAAG,IAAI,CAAA;AAC/C;AACA,EAAA,IAAIxF,WAAW,GAAG,IAAI7J,GAAG,EAAoB,CAAA;AAC7C;EACA,IAAIsP,oBAAmD,GAAG,IAAI,CAAA;AAC9D;EACA,IAAIC,uBAA+D,GAAG,IAAI,CAAA;AAC1E;EACA,IAAIC,iBAAmD,GAAG,IAAI,CAAA;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,IAAIC,qBAAqB,GAAGxG,IAAI,CAACyG,aAAa,IAAI,IAAI,CAAA;AAEtD,EAAA,IAAIC,cAAc,GAAG7O,WAAW,CAACgO,UAAU,EAAE7F,IAAI,CAAC7N,OAAO,CAACT,QAAQ,EAAEqG,QAAQ,CAAC,CAAA;EAC7E,IAAI4O,aAA+B,GAAG,IAAI,CAAA;EAE1C,IAAID,cAAc,IAAI,IAAI,EAAE;AAC1B;AACA;AACA,IAAA,IAAIpQ,KAAK,GAAGsQ,sBAAsB,CAAC,GAAG,EAAE;AACtChV,MAAAA,QAAQ,EAAEoO,IAAI,CAAC7N,OAAO,CAACT,QAAQ,CAACE,QAAAA;AAClC,KAAC,CAAC,CAAA;IACF,IAAI;MAAEwG,OAAO;AAAEnB,MAAAA,KAAAA;AAAM,KAAC,GAAG4P,sBAAsB,CAAChB,UAAU,CAAC,CAAA;AAC3Da,IAAAA,cAAc,GAAGtO,OAAO,CAAA;AACxBuO,IAAAA,aAAa,GAAG;MAAE,CAAC1P,KAAK,CAACO,EAAE,GAAGlB,KAAAA;KAAO,CAAA;AACvC,GAAA;AAEA,EAAA,IAAIwQ,WAAoB,CAAA;AACxB,EAAA,IAAIC,aAAa,GAAGL,cAAc,CAACpL,IAAI,CAAE0L,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACgQ,IAAI,CAAC,CAAA;AAC5D,EAAA,IAAIC,UAAU,GAAGR,cAAc,CAACpL,IAAI,CAAE0L,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACkQ,MAAM,CAAC,CAAA;AAC3D,EAAA,IAAIJ,aAAa,EAAE;AACjB;AACA;AACAD,IAAAA,WAAW,GAAG,KAAK,CAAA;AACrB,GAAC,MAAM,IAAI,CAACI,UAAU,EAAE;AACtB;AACAJ,IAAAA,WAAW,GAAG,IAAI,CAAA;AACpB,GAAC,MAAM,IAAIf,MAAM,CAACG,mBAAmB,EAAE;AACrC;AACA;AACA;AACA,IAAA,IAAIvN,UAAU,GAAGqH,IAAI,CAACyG,aAAa,GAAGzG,IAAI,CAACyG,aAAa,CAAC9N,UAAU,GAAG,IAAI,CAAA;AAC1E,IAAA,IAAIyO,MAAM,GAAGpH,IAAI,CAACyG,aAAa,GAAGzG,IAAI,CAACyG,aAAa,CAACW,MAAM,GAAG,IAAI,CAAA;IAClEN,WAAW,GAAGJ,cAAc,CAAC9K,KAAK,CAC/BoL,CAAC,IACAA,CAAC,CAAC/P,KAAK,CAACkQ,MAAM,IACdH,CAAC,CAAC/P,KAAK,CAACkQ,MAAM,CAACE,OAAO,KAAK,IAAI,KAC7B1O,UAAU,IAAIA,UAAU,CAACqO,CAAC,CAAC/P,KAAK,CAACO,EAAE,CAAC,KAAK3G,SAAS,IACjDuW,MAAM,IAAIA,MAAM,CAACJ,CAAC,CAAC/P,KAAK,CAACO,EAAE,CAAC,KAAK3G,SAAU,CAClD,CAAC,CAAA;AACH,GAAC,MAAM;AACL;AACA;AACAiW,IAAAA,WAAW,GAAG9G,IAAI,CAACyG,aAAa,IAAI,IAAI,CAAA;AAC1C,GAAA;AAEA,EAAA,IAAIa,MAAc,CAAA;AAClB,EAAA,IAAI1W,KAAkB,GAAG;AACvB2W,IAAAA,aAAa,EAAEvH,IAAI,CAAC7N,OAAO,CAACnB,MAAM;AAClCU,IAAAA,QAAQ,EAAEsO,IAAI,CAAC7N,OAAO,CAACT,QAAQ;AAC/B0G,IAAAA,OAAO,EAAEsO,cAAc;IACvBI,WAAW;AACXU,IAAAA,UAAU,EAAEhD,eAAe;AAC3B;IACAiD,qBAAqB,EAAEzH,IAAI,CAACyG,aAAa,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI;AAChEiB,IAAAA,kBAAkB,EAAE,KAAK;AACzBC,IAAAA,YAAY,EAAE,MAAM;AACpBhP,IAAAA,UAAU,EAAGqH,IAAI,CAACyG,aAAa,IAAIzG,IAAI,CAACyG,aAAa,CAAC9N,UAAU,IAAK,EAAE;IACvEiP,UAAU,EAAG5H,IAAI,CAACyG,aAAa,IAAIzG,IAAI,CAACyG,aAAa,CAACmB,UAAU,IAAK,IAAI;IACzER,MAAM,EAAGpH,IAAI,CAACyG,aAAa,IAAIzG,IAAI,CAACyG,aAAa,CAACW,MAAM,IAAKT,aAAa;AAC1EkB,IAAAA,QAAQ,EAAE,IAAIC,GAAG,EAAE;IACnBC,QAAQ,EAAE,IAAID,GAAG,EAAC;GACnB,CAAA;;AAED;AACA;AACA,EAAA,IAAIE,aAA4B,GAAGC,MAAa,CAAChX,GAAG,CAAA;;AAEpD;AACA;EACA,IAAIiX,yBAAyB,GAAG,KAAK,CAAA;;AAErC;AACA,EAAA,IAAIC,2BAAmD,CAAA;;AAEvD;EACA,IAAIC,4BAA4B,GAAG,KAAK,CAAA;;AAExC;AACA,EAAA,IAAIC,sBAAgD,GAAG,IAAIP,GAAG,EAG3D,CAAA;;AAEH;EACA,IAAIQ,2BAAgD,GAAG,IAAI,CAAA;;AAE3D;AACA;EACA,IAAIC,2BAA2B,GAAG,KAAK,CAAA;;AAEvC;AACA;AACA;AACA;EACA,IAAIC,sBAAsB,GAAG,KAAK,CAAA;;AAElC;AACA;EACA,IAAIC,uBAAiC,GAAG,EAAE,CAAA;;AAE1C;AACA;EACA,IAAIC,qBAA+B,GAAG,EAAE,CAAA;;AAExC;AACA,EAAA,IAAIC,gBAAgB,GAAG,IAAIb,GAAG,EAA2B,CAAA;;AAEzD;EACA,IAAIc,kBAAkB,GAAG,CAAC,CAAA;;AAE1B;AACA;AACA;EACA,IAAIC,uBAAuB,GAAG,CAAC,CAAC,CAAA;;AAEhC;AACA,EAAA,IAAIC,cAAc,GAAG,IAAIhB,GAAG,EAAkB,CAAA;;AAE9C;AACA,EAAA,IAAIiB,gBAAgB,GAAG,IAAIhS,GAAG,EAAU,CAAA;;AAExC;AACA,EAAA,IAAIiS,gBAAgB,GAAG,IAAIlB,GAAG,EAA0B,CAAA;;AAExD;AACA,EAAA,IAAImB,cAAc,GAAG,IAAInB,GAAG,EAAkB,CAAA;;AAE9C;AACA;AACA,EAAA,IAAIoB,eAAe,GAAG,IAAInS,GAAG,EAAU,CAAA;;AAEvC;AACA;AACA;AACA;AACA,EAAA,IAAIoS,eAAe,GAAG,IAAIrB,GAAG,EAAwB,CAAA;;AAErD;AACA;AACA,EAAA,IAAIsB,gBAAgB,GAAG,IAAItB,GAAG,EAA2B,CAAA;;AAEzD;AACA;EACA,IAAIuB,uBAAuB,GAAG,KAAK,CAAA;;AAEnC;AACA;AACA;EACA,SAASC,UAAUA,GAAG;AACpB;AACA;IACAlD,eAAe,GAAGpG,IAAI,CAAC7N,OAAO,CAACiB,MAAM,CACnCuC,IAAA,IAAgD;MAAA,IAA/C;AAAE3E,QAAAA,MAAM,EAAEuW,aAAa;QAAE7V,QAAQ;AAAEqB,QAAAA,KAAAA;AAAM,OAAC,GAAA4C,IAAA,CAAA;AACzC;AACA;AACA,MAAA,IAAI0T,uBAAuB,EAAE;AAC3BA,QAAAA,uBAAuB,GAAG,KAAK,CAAA;AAC/B,QAAA,OAAA;AACF,OAAA;MAEAxX,OAAO,CACLuX,gBAAgB,CAACnG,IAAI,KAAK,CAAC,IAAIlQ,KAAK,IAAI,IAAI,EAC5C,oEAAoE,GAClE,wEAAwE,GACxE,uEAAuE,GACvE,yEAAyE,GACzE,iEAAiE,GACjE,yDACJ,CAAC,CAAA;MAED,IAAIwW,UAAU,GAAGC,qBAAqB,CAAC;QACrCC,eAAe,EAAE7Y,KAAK,CAACc,QAAQ;AAC/BmB,QAAAA,YAAY,EAAEnB,QAAQ;AACtB6V,QAAAA,aAAAA;AACF,OAAC,CAAC,CAAA;AAEF,MAAA,IAAIgC,UAAU,IAAIxW,KAAK,IAAI,IAAI,EAAE;AAC/B;AACAsW,QAAAA,uBAAuB,GAAG,IAAI,CAAA;QAC9BrJ,IAAI,CAAC7N,OAAO,CAACe,EAAE,CAACH,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;;AAE3B;QACA2W,aAAa,CAACH,UAAU,EAAE;AACxB3Y,UAAAA,KAAK,EAAE,SAAS;UAChBc,QAAQ;AACRsT,UAAAA,OAAOA,GAAG;YACR0E,aAAa,CAACH,UAAU,EAAG;AACzB3Y,cAAAA,KAAK,EAAE,YAAY;AACnBoU,cAAAA,OAAO,EAAEnU,SAAS;AAClBoU,cAAAA,KAAK,EAAEpU,SAAS;AAChBa,cAAAA,QAAAA;AACF,aAAC,CAAC,CAAA;AACF;AACAsO,YAAAA,IAAI,CAAC7N,OAAO,CAACe,EAAE,CAACH,KAAK,CAAC,CAAA;WACvB;AACDkS,UAAAA,KAAKA,GAAG;YACN,IAAI8C,QAAQ,GAAG,IAAID,GAAG,CAAClX,KAAK,CAACmX,QAAQ,CAAC,CAAA;AACtCA,YAAAA,QAAQ,CAACzH,GAAG,CAACiJ,UAAU,EAAGxE,YAAY,CAAC,CAAA;AACvC4E,YAAAA,WAAW,CAAC;AAAE5B,cAAAA,QAAAA;AAAS,aAAC,CAAC,CAAA;AAC3B,WAAA;AACF,SAAC,CAAC,CAAA;AACF,QAAA,OAAA;AACF,OAAA;AAEA,MAAA,OAAO6B,eAAe,CAACrC,aAAa,EAAE7V,QAAQ,CAAC,CAAA;AACjD,KACF,CAAC,CAAA;AAED,IAAA,IAAI+T,SAAS,EAAE;AACb;AACA;AACAoE,MAAAA,yBAAyB,CAACrE,YAAY,EAAE6C,sBAAsB,CAAC,CAAA;MAC/D,IAAIyB,uBAAuB,GAAGA,MAC5BC,yBAAyB,CAACvE,YAAY,EAAE6C,sBAAsB,CAAC,CAAA;AACjE7C,MAAAA,YAAY,CAAC7O,gBAAgB,CAAC,UAAU,EAAEmT,uBAAuB,CAAC,CAAA;MAClExB,2BAA2B,GAAGA,MAC5B9C,YAAY,CAAC5O,mBAAmB,CAAC,UAAU,EAAEkT,uBAAuB,CAAC,CAAA;AACzE,KAAA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAI,CAAClZ,KAAK,CAACkW,WAAW,EAAE;MACtB8C,eAAe,CAAC3B,MAAa,CAAChX,GAAG,EAAEL,KAAK,CAACc,QAAQ,EAAE;AACjDsY,QAAAA,gBAAgB,EAAE,IAAA;AACpB,OAAC,CAAC,CAAA;AACJ,KAAA;AAEA,IAAA,OAAO1C,MAAM,CAAA;AACf,GAAA;;AAEA;EACA,SAAS2C,OAAOA,GAAG;AACjB,IAAA,IAAI7D,eAAe,EAAE;AACnBA,MAAAA,eAAe,EAAE,CAAA;AACnB,KAAA;AACA,IAAA,IAAIkC,2BAA2B,EAAE;AAC/BA,MAAAA,2BAA2B,EAAE,CAAA;AAC/B,KAAA;IACA1H,WAAW,CAACsJ,KAAK,EAAE,CAAA;AACnB/B,IAAAA,2BAA2B,IAAIA,2BAA2B,CAACtF,KAAK,EAAE,CAAA;AAClEjS,IAAAA,KAAK,CAACiX,QAAQ,CAACnO,OAAO,CAAC,CAACgE,CAAC,EAAEjM,GAAG,KAAK0Y,aAAa,CAAC1Y,GAAG,CAAC,CAAC,CAAA;AACtDb,IAAAA,KAAK,CAACmX,QAAQ,CAACrO,OAAO,CAAC,CAACgE,CAAC,EAAEjM,GAAG,KAAK2Y,aAAa,CAAC3Y,GAAG,CAAC,CAAC,CAAA;AACxD,GAAA;;AAEA;EACA,SAASkR,SAASA,CAACtP,EAAoB,EAAE;AACvCuN,IAAAA,WAAW,CAACiB,GAAG,CAACxO,EAAE,CAAC,CAAA;AACnB,IAAA,OAAO,MAAMuN,WAAW,CAAC0B,MAAM,CAACjP,EAAE,CAAC,CAAA;AACrC,GAAA;;AAEA;AACA,EAAA,SAASsW,WAAWA,CAClBU,QAA8B,EAC9BC,IAGC,EACK;AAAA,IAAA,IAJNA,IAGC,KAAA,KAAA,CAAA,EAAA;MAHDA,IAGC,GAAG,EAAE,CAAA;AAAA,KAAA;AAEN1Z,IAAAA,KAAK,GAAA8E,QAAA,CAAA,EAAA,EACA9E,KAAK,EACLyZ,QAAQ,CACZ,CAAA;;AAED;AACA;IACA,IAAIE,iBAA2B,GAAG,EAAE,CAAA;IACpC,IAAIC,mBAA6B,GAAG,EAAE,CAAA;IAEtC,IAAIzE,MAAM,CAACC,iBAAiB,EAAE;MAC5BpV,KAAK,CAACiX,QAAQ,CAACnO,OAAO,CAAC,CAAC+Q,OAAO,EAAEhZ,GAAG,KAAK;AACvC,QAAA,IAAIgZ,OAAO,CAAC7Z,KAAK,KAAK,MAAM,EAAE;AAC5B,UAAA,IAAIsY,eAAe,CAAC7I,GAAG,CAAC5O,GAAG,CAAC,EAAE;AAC5B;AACA+Y,YAAAA,mBAAmB,CAAC7X,IAAI,CAAClB,GAAG,CAAC,CAAA;AAC/B,WAAC,MAAM;AACL;AACA;AACA8Y,YAAAA,iBAAiB,CAAC5X,IAAI,CAAClB,GAAG,CAAC,CAAA;AAC7B,WAAA;AACF,SAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAA;;AAEA;AACA;AACA;IACA,CAAC,GAAGmP,WAAW,CAAC,CAAClH,OAAO,CAAEgJ,UAAU,IAClCA,UAAU,CAAC9R,KAAK,EAAE;AAChBsY,MAAAA,eAAe,EAAEsB,mBAAmB;MACpCE,2BAA2B,EAAEJ,IAAI,CAACK,kBAAkB;AACpDC,MAAAA,kBAAkB,EAAEN,IAAI,CAACO,SAAS,KAAK,IAAA;AACzC,KAAC,CACH,CAAC,CAAA;;AAED;IACA,IAAI9E,MAAM,CAACC,iBAAiB,EAAE;AAC5BuE,MAAAA,iBAAiB,CAAC7Q,OAAO,CAAEjI,GAAG,IAAKb,KAAK,CAACiX,QAAQ,CAACvF,MAAM,CAAC7Q,GAAG,CAAC,CAAC,CAAA;MAC9D+Y,mBAAmB,CAAC9Q,OAAO,CAAEjI,GAAG,IAAK0Y,aAAa,CAAC1Y,GAAG,CAAC,CAAC,CAAA;AAC1D,KAAA;AACF,GAAA;;AAEA;AACA;AACA;AACA;AACA;AACA,EAAA,SAASqZ,kBAAkBA,CACzBpZ,QAAkB,EAClB2Y,QAA0E,EAAAU,KAAA,EAEpE;IAAA,IAAAC,eAAA,EAAAC,gBAAA,CAAA;IAAA,IADN;AAAEJ,MAAAA,SAAAA;AAAmC,KAAC,GAAAE,KAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,KAAA,CAAA;AAE3C;AACA;AACA;AACA;AACA;IACA,IAAIG,cAAc,GAChBta,KAAK,CAACgX,UAAU,IAAI,IAAI,IACxBhX,KAAK,CAAC4W,UAAU,CAAC/C,UAAU,IAAI,IAAI,IACnC0G,gBAAgB,CAACva,KAAK,CAAC4W,UAAU,CAAC/C,UAAU,CAAC,IAC7C7T,KAAK,CAAC4W,UAAU,CAAC5W,KAAK,KAAK,SAAS,IACpC,CAAA,CAAAoa,eAAA,GAAAtZ,QAAQ,CAACd,KAAK,KAAA,IAAA,GAAA,KAAA,CAAA,GAAdoa,eAAA,CAAgBI,WAAW,MAAK,IAAI,CAAA;AAEtC,IAAA,IAAIxD,UAA4B,CAAA;IAChC,IAAIyC,QAAQ,CAACzC,UAAU,EAAE;AACvB,MAAA,IAAIzL,MAAM,CAACkP,IAAI,CAAChB,QAAQ,CAACzC,UAAU,CAAC,CAAC7W,MAAM,GAAG,CAAC,EAAE;QAC/C6W,UAAU,GAAGyC,QAAQ,CAACzC,UAAU,CAAA;AAClC,OAAC,MAAM;AACL;AACAA,QAAAA,UAAU,GAAG,IAAI,CAAA;AACnB,OAAA;KACD,MAAM,IAAIsD,cAAc,EAAE;AACzB;MACAtD,UAAU,GAAGhX,KAAK,CAACgX,UAAU,CAAA;AAC/B,KAAC,MAAM;AACL;AACAA,MAAAA,UAAU,GAAG,IAAI,CAAA;AACnB,KAAA;;AAEA;AACA,IAAA,IAAIjP,UAAU,GAAG0R,QAAQ,CAAC1R,UAAU,GAChC2S,eAAe,CACb1a,KAAK,CAAC+H,UAAU,EAChB0R,QAAQ,CAAC1R,UAAU,EACnB0R,QAAQ,CAACjS,OAAO,IAAI,EAAE,EACtBiS,QAAQ,CAACjD,MACX,CAAC,GACDxW,KAAK,CAAC+H,UAAU,CAAA;;AAEpB;AACA;AACA,IAAA,IAAIoP,QAAQ,GAAGnX,KAAK,CAACmX,QAAQ,CAAA;AAC7B,IAAA,IAAIA,QAAQ,CAAC9E,IAAI,GAAG,CAAC,EAAE;AACrB8E,MAAAA,QAAQ,GAAG,IAAID,GAAG,CAACC,QAAQ,CAAC,CAAA;AAC5BA,MAAAA,QAAQ,CAACrO,OAAO,CAAC,CAACgE,CAAC,EAAEoF,CAAC,KAAKiF,QAAQ,CAACzH,GAAG,CAACwC,CAAC,EAAEiC,YAAY,CAAC,CAAC,CAAA;AAC3D,KAAA;;AAEA;AACA;AACA,IAAA,IAAI2C,kBAAkB,GACpBQ,yBAAyB,KAAK,IAAI,IACjCtX,KAAK,CAAC4W,UAAU,CAAC/C,UAAU,IAAI,IAAI,IAClC0G,gBAAgB,CAACva,KAAK,CAAC4W,UAAU,CAAC/C,UAAU,CAAC,IAC7C,EAAAwG,gBAAA,GAAAvZ,QAAQ,CAACd,KAAK,KAAdqa,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAA,CAAgBG,WAAW,MAAK,IAAK,CAAA;AAEzC,IAAA,IAAItF,kBAAkB,EAAE;AACtBD,MAAAA,UAAU,GAAGC,kBAAkB,CAAA;AAC/BA,MAAAA,kBAAkB,GAAGjV,SAAS,CAAA;AAChC,KAAA;AAEA,IAAA,IAAI0X,2BAA2B,EAAE,CAEhC,MAAM,IAAIP,aAAa,KAAKC,MAAa,CAAChX,GAAG,EAAE,CAE/C,MAAM,IAAI+W,aAAa,KAAKC,MAAa,CAACrV,IAAI,EAAE;MAC/CoN,IAAI,CAAC7N,OAAO,CAACQ,IAAI,CAACjB,QAAQ,EAAEA,QAAQ,CAACd,KAAK,CAAC,CAAA;AAC7C,KAAC,MAAM,IAAIoX,aAAa,KAAKC,MAAa,CAAChV,OAAO,EAAE;MAClD+M,IAAI,CAAC7N,OAAO,CAACa,OAAO,CAACtB,QAAQ,EAAEA,QAAQ,CAACd,KAAK,CAAC,CAAA;AAChD,KAAA;AAEA,IAAA,IAAI+Z,kBAAkD,CAAA;;AAEtD;AACA,IAAA,IAAI3C,aAAa,KAAKC,MAAa,CAAChX,GAAG,EAAE;AACvC;MACA,IAAIsa,UAAU,GAAGlD,sBAAsB,CAACjG,GAAG,CAACxR,KAAK,CAACc,QAAQ,CAACE,QAAQ,CAAC,CAAA;MACpE,IAAI2Z,UAAU,IAAIA,UAAU,CAAClL,GAAG,CAAC3O,QAAQ,CAACE,QAAQ,CAAC,EAAE;AACnD+Y,QAAAA,kBAAkB,GAAG;UACnBlB,eAAe,EAAE7Y,KAAK,CAACc,QAAQ;AAC/BmB,UAAAA,YAAY,EAAEnB,QAAAA;SACf,CAAA;OACF,MAAM,IAAI2W,sBAAsB,CAAChI,GAAG,CAAC3O,QAAQ,CAACE,QAAQ,CAAC,EAAE;AACxD;AACA;AACA+Y,QAAAA,kBAAkB,GAAG;AACnBlB,UAAAA,eAAe,EAAE/X,QAAQ;UACzBmB,YAAY,EAAEjC,KAAK,CAACc,QAAAA;SACrB,CAAA;AACH,OAAA;KACD,MAAM,IAAI0W,4BAA4B,EAAE;AACvC;MACA,IAAIoD,OAAO,GAAGnD,sBAAsB,CAACjG,GAAG,CAACxR,KAAK,CAACc,QAAQ,CAACE,QAAQ,CAAC,CAAA;AACjE,MAAA,IAAI4Z,OAAO,EAAE;AACXA,QAAAA,OAAO,CAAC3J,GAAG,CAACnQ,QAAQ,CAACE,QAAQ,CAAC,CAAA;AAChC,OAAC,MAAM;QACL4Z,OAAO,GAAG,IAAIzU,GAAG,CAAS,CAACrF,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAA;QAC9CyW,sBAAsB,CAAC/H,GAAG,CAAC1P,KAAK,CAACc,QAAQ,CAACE,QAAQ,EAAE4Z,OAAO,CAAC,CAAA;AAC9D,OAAA;AACAb,MAAAA,kBAAkB,GAAG;QACnBlB,eAAe,EAAE7Y,KAAK,CAACc,QAAQ;AAC/BmB,QAAAA,YAAY,EAAEnB,QAAAA;OACf,CAAA;AACH,KAAA;IAEAiY,WAAW,CAAAjU,QAAA,CAAA,EAAA,EAEJ2U,QAAQ,EAAA;AAAE;MACbzC,UAAU;MACVjP,UAAU;AACV4O,MAAAA,aAAa,EAAES,aAAa;MAC5BtW,QAAQ;AACRoV,MAAAA,WAAW,EAAE,IAAI;AACjBU,MAAAA,UAAU,EAAEhD,eAAe;AAC3BmD,MAAAA,YAAY,EAAE,MAAM;AACpBF,MAAAA,qBAAqB,EAAEgE,sBAAsB,CAC3C/Z,QAAQ,EACR2Y,QAAQ,CAACjS,OAAO,IAAIxH,KAAK,CAACwH,OAC5B,CAAC;MACDsP,kBAAkB;AAClBK,MAAAA,QAAAA;KAEF,CAAA,EAAA;MACE4C,kBAAkB;MAClBE,SAAS,EAAEA,SAAS,KAAK,IAAA;AAC3B,KACF,CAAC,CAAA;;AAED;IACA7C,aAAa,GAAGC,MAAa,CAAChX,GAAG,CAAA;AACjCiX,IAAAA,yBAAyB,GAAG,KAAK,CAAA;AACjCE,IAAAA,4BAA4B,GAAG,KAAK,CAAA;AACpCG,IAAAA,2BAA2B,GAAG,KAAK,CAAA;AACnCC,IAAAA,sBAAsB,GAAG,KAAK,CAAA;AAC9BC,IAAAA,uBAAuB,GAAG,EAAE,CAAA;AAC5BC,IAAAA,qBAAqB,GAAG,EAAE,CAAA;AAC5B,GAAA;;AAEA;AACA;AACA,EAAA,eAAegD,QAAQA,CACrBla,EAAsB,EACtB8Y,IAA4B,EACb;AACf,IAAA,IAAI,OAAO9Y,EAAE,KAAK,QAAQ,EAAE;AAC1BwO,MAAAA,IAAI,CAAC7N,OAAO,CAACe,EAAE,CAAC1B,EAAE,CAAC,CAAA;AACnB,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAAIma,cAAc,GAAGC,WAAW,CAC9Bhb,KAAK,CAACc,QAAQ,EACdd,KAAK,CAACwH,OAAO,EACbL,QAAQ,EACRgO,MAAM,CAACI,kBAAkB,EACzB3U,EAAE,EACFuU,MAAM,CAAChH,oBAAoB,EAC3BuL,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEuB,WAAW,EACjBvB,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAEwB,QACR,CAAC,CAAA;IACD,IAAI;MAAEvZ,IAAI;MAAEwZ,UAAU;AAAEzV,MAAAA,KAAAA;AAAM,KAAC,GAAG0V,wBAAwB,CACxDjG,MAAM,CAACE,sBAAsB,EAC7B,KAAK,EACL0F,cAAc,EACdrB,IACF,CAAC,CAAA;AAED,IAAA,IAAIb,eAAe,GAAG7Y,KAAK,CAACc,QAAQ,CAAA;AACpC,IAAA,IAAImB,YAAY,GAAGlB,cAAc,CAACf,KAAK,CAACc,QAAQ,EAAEa,IAAI,EAAE+X,IAAI,IAAIA,IAAI,CAAC1Z,KAAK,CAAC,CAAA;;AAE3E;AACA;AACA;AACA;AACA;AACAiC,IAAAA,YAAY,GAAA6C,QAAA,CACP7C,EAAAA,EAAAA,YAAY,EACZmN,IAAI,CAAC7N,OAAO,CAACG,cAAc,CAACO,YAAY,CAAC,CAC7C,CAAA;AAED,IAAA,IAAIoZ,WAAW,GAAG3B,IAAI,IAAIA,IAAI,CAACtX,OAAO,IAAI,IAAI,GAAGsX,IAAI,CAACtX,OAAO,GAAGnC,SAAS,CAAA;AAEzE,IAAA,IAAI0W,aAAa,GAAGU,MAAa,CAACrV,IAAI,CAAA;IAEtC,IAAIqZ,WAAW,KAAK,IAAI,EAAE;MACxB1E,aAAa,GAAGU,MAAa,CAAChV,OAAO,CAAA;AACvC,KAAC,MAAM,IAAIgZ,WAAW,KAAK,KAAK,EAAE,CAEjC,MAAM,IACLF,UAAU,IAAI,IAAI,IAClBZ,gBAAgB,CAACY,UAAU,CAACtH,UAAU,CAAC,IACvCsH,UAAU,CAACrH,UAAU,KAAK9T,KAAK,CAACc,QAAQ,CAACE,QAAQ,GAAGhB,KAAK,CAACc,QAAQ,CAACe,MAAM,EACzE;AACA;AACA;AACA;AACA;MACA8U,aAAa,GAAGU,MAAa,CAAChV,OAAO,CAAA;AACvC,KAAA;AAEA,IAAA,IAAIyU,kBAAkB,GACpB4C,IAAI,IAAI,oBAAoB,IAAIA,IAAI,GAChCA,IAAI,CAAC5C,kBAAkB,KAAK,IAAI,GAChC7W,SAAS,CAAA;IAEf,IAAIga,SAAS,GAAG,CAACP,IAAI,IAAIA,IAAI,CAACM,kBAAkB,MAAM,IAAI,CAAA;IAE1D,IAAIrB,UAAU,GAAGC,qBAAqB,CAAC;MACrCC,eAAe;MACf5W,YAAY;AACZ0U,MAAAA,aAAAA;AACF,KAAC,CAAC,CAAA;AAEF,IAAA,IAAIgC,UAAU,EAAE;AACd;MACAG,aAAa,CAACH,UAAU,EAAE;AACxB3Y,QAAAA,KAAK,EAAE,SAAS;AAChBc,QAAAA,QAAQ,EAAEmB,YAAY;AACtBmS,QAAAA,OAAOA,GAAG;UACR0E,aAAa,CAACH,UAAU,EAAG;AACzB3Y,YAAAA,KAAK,EAAE,YAAY;AACnBoU,YAAAA,OAAO,EAAEnU,SAAS;AAClBoU,YAAAA,KAAK,EAAEpU,SAAS;AAChBa,YAAAA,QAAQ,EAAEmB,YAAAA;AACZ,WAAC,CAAC,CAAA;AACF;AACA6Y,UAAAA,QAAQ,CAACla,EAAE,EAAE8Y,IAAI,CAAC,CAAA;SACnB;AACDrF,QAAAA,KAAKA,GAAG;UACN,IAAI8C,QAAQ,GAAG,IAAID,GAAG,CAAClX,KAAK,CAACmX,QAAQ,CAAC,CAAA;AACtCA,UAAAA,QAAQ,CAACzH,GAAG,CAACiJ,UAAU,EAAGxE,YAAY,CAAC,CAAA;AACvC4E,UAAAA,WAAW,CAAC;AAAE5B,YAAAA,QAAAA;AAAS,WAAC,CAAC,CAAA;AAC3B,SAAA;AACF,OAAC,CAAC,CAAA;AACF,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,MAAM6B,eAAe,CAACrC,aAAa,EAAE1U,YAAY,EAAE;MACxDkZ,UAAU;AACV;AACA;AACAG,MAAAA,YAAY,EAAE5V,KAAK;MACnBoR,kBAAkB;AAClB1U,MAAAA,OAAO,EAAEsX,IAAI,IAAIA,IAAI,CAACtX,OAAO;AAC7BmZ,MAAAA,oBAAoB,EAAE7B,IAAI,IAAIA,IAAI,CAAC8B,uBAAuB;AAC1DvB,MAAAA,SAAAA;AACF,KAAC,CAAC,CAAA;AACJ,GAAA;;AAEA;AACA;AACA;EACA,SAASwB,UAAUA,GAAG;AACpBC,IAAAA,oBAAoB,EAAE,CAAA;AACtB3C,IAAAA,WAAW,CAAC;AAAEhC,MAAAA,YAAY,EAAE,SAAA;AAAU,KAAC,CAAC,CAAA;;AAExC;AACA;AACA,IAAA,IAAI/W,KAAK,CAAC4W,UAAU,CAAC5W,KAAK,KAAK,YAAY,EAAE;AAC3C,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA;AACA,IAAA,IAAIA,KAAK,CAAC4W,UAAU,CAAC5W,KAAK,KAAK,MAAM,EAAE;MACrCgZ,eAAe,CAAChZ,KAAK,CAAC2W,aAAa,EAAE3W,KAAK,CAACc,QAAQ,EAAE;AACnD6a,QAAAA,8BAA8B,EAAE,IAAA;AAClC,OAAC,CAAC,CAAA;AACF,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA;AACA3C,IAAAA,eAAe,CACb5B,aAAa,IAAIpX,KAAK,CAAC2W,aAAa,EACpC3W,KAAK,CAAC4W,UAAU,CAAC9V,QAAQ,EACzB;MAAE8a,kBAAkB,EAAE5b,KAAK,CAAC4W,UAAAA;AAAW,KACzC,CAAC,CAAA;AACH,GAAA;;AAEA;AACA;AACA;AACA,EAAA,eAAeoC,eAAeA,CAC5BrC,aAA4B,EAC5B7V,QAAkB,EAClB4Y,IAWC,EACc;AACf;AACA;AACA;AACAnC,IAAAA,2BAA2B,IAAIA,2BAA2B,CAACtF,KAAK,EAAE,CAAA;AAClEsF,IAAAA,2BAA2B,GAAG,IAAI,CAAA;AAClCH,IAAAA,aAAa,GAAGT,aAAa,CAAA;IAC7BgB,2BAA2B,GACzB,CAAC+B,IAAI,IAAIA,IAAI,CAACiC,8BAA8B,MAAM,IAAI,CAAA;;AAExD;AACA;IACAE,kBAAkB,CAAC7b,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAACwH,OAAO,CAAC,CAAA;IACjD8P,yBAAyB,GAAG,CAACoC,IAAI,IAAIA,IAAI,CAAC5C,kBAAkB,MAAM,IAAI,CAAA;IAEtEU,4BAA4B,GAAG,CAACkC,IAAI,IAAIA,IAAI,CAAC6B,oBAAoB,MAAM,IAAI,CAAA;AAE3E,IAAA,IAAIO,WAAW,GAAG5G,kBAAkB,IAAID,UAAU,CAAA;AAClD,IAAA,IAAI8G,iBAAiB,GAAGrC,IAAI,IAAIA,IAAI,CAACkC,kBAAkB,CAAA;IACvD,IAAIpU,OAAO,GAAGP,WAAW,CAAC6U,WAAW,EAAEhb,QAAQ,EAAEqG,QAAQ,CAAC,CAAA;IAC1D,IAAI8S,SAAS,GAAG,CAACP,IAAI,IAAIA,IAAI,CAACO,SAAS,MAAM,IAAI,CAAA;;AAEjD;IACA,IAAI,CAACzS,OAAO,EAAE;AACZ,MAAA,IAAI9B,KAAK,GAAGsQ,sBAAsB,CAAC,GAAG,EAAE;QAAEhV,QAAQ,EAAEF,QAAQ,CAACE,QAAAA;AAAS,OAAC,CAAC,CAAA;MACxE,IAAI;AAAEwG,QAAAA,OAAO,EAAEwU,eAAe;AAAE3V,QAAAA,KAAAA;AAAM,OAAC,GACrC4P,sBAAsB,CAAC6F,WAAW,CAAC,CAAA;AACrC;AACAG,MAAAA,qBAAqB,EAAE,CAAA;MACvB/B,kBAAkB,CAChBpZ,QAAQ,EACR;AACE0G,QAAAA,OAAO,EAAEwU,eAAe;QACxBjU,UAAU,EAAE,EAAE;AACdyO,QAAAA,MAAM,EAAE;UACN,CAACnQ,KAAK,CAACO,EAAE,GAAGlB,KAAAA;AACd,SAAA;AACF,OAAC,EACD;AAAEuU,QAAAA,SAAAA;AAAU,OACd,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IACEja,KAAK,CAACkW,WAAW,IACjB,CAAC0B,sBAAsB,IACvBsE,gBAAgB,CAAClc,KAAK,CAACc,QAAQ,EAAEA,QAAQ,CAAC,IAC1C,EAAE4Y,IAAI,IAAIA,IAAI,CAACyB,UAAU,IAAIZ,gBAAgB,CAACb,IAAI,CAACyB,UAAU,CAACtH,UAAU,CAAC,CAAC,EAC1E;MACAqG,kBAAkB,CAACpZ,QAAQ,EAAE;AAAE0G,QAAAA,OAAAA;AAAQ,OAAC,EAAE;AAAEyS,QAAAA,SAAAA;AAAU,OAAC,CAAC,CAAA;AACxD,MAAA,OAAA;AACF,KAAA;;AAEA;AACA1C,IAAAA,2BAA2B,GAAG,IAAI9G,eAAe,EAAE,CAAA;AACnD,IAAA,IAAI0L,OAAO,GAAGC,uBAAuB,CACnChN,IAAI,CAAC7N,OAAO,EACZT,QAAQ,EACRyW,2BAA2B,CAAC3G,MAAM,EAClC8I,IAAI,IAAIA,IAAI,CAACyB,UACf,CAAC,CAAA;AACD,IAAA,IAAIkB,iBAAwC,CAAA;AAC5C,IAAA,IAAIf,YAAmC,CAAA;AAEvC,IAAA,IAAI5B,IAAI,IAAIA,IAAI,CAAC4B,YAAY,EAAE;AAC7B;AACA;AACA;AACA;AACAA,MAAAA,YAAY,GAAG;QACb,CAACgB,mBAAmB,CAAC9U,OAAO,CAAC,CAACnB,KAAK,CAACO,EAAE,GAAG8S,IAAI,CAAC4B,YAAAA;OAC/C,CAAA;AACH,KAAC,MAAM,IACL5B,IAAI,IACJA,IAAI,CAACyB,UAAU,IACfZ,gBAAgB,CAACb,IAAI,CAACyB,UAAU,CAACtH,UAAU,CAAC,EAC5C;AACA;AACA,MAAA,IAAI0I,YAAY,GAAG,MAAMC,YAAY,CACnCL,OAAO,EACPrb,QAAQ,EACR4Y,IAAI,CAACyB,UAAU,EACf3T,OAAO,EACP;QAAEpF,OAAO,EAAEsX,IAAI,CAACtX,OAAO;AAAE6X,QAAAA,SAAAA;AAAU,OACrC,CAAC,CAAA;MAED,IAAIsC,YAAY,CAACE,cAAc,EAAE;AAC/B,QAAA,OAAA;AACF,OAAA;MAEAJ,iBAAiB,GAAGE,YAAY,CAACF,iBAAiB,CAAA;MAClDf,YAAY,GAAGiB,YAAY,CAACG,kBAAkB,CAAA;MAC9CX,iBAAiB,GAAGY,oBAAoB,CAAC7b,QAAQ,EAAE4Y,IAAI,CAACyB,UAAU,CAAC,CAAA;AACnElB,MAAAA,SAAS,GAAG,KAAK,CAAA;;AAEjB;AACAkC,MAAAA,OAAO,GAAG,IAAIS,OAAO,CAACT,OAAO,CAACxY,GAAG,EAAE;QAAEiN,MAAM,EAAEuL,OAAO,CAACvL,MAAAA;AAAO,OAAC,CAAC,CAAA;AAChE,KAAA;;AAEA;IACA,IAAI;MAAE6L,cAAc;MAAE1U,UAAU;AAAEyO,MAAAA,MAAAA;AAAO,KAAC,GAAG,MAAMqG,aAAa,CAC9DV,OAAO,EACPrb,QAAQ,EACR0G,OAAO,EACPuU,iBAAiB,EACjBrC,IAAI,IAAIA,IAAI,CAACyB,UAAU,EACvBzB,IAAI,IAAIA,IAAI,CAACoD,iBAAiB,EAC9BpD,IAAI,IAAIA,IAAI,CAACtX,OAAO,EACpBsX,IAAI,IAAIA,IAAI,CAACN,gBAAgB,KAAK,IAAI,EACtCa,SAAS,EACToC,iBAAiB,EACjBf,YACF,CAAC,CAAA;AAED,IAAA,IAAImB,cAAc,EAAE;AAClB,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA;AACAlF,IAAAA,2BAA2B,GAAG,IAAI,CAAA;IAElC2C,kBAAkB,CAACpZ,QAAQ,EAAAgE,QAAA,CAAA;AACzB0C,MAAAA,OAAAA;AAAO,KAAA,EACH6U,iBAAiB,GAAG;AAAErF,MAAAA,UAAU,EAAEqF,iBAAAA;KAAmB,GAAG,EAAE,EAAA;MAC9DtU,UAAU;AACVyO,MAAAA,MAAAA;AAAM,KAAA,CACP,CAAC,CAAA;AACJ,GAAA;;AAEA;AACA;EACA,eAAegG,YAAYA,CACzBL,OAAgB,EAChBrb,QAAkB,EAClBqa,UAAsB,EACtB3T,OAAiC,EACjCkS,IAAgD,EACnB;AAAA,IAAA,IAD7BA,IAAgD,KAAA,KAAA,CAAA,EAAA;MAAhDA,IAAgD,GAAG,EAAE,CAAA;AAAA,KAAA;AAErDgC,IAAAA,oBAAoB,EAAE,CAAA;;AAEtB;AACA,IAAA,IAAI9E,UAAU,GAAGmG,uBAAuB,CAACjc,QAAQ,EAAEqa,UAAU,CAAC,CAAA;AAC9DpC,IAAAA,WAAW,CAAC;AAAEnC,MAAAA,UAAAA;AAAW,KAAC,EAAE;AAAEqD,MAAAA,SAAS,EAAEP,IAAI,CAACO,SAAS,KAAK,IAAA;AAAK,KAAC,CAAC,CAAA;;AAEnE;AACA,IAAA,IAAItQ,MAAkB,CAAA;AACtB,IAAA,IAAIqT,WAAW,GAAGC,cAAc,CAACzV,OAAO,EAAE1G,QAAQ,CAAC,CAAA;AAEnD,IAAA,IAAI,CAACkc,WAAW,CAAC3W,KAAK,CAACjG,MAAM,IAAI,CAAC4c,WAAW,CAAC3W,KAAK,CAACgQ,IAAI,EAAE;AACxD1M,MAAAA,MAAM,GAAG;QACPuT,IAAI,EAAEjX,UAAU,CAACP,KAAK;AACtBA,QAAAA,KAAK,EAAEsQ,sBAAsB,CAAC,GAAG,EAAE;UACjCmH,MAAM,EAAEhB,OAAO,CAACgB,MAAM;UACtBnc,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;AAC3Boc,UAAAA,OAAO,EAAEJ,WAAW,CAAC3W,KAAK,CAACO,EAAAA;SAC5B,CAAA;OACF,CAAA;AACH,KAAC,MAAM;MACL+C,MAAM,GAAG,MAAM0T,kBAAkB,CAC/B,QAAQ,EACRlB,OAAO,EACPa,WAAW,EACXxV,OAAO,EACPd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,EACRgO,MAAM,CAAChH,oBACT,CAAC,CAAA;AAED,MAAA,IAAIgO,OAAO,CAACvL,MAAM,CAACa,OAAO,EAAE;QAC1B,OAAO;AAAEgL,UAAAA,cAAc,EAAE,IAAA;SAAM,CAAA;AACjC,OAAA;AACF,KAAA;AAEA,IAAA,IAAIa,gBAAgB,CAAC3T,MAAM,CAAC,EAAE;AAC5B,MAAA,IAAIvH,OAAgB,CAAA;AACpB,MAAA,IAAIsX,IAAI,IAAIA,IAAI,CAACtX,OAAO,IAAI,IAAI,EAAE;QAChCA,OAAO,GAAGsX,IAAI,CAACtX,OAAO,CAAA;AACxB,OAAC,MAAM;AACL;AACA;AACA;AACAA,QAAAA,OAAO,GACLuH,MAAM,CAAC7I,QAAQ,KAAKd,KAAK,CAACc,QAAQ,CAACE,QAAQ,GAAGhB,KAAK,CAACc,QAAQ,CAACe,MAAM,CAAA;AACvE,OAAA;AACA,MAAA,MAAM0b,uBAAuB,CAACvd,KAAK,EAAE2J,MAAM,EAAE;QAAEwR,UAAU;AAAE/Y,QAAAA,OAAAA;AAAQ,OAAC,CAAC,CAAA;MACrE,OAAO;AAAEqa,QAAAA,cAAc,EAAE,IAAA;OAAM,CAAA;AACjC,KAAA;AAEA,IAAA,IAAIe,aAAa,CAAC7T,MAAM,CAAC,EAAE;AACzB;AACA;MACA,IAAI8T,aAAa,GAAGnB,mBAAmB,CAAC9U,OAAO,EAAEwV,WAAW,CAAC3W,KAAK,CAACO,EAAE,CAAC,CAAA;;AAEtE;AACA;AACA;AACA;MACA,IAAI,CAAC8S,IAAI,IAAIA,IAAI,CAACtX,OAAO,MAAM,IAAI,EAAE;QACnCgV,aAAa,GAAGC,MAAa,CAACrV,IAAI,CAAA;AACpC,OAAA;MAEA,OAAO;AACL;QACAqa,iBAAiB,EAAE,EAAE;AACrBK,QAAAA,kBAAkB,EAAE;AAAE,UAAA,CAACe,aAAa,CAACpX,KAAK,CAACO,EAAE,GAAG+C,MAAM,CAACjE,KAAAA;AAAM,SAAA;OAC9D,CAAA;AACH,KAAA;AAEA,IAAA,IAAIgY,gBAAgB,CAAC/T,MAAM,CAAC,EAAE;MAC5B,MAAMqM,sBAAsB,CAAC,GAAG,EAAE;AAAEkH,QAAAA,IAAI,EAAE,cAAA;AAAe,OAAC,CAAC,CAAA;AAC7D,KAAA;IAEA,OAAO;AACLb,MAAAA,iBAAiB,EAAE;AAAE,QAAA,CAACW,WAAW,CAAC3W,KAAK,CAACO,EAAE,GAAG+C,MAAM,CAAC1B,IAAAA;AAAK,OAAA;KAC1D,CAAA;AACH,GAAA;;AAEA;AACA;EACA,eAAe4U,aAAaA,CAC1BV,OAAgB,EAChBrb,QAAkB,EAClB0G,OAAiC,EACjCoU,kBAA+B,EAC/BT,UAAuB,EACvB2B,iBAA8B,EAC9B1a,OAAiB,EACjBgX,gBAA0B,EAC1Ba,SAAmB,EACnBoC,iBAA6B,EAC7Bf,YAAwB,EACM;AAC9B;IACA,IAAIS,iBAAiB,GACnBH,kBAAkB,IAAIe,oBAAoB,CAAC7b,QAAQ,EAAEqa,UAAU,CAAC,CAAA;;AAElE;AACA;IACA,IAAIwC,gBAAgB,GAClBxC,UAAU,IACV2B,iBAAiB,IACjBc,2BAA2B,CAAC7B,iBAAiB,CAAC,CAAA;AAEhD,IAAA,IAAID,WAAW,GAAG5G,kBAAkB,IAAID,UAAU,CAAA;IAClD,IAAI,CAAC4I,aAAa,EAAEC,oBAAoB,CAAC,GAAGC,gBAAgB,CAC1D3O,IAAI,CAAC7N,OAAO,EACZvB,KAAK,EACLwH,OAAO,EACPmW,gBAAgB,EAChB7c,QAAQ,EACRqU,MAAM,CAACG,mBAAmB,IAAI8D,gBAAgB,KAAK,IAAI,EACvDxB,sBAAsB,EACtBC,uBAAuB,EACvBC,qBAAqB,EACrBQ,eAAe,EACfF,gBAAgB,EAChBD,gBAAgB,EAChB2D,WAAW,EACX3U,QAAQ,EACRkV,iBAAiB,EACjBf,YACF,CAAC,CAAA;;AAED;AACA;AACA;AACAW,IAAAA,qBAAqB,CAClBmB,OAAO,IACN,EAAE5V,OAAO,IAAIA,OAAO,CAACkD,IAAI,CAAE0L,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACO,EAAE,KAAKwW,OAAO,CAAC,CAAC,IACxDS,aAAa,IAAIA,aAAa,CAACnT,IAAI,CAAE0L,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACO,EAAE,KAAKwW,OAAO,CACtE,CAAC,CAAA;IAEDnF,uBAAuB,GAAG,EAAED,kBAAkB,CAAA;;AAE9C;IACA,IAAI6F,aAAa,CAAC1d,MAAM,KAAK,CAAC,IAAI2d,oBAAoB,CAAC3d,MAAM,KAAK,CAAC,EAAE;AACnE,MAAA,IAAI6d,eAAe,GAAGC,sBAAsB,EAAE,CAAA;MAC9C/D,kBAAkB,CAChBpZ,QAAQ,EAAAgE,QAAA,CAAA;QAEN0C,OAAO;QACPO,UAAU,EAAE,EAAE;AACd;QACAyO,MAAM,EAAE8E,YAAY,IAAI,IAAA;AAAI,OAAA,EACxBe,iBAAiB,GAAG;AAAErF,QAAAA,UAAU,EAAEqF,iBAAAA;AAAkB,OAAC,GAAG,EAAE,EAC1D2B,eAAe,GAAG;AAAE/G,QAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAClX,KAAK,CAACiX,QAAQ,CAAA;OAAG,GAAG,EAAE,CAElE,EAAA;AAAEgD,QAAAA,SAAAA;AAAU,OACd,CAAC,CAAA;MACD,OAAO;AAAEwC,QAAAA,cAAc,EAAE,IAAA;OAAM,CAAA;AACjC,KAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;IACA,IACE,CAAC9E,2BAA2B,KAC3B,CAACxC,MAAM,CAACG,mBAAmB,IAAI,CAAC8D,gBAAgB,CAAC,EAClD;AACA0E,MAAAA,oBAAoB,CAAChV,OAAO,CAAEoV,EAAE,IAAK;QACnC,IAAIrE,OAAO,GAAG7Z,KAAK,CAACiX,QAAQ,CAACzF,GAAG,CAAC0M,EAAE,CAACrd,GAAG,CAAC,CAAA;AACxC,QAAA,IAAIsd,mBAAmB,GAAGC,iBAAiB,CACzCne,SAAS,EACT4Z,OAAO,GAAGA,OAAO,CAAC5R,IAAI,GAAGhI,SAC3B,CAAC,CAAA;QACDD,KAAK,CAACiX,QAAQ,CAACvH,GAAG,CAACwO,EAAE,CAACrd,GAAG,EAAEsd,mBAAmB,CAAC,CAAA;AACjD,OAAC,CAAC,CAAA;AACF,MAAA,IAAInH,UAAU,GAAGqF,iBAAiB,IAAIrc,KAAK,CAACgX,UAAU,CAAA;AACtD+B,MAAAA,WAAW,CAAAjU,QAAA,CAAA;AAEP8R,QAAAA,UAAU,EAAEmF,iBAAAA;AAAiB,OAAA,EACzB/E,UAAU,GACVzL,MAAM,CAACkP,IAAI,CAACzD,UAAU,CAAC,CAAC7W,MAAM,KAAK,CAAC,GAClC;AAAE6W,QAAAA,UAAU,EAAE,IAAA;AAAK,OAAC,GACpB;AAAEA,QAAAA,UAAAA;OAAY,GAChB,EAAE,EACF8G,oBAAoB,CAAC3d,MAAM,GAAG,CAAC,GAC/B;AAAE8W,QAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAClX,KAAK,CAACiX,QAAQ,CAAA;OAAG,GACrC,EAAE,CAER,EAAA;AACEgD,QAAAA,SAAAA;AACF,OACF,CAAC,CAAA;AACH,KAAA;AAEA6D,IAAAA,oBAAoB,CAAChV,OAAO,CAAEoV,EAAE,IAAK;MACnC,IAAInG,gBAAgB,CAACtI,GAAG,CAACyO,EAAE,CAACrd,GAAG,CAAC,EAAE;AAChCwd,QAAAA,YAAY,CAACH,EAAE,CAACrd,GAAG,CAAC,CAAA;AACtB,OAAA;MACA,IAAIqd,EAAE,CAAC1N,UAAU,EAAE;AACjB;AACA;AACA;QACAuH,gBAAgB,CAACrI,GAAG,CAACwO,EAAE,CAACrd,GAAG,EAAEqd,EAAE,CAAC1N,UAAU,CAAC,CAAA;AAC7C,OAAA;AACF,KAAC,CAAC,CAAA;;AAEF;AACA,IAAA,IAAI8N,8BAA8B,GAAGA,MACnCR,oBAAoB,CAAChV,OAAO,CAAEyV,CAAC,IAAKF,YAAY,CAACE,CAAC,CAAC1d,GAAG,CAAC,CAAC,CAAA;AAC1D,IAAA,IAAI0W,2BAA2B,EAAE;MAC/BA,2BAA2B,CAAC3G,MAAM,CAAC7K,gBAAgB,CACjD,OAAO,EACPuY,8BACF,CAAC,CAAA;AACH,KAAA;IAEA,IAAI;MAAEE,OAAO;MAAEC,aAAa;AAAEC,MAAAA,cAAAA;AAAe,KAAC,GAC5C,MAAMC,8BAA8B,CAClC3e,KAAK,CAACwH,OAAO,EACbA,OAAO,EACPqW,aAAa,EACbC,oBAAoB,EACpB3B,OACF,CAAC,CAAA;AAEH,IAAA,IAAIA,OAAO,CAACvL,MAAM,CAACa,OAAO,EAAE;MAC1B,OAAO;AAAEgL,QAAAA,cAAc,EAAE,IAAA;OAAM,CAAA;AACjC,KAAA;;AAEA;AACA;AACA;AACA,IAAA,IAAIlF,2BAA2B,EAAE;MAC/BA,2BAA2B,CAAC3G,MAAM,CAAC5K,mBAAmB,CACpD,OAAO,EACPsY,8BACF,CAAC,CAAA;AACH,KAAA;AACAR,IAAAA,oBAAoB,CAAChV,OAAO,CAAEoV,EAAE,IAAKnG,gBAAgB,CAACrG,MAAM,CAACwM,EAAE,CAACrd,GAAG,CAAC,CAAC,CAAA;;AAErE;AACA,IAAA,IAAIkS,QAAQ,GAAG6L,YAAY,CAACJ,OAAO,CAAC,CAAA;AACpC,IAAA,IAAIzL,QAAQ,EAAE;AACZ,MAAA,IAAIA,QAAQ,CAACnO,GAAG,IAAIiZ,aAAa,CAAC1d,MAAM,EAAE;AACxC;AACA;AACA;AACA,QAAA,IAAI0e,UAAU,GACZf,oBAAoB,CAAC/K,QAAQ,CAACnO,GAAG,GAAGiZ,aAAa,CAAC1d,MAAM,CAAC,CAACU,GAAG,CAAA;AAC/DsX,QAAAA,gBAAgB,CAAClH,GAAG,CAAC4N,UAAU,CAAC,CAAA;AAClC,OAAA;AACA,MAAA,MAAMtB,uBAAuB,CAACvd,KAAK,EAAE+S,QAAQ,CAACpJ,MAAM,EAAE;AAAEvH,QAAAA,OAAAA;AAAQ,OAAC,CAAC,CAAA;MAClE,OAAO;AAAEqa,QAAAA,cAAc,EAAE,IAAA;OAAM,CAAA;AACjC,KAAA;;AAEA;IACA,IAAI;MAAE1U,UAAU;AAAEyO,MAAAA,MAAAA;AAAO,KAAC,GAAGsI,iBAAiB,CAC5C9e,KAAK,EACLwH,OAAO,EACPqW,aAAa,EACbY,aAAa,EACbnD,YAAY,EACZwC,oBAAoB,EACpBY,cAAc,EACdnG,eACF,CAAC,CAAA;;AAED;AACAA,IAAAA,eAAe,CAACzP,OAAO,CAAC,CAACiW,YAAY,EAAE3B,OAAO,KAAK;AACjD2B,MAAAA,YAAY,CAAChN,SAAS,CAAEN,OAAO,IAAK;AAClC;AACA;AACA;AACA,QAAA,IAAIA,OAAO,IAAIsN,YAAY,CAAC/N,IAAI,EAAE;AAChCuH,UAAAA,eAAe,CAAC7G,MAAM,CAAC0L,OAAO,CAAC,CAAA;AACjC,SAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAC,CAAC,CAAA;AAEF,IAAA,IAAIY,eAAe,GAAGC,sBAAsB,EAAE,CAAA;AAC9C,IAAA,IAAIe,kBAAkB,GAAGC,oBAAoB,CAAChH,uBAAuB,CAAC,CAAA;IACtE,IAAIiH,oBAAoB,GACtBlB,eAAe,IAAIgB,kBAAkB,IAAIlB,oBAAoB,CAAC3d,MAAM,GAAG,CAAC,CAAA;AAE1E,IAAA,OAAA2E,QAAA,CAAA;MACEiD,UAAU;AACVyO,MAAAA,MAAAA;AAAM,KAAA,EACF0I,oBAAoB,GAAG;AAAEjI,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAClX,KAAK,CAACiX,QAAQ,CAAA;KAAG,GAAG,EAAE,CAAA,CAAA;AAEzE,GAAA;;AAEA;EACA,SAASkI,KAAKA,CACZte,GAAW,EACXuc,OAAe,EACf3Z,IAAmB,EACnBiW,IAAyB,EACzB;AACA,IAAA,IAAI3E,QAAQ,EAAE;MACZ,MAAM,IAAI5Q,KAAK,CACb,2EAA2E,GACzE,8EAA8E,GAC9E,6CACJ,CAAC,CAAA;AACH,KAAA;IAEA,IAAI4T,gBAAgB,CAACtI,GAAG,CAAC5O,GAAG,CAAC,EAAEwd,YAAY,CAACxd,GAAG,CAAC,CAAA;IAChD,IAAIoZ,SAAS,GAAG,CAACP,IAAI,IAAIA,IAAI,CAACM,kBAAkB,MAAM,IAAI,CAAA;AAE1D,IAAA,IAAI8B,WAAW,GAAG5G,kBAAkB,IAAID,UAAU,CAAA;AAClD,IAAA,IAAI8F,cAAc,GAAGC,WAAW,CAC9Bhb,KAAK,CAACc,QAAQ,EACdd,KAAK,CAACwH,OAAO,EACbL,QAAQ,EACRgO,MAAM,CAACI,kBAAkB,EACzB9R,IAAI,EACJ0R,MAAM,CAAChH,oBAAoB,EAC3BiP,OAAO,EACP1D,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAEwB,QACR,CAAC,CAAA;IACD,IAAI1T,OAAO,GAAGP,WAAW,CAAC6U,WAAW,EAAEf,cAAc,EAAE5T,QAAQ,CAAC,CAAA;IAEhE,IAAI,CAACK,OAAO,EAAE;MACZ4X,eAAe,CACbve,GAAG,EACHuc,OAAO,EACPpH,sBAAsB,CAAC,GAAG,EAAE;AAAEhV,QAAAA,QAAQ,EAAE+Z,cAAAA;AAAe,OAAC,CAAC,EACzD;AAAEd,QAAAA,SAAAA;AAAU,OACd,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;IAEA,IAAI;MAAEtY,IAAI;MAAEwZ,UAAU;AAAEzV,MAAAA,KAAAA;AAAM,KAAC,GAAG0V,wBAAwB,CACxDjG,MAAM,CAACE,sBAAsB,EAC7B,IAAI,EACJ0F,cAAc,EACdrB,IACF,CAAC,CAAA;AAED,IAAA,IAAIhU,KAAK,EAAE;AACT0Z,MAAAA,eAAe,CAACve,GAAG,EAAEuc,OAAO,EAAE1X,KAAK,EAAE;AAAEuU,QAAAA,SAAAA;AAAU,OAAC,CAAC,CAAA;AACnD,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAAInS,KAAK,GAAGmV,cAAc,CAACzV,OAAO,EAAE7F,IAAI,CAAC,CAAA;IAEzC2V,yBAAyB,GAAG,CAACoC,IAAI,IAAIA,IAAI,CAAC5C,kBAAkB,MAAM,IAAI,CAAA;IAEtE,IAAIqE,UAAU,IAAIZ,gBAAgB,CAACY,UAAU,CAACtH,UAAU,CAAC,EAAE;AACzDwL,MAAAA,mBAAmB,CACjBxe,GAAG,EACHuc,OAAO,EACPzb,IAAI,EACJmG,KAAK,EACLN,OAAO,EACPyS,SAAS,EACTkB,UACF,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA/C,IAAAA,gBAAgB,CAAC1I,GAAG,CAAC7O,GAAG,EAAE;MAAEuc,OAAO;AAAEzb,MAAAA,IAAAA;AAAK,KAAC,CAAC,CAAA;AAC5C2d,IAAAA,mBAAmB,CACjBze,GAAG,EACHuc,OAAO,EACPzb,IAAI,EACJmG,KAAK,EACLN,OAAO,EACPyS,SAAS,EACTkB,UACF,CAAC,CAAA;AACH,GAAA;;AAEA;AACA;AACA,EAAA,eAAekE,mBAAmBA,CAChCxe,GAAW,EACXuc,OAAe,EACfzb,IAAY,EACZmG,KAA6B,EAC7ByX,cAAwC,EACxCtF,SAAkB,EAClBkB,UAAsB,EACtB;AACAO,IAAAA,oBAAoB,EAAE,CAAA;AACtBtD,IAAAA,gBAAgB,CAAC1G,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAE5B,IAAA,IAAI,CAACiH,KAAK,CAACzB,KAAK,CAACjG,MAAM,IAAI,CAAC0H,KAAK,CAACzB,KAAK,CAACgQ,IAAI,EAAE;AAC5C,MAAA,IAAI3Q,KAAK,GAAGsQ,sBAAsB,CAAC,GAAG,EAAE;QACtCmH,MAAM,EAAEhC,UAAU,CAACtH,UAAU;AAC7B7S,QAAAA,QAAQ,EAAEW,IAAI;AACdyb,QAAAA,OAAO,EAAEA,OAAAA;AACX,OAAC,CAAC,CAAA;AACFgC,MAAAA,eAAe,CAACve,GAAG,EAAEuc,OAAO,EAAE1X,KAAK,EAAE;AAAEuU,QAAAA,SAAAA;AAAU,OAAC,CAAC,CAAA;AACnD,MAAA,OAAA;AACF,KAAA;;AAEA;IACA,IAAIuF,eAAe,GAAGxf,KAAK,CAACiX,QAAQ,CAACzF,GAAG,CAAC3Q,GAAG,CAAC,CAAA;IAC7C4e,kBAAkB,CAAC5e,GAAG,EAAE6e,oBAAoB,CAACvE,UAAU,EAAEqE,eAAe,CAAC,EAAE;AACzEvF,MAAAA,SAAAA;AACF,KAAC,CAAC,CAAA;;AAEF;AACA,IAAA,IAAI0F,eAAe,GAAG,IAAIlP,eAAe,EAAE,CAAA;AAC3C,IAAA,IAAImP,YAAY,GAAGxD,uBAAuB,CACxChN,IAAI,CAAC7N,OAAO,EACZI,IAAI,EACJge,eAAe,CAAC/O,MAAM,EACtBuK,UACF,CAAC,CAAA;AACDpD,IAAAA,gBAAgB,CAACrI,GAAG,CAAC7O,GAAG,EAAE8e,eAAe,CAAC,CAAA;IAE1C,IAAIE,iBAAiB,GAAG7H,kBAAkB,CAAA;IAC1C,IAAI8H,YAAY,GAAG,MAAMzC,kBAAkB,CACzC,QAAQ,EACRuC,YAAY,EACZ9X,KAAK,EACLyX,cAAc,EACd7Y,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,EACRgO,MAAM,CAAChH,oBACT,CAAC,CAAA;AAED,IAAA,IAAIyR,YAAY,CAAChP,MAAM,CAACa,OAAO,EAAE;AAC/B;AACA;MACA,IAAIsG,gBAAgB,CAACvG,GAAG,CAAC3Q,GAAG,CAAC,KAAK8e,eAAe,EAAE;AACjD5H,QAAAA,gBAAgB,CAACrG,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC9B,OAAA;AACA,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA;IACA,IAAIsU,MAAM,CAACC,iBAAiB,IAAIkD,eAAe,CAAC7I,GAAG,CAAC5O,GAAG,CAAC,EAAE;MACxD,IAAIyc,gBAAgB,CAACwC,YAAY,CAAC,IAAItC,aAAa,CAACsC,YAAY,CAAC,EAAE;AACjEL,QAAAA,kBAAkB,CAAC5e,GAAG,EAAEkf,cAAc,CAAC9f,SAAS,CAAC,CAAC,CAAA;AAClD,QAAA,OAAA;AACF,OAAA;AACA;AACF,KAAC,MAAM;AACL,MAAA,IAAIqd,gBAAgB,CAACwC,YAAY,CAAC,EAAE;AAClC/H,QAAAA,gBAAgB,CAACrG,MAAM,CAAC7Q,GAAG,CAAC,CAAA;QAC5B,IAAIoX,uBAAuB,GAAG4H,iBAAiB,EAAE;AAC/C;AACA;AACA;AACA;AACAJ,UAAAA,kBAAkB,CAAC5e,GAAG,EAAEkf,cAAc,CAAC9f,SAAS,CAAC,CAAC,CAAA;AAClD,UAAA,OAAA;AACF,SAAC,MAAM;AACLkY,UAAAA,gBAAgB,CAAClH,GAAG,CAACpQ,GAAG,CAAC,CAAA;AACzB4e,UAAAA,kBAAkB,CAAC5e,GAAG,EAAEud,iBAAiB,CAACjD,UAAU,CAAC,CAAC,CAAA;AACtD,UAAA,OAAOoC,uBAAuB,CAACvd,KAAK,EAAE8f,YAAY,EAAE;AAClDhD,YAAAA,iBAAiB,EAAE3B,UAAAA;AACrB,WAAC,CAAC,CAAA;AACJ,SAAA;AACF,OAAA;;AAEA;AACA,MAAA,IAAIqC,aAAa,CAACsC,YAAY,CAAC,EAAE;QAC/BV,eAAe,CAACve,GAAG,EAAEuc,OAAO,EAAE0C,YAAY,CAACpa,KAAK,CAAC,CAAA;AACjD,QAAA,OAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,IAAIgY,gBAAgB,CAACoC,YAAY,CAAC,EAAE;MAClC,MAAM9J,sBAAsB,CAAC,GAAG,EAAE;AAAEkH,QAAAA,IAAI,EAAE,cAAA;AAAe,OAAC,CAAC,CAAA;AAC7D,KAAA;;AAEA;AACA;IACA,IAAIjb,YAAY,GAAGjC,KAAK,CAAC4W,UAAU,CAAC9V,QAAQ,IAAId,KAAK,CAACc,QAAQ,CAAA;AAC9D,IAAA,IAAIkf,mBAAmB,GAAG5D,uBAAuB,CAC/ChN,IAAI,CAAC7N,OAAO,EACZU,YAAY,EACZ0d,eAAe,CAAC/O,MAClB,CAAC,CAAA;AACD,IAAA,IAAIkL,WAAW,GAAG5G,kBAAkB,IAAID,UAAU,CAAA;IAClD,IAAIzN,OAAO,GACTxH,KAAK,CAAC4W,UAAU,CAAC5W,KAAK,KAAK,MAAM,GAC7BiH,WAAW,CAAC6U,WAAW,EAAE9b,KAAK,CAAC4W,UAAU,CAAC9V,QAAQ,EAAEqG,QAAQ,CAAC,GAC7DnH,KAAK,CAACwH,OAAO,CAAA;AAEnBxD,IAAAA,SAAS,CAACwD,OAAO,EAAE,8CAA8C,CAAC,CAAA;IAElE,IAAIyY,MAAM,GAAG,EAAEjI,kBAAkB,CAAA;AACjCE,IAAAA,cAAc,CAACxI,GAAG,CAAC7O,GAAG,EAAEof,MAAM,CAAC,CAAA;IAE/B,IAAIC,WAAW,GAAG9B,iBAAiB,CAACjD,UAAU,EAAE2E,YAAY,CAAC7X,IAAI,CAAC,CAAA;IAClEjI,KAAK,CAACiX,QAAQ,CAACvH,GAAG,CAAC7O,GAAG,EAAEqf,WAAW,CAAC,CAAA;AAEpC,IAAA,IAAI,CAACrC,aAAa,EAAEC,oBAAoB,CAAC,GAAGC,gBAAgB,CAC1D3O,IAAI,CAAC7N,OAAO,EACZvB,KAAK,EACLwH,OAAO,EACP2T,UAAU,EACVlZ,YAAY,EACZ,KAAK,EACL2V,sBAAsB,EACtBC,uBAAuB,EACvBC,qBAAqB,EACrBQ,eAAe,EACfF,gBAAgB,EAChBD,gBAAgB,EAChB2D,WAAW,EACX3U,QAAQ,EACR;AAAE,MAAA,CAACW,KAAK,CAACzB,KAAK,CAACO,EAAE,GAAGkZ,YAAY,CAAC7X,IAAAA;KAAM,EACvChI,SAAS;KACV,CAAA;;AAED;AACA;AACA;AACA6d,IAAAA,oBAAoB,CACjBnT,MAAM,CAAEuT,EAAE,IAAKA,EAAE,CAACrd,GAAG,KAAKA,GAAG,CAAC,CAC9BiI,OAAO,CAAEoV,EAAE,IAAK;AACf,MAAA,IAAIiC,QAAQ,GAAGjC,EAAE,CAACrd,GAAG,CAAA;MACrB,IAAI2e,eAAe,GAAGxf,KAAK,CAACiX,QAAQ,CAACzF,GAAG,CAAC2O,QAAQ,CAAC,CAAA;AAClD,MAAA,IAAIhC,mBAAmB,GAAGC,iBAAiB,CACzCne,SAAS,EACTuf,eAAe,GAAGA,eAAe,CAACvX,IAAI,GAAGhI,SAC3C,CAAC,CAAA;MACDD,KAAK,CAACiX,QAAQ,CAACvH,GAAG,CAACyQ,QAAQ,EAAEhC,mBAAmB,CAAC,CAAA;AACjD,MAAA,IAAIpG,gBAAgB,CAACtI,GAAG,CAAC0Q,QAAQ,CAAC,EAAE;QAClC9B,YAAY,CAAC8B,QAAQ,CAAC,CAAA;AACxB,OAAA;MACA,IAAIjC,EAAE,CAAC1N,UAAU,EAAE;QACjBuH,gBAAgB,CAACrI,GAAG,CAACyQ,QAAQ,EAAEjC,EAAE,CAAC1N,UAAU,CAAC,CAAA;AAC/C,OAAA;AACF,KAAC,CAAC,CAAA;AAEJuI,IAAAA,WAAW,CAAC;AAAE9B,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAClX,KAAK,CAACiX,QAAQ,CAAA;AAAE,KAAC,CAAC,CAAA;AAElD,IAAA,IAAIqH,8BAA8B,GAAGA,MACnCR,oBAAoB,CAAChV,OAAO,CAAEoV,EAAE,IAAKG,YAAY,CAACH,EAAE,CAACrd,GAAG,CAAC,CAAC,CAAA;IAE5D8e,eAAe,CAAC/O,MAAM,CAAC7K,gBAAgB,CACrC,OAAO,EACPuY,8BACF,CAAC,CAAA;IAED,IAAI;MAAEE,OAAO;MAAEC,aAAa;AAAEC,MAAAA,cAAAA;AAAe,KAAC,GAC5C,MAAMC,8BAA8B,CAClC3e,KAAK,CAACwH,OAAO,EACbA,OAAO,EACPqW,aAAa,EACbC,oBAAoB,EACpBkC,mBACF,CAAC,CAAA;AAEH,IAAA,IAAIL,eAAe,CAAC/O,MAAM,CAACa,OAAO,EAAE;AAClC,MAAA,OAAA;AACF,KAAA;IAEAkO,eAAe,CAAC/O,MAAM,CAAC5K,mBAAmB,CACxC,OAAO,EACPsY,8BACF,CAAC,CAAA;AAEDpG,IAAAA,cAAc,CAACxG,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC1BkX,IAAAA,gBAAgB,CAACrG,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC5Bid,IAAAA,oBAAoB,CAAChV,OAAO,CAAEyH,CAAC,IAAKwH,gBAAgB,CAACrG,MAAM,CAACnB,CAAC,CAAC1P,GAAG,CAAC,CAAC,CAAA;AAEnE,IAAA,IAAIkS,QAAQ,GAAG6L,YAAY,CAACJ,OAAO,CAAC,CAAA;AACpC,IAAA,IAAIzL,QAAQ,EAAE;AACZ,MAAA,IAAIA,QAAQ,CAACnO,GAAG,IAAIiZ,aAAa,CAAC1d,MAAM,EAAE;AACxC;AACA;AACA;AACA,QAAA,IAAI0e,UAAU,GACZf,oBAAoB,CAAC/K,QAAQ,CAACnO,GAAG,GAAGiZ,aAAa,CAAC1d,MAAM,CAAC,CAACU,GAAG,CAAA;AAC/DsX,QAAAA,gBAAgB,CAAClH,GAAG,CAAC4N,UAAU,CAAC,CAAA;AAClC,OAAA;AACA,MAAA,OAAOtB,uBAAuB,CAACvd,KAAK,EAAE+S,QAAQ,CAACpJ,MAAM,CAAC,CAAA;AACxD,KAAA;;AAEA;IACA,IAAI;MAAE5B,UAAU;AAAEyO,MAAAA,MAAAA;KAAQ,GAAGsI,iBAAiB,CAC5C9e,KAAK,EACLA,KAAK,CAACwH,OAAO,EACbqW,aAAa,EACbY,aAAa,EACbxe,SAAS,EACT6d,oBAAoB,EACpBY,cAAc,EACdnG,eACF,CAAC,CAAA;;AAED;AACA;IACA,IAAIvY,KAAK,CAACiX,QAAQ,CAACxH,GAAG,CAAC5O,GAAG,CAAC,EAAE;AAC3B,MAAA,IAAIuf,WAAW,GAAGL,cAAc,CAACD,YAAY,CAAC7X,IAAI,CAAC,CAAA;MACnDjI,KAAK,CAACiX,QAAQ,CAACvH,GAAG,CAAC7O,GAAG,EAAEuf,WAAW,CAAC,CAAA;AACtC,KAAA;IAEAnB,oBAAoB,CAACgB,MAAM,CAAC,CAAA;;AAE5B;AACA;AACA;IACA,IACEjgB,KAAK,CAAC4W,UAAU,CAAC5W,KAAK,KAAK,SAAS,IACpCigB,MAAM,GAAGhI,uBAAuB,EAChC;AACAjU,MAAAA,SAAS,CAACoT,aAAa,EAAE,yBAAyB,CAAC,CAAA;AACnDG,MAAAA,2BAA2B,IAAIA,2BAA2B,CAACtF,KAAK,EAAE,CAAA;AAElEiI,MAAAA,kBAAkB,CAACla,KAAK,CAAC4W,UAAU,CAAC9V,QAAQ,EAAE;QAC5C0G,OAAO;QACPO,UAAU;QACVyO,MAAM;AACNS,QAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAClX,KAAK,CAACiX,QAAQ,CAAA;AAClC,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;AACL;AACA;AACA;AACA8B,MAAAA,WAAW,CAAC;QACVvC,MAAM;AACNzO,QAAAA,UAAU,EAAE2S,eAAe,CACzB1a,KAAK,CAAC+H,UAAU,EAChBA,UAAU,EACVP,OAAO,EACPgP,MACF,CAAC;AACDS,QAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAClX,KAAK,CAACiX,QAAQ,CAAA;AAClC,OAAC,CAAC,CAAA;AACFW,MAAAA,sBAAsB,GAAG,KAAK,CAAA;AAChC,KAAA;AACF,GAAA;;AAEA;AACA,EAAA,eAAe0H,mBAAmBA,CAChCze,GAAW,EACXuc,OAAe,EACfzb,IAAY,EACZmG,KAA6B,EAC7BN,OAAiC,EACjCyS,SAAkB,EAClBkB,UAAuB,EACvB;IACA,IAAIqE,eAAe,GAAGxf,KAAK,CAACiX,QAAQ,CAACzF,GAAG,CAAC3Q,GAAG,CAAC,CAAA;AAC7C4e,IAAAA,kBAAkB,CAChB5e,GAAG,EACHud,iBAAiB,CACfjD,UAAU,EACVqE,eAAe,GAAGA,eAAe,CAACvX,IAAI,GAAGhI,SAC3C,CAAC,EACD;AAAEga,MAAAA,SAAAA;AAAU,KACd,CAAC,CAAA;;AAED;AACA,IAAA,IAAI0F,eAAe,GAAG,IAAIlP,eAAe,EAAE,CAAA;AAC3C,IAAA,IAAImP,YAAY,GAAGxD,uBAAuB,CACxChN,IAAI,CAAC7N,OAAO,EACZI,IAAI,EACJge,eAAe,CAAC/O,MAClB,CAAC,CAAA;AACDmH,IAAAA,gBAAgB,CAACrI,GAAG,CAAC7O,GAAG,EAAE8e,eAAe,CAAC,CAAA;IAE1C,IAAIE,iBAAiB,GAAG7H,kBAAkB,CAAA;IAC1C,IAAIrO,MAAkB,GAAG,MAAM0T,kBAAkB,CAC/C,QAAQ,EACRuC,YAAY,EACZ9X,KAAK,EACLN,OAAO,EACPd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,EACRgO,MAAM,CAAChH,oBACT,CAAC,CAAA;;AAED;AACA;AACA;AACA;AACA,IAAA,IAAIuP,gBAAgB,CAAC/T,MAAM,CAAC,EAAE;AAC5BA,MAAAA,MAAM,GACJ,CAAC,MAAM0W,mBAAmB,CAAC1W,MAAM,EAAEiW,YAAY,CAAChP,MAAM,EAAE,IAAI,CAAC,KAC7DjH,MAAM,CAAA;AACV,KAAA;;AAEA;AACA;IACA,IAAIoO,gBAAgB,CAACvG,GAAG,CAAC3Q,GAAG,CAAC,KAAK8e,eAAe,EAAE;AACjD5H,MAAAA,gBAAgB,CAACrG,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC9B,KAAA;AAEA,IAAA,IAAI+e,YAAY,CAAChP,MAAM,CAACa,OAAO,EAAE;AAC/B,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA,IAAA,IAAI6G,eAAe,CAAC7I,GAAG,CAAC5O,GAAG,CAAC,EAAE;AAC5B4e,MAAAA,kBAAkB,CAAC5e,GAAG,EAAEkf,cAAc,CAAC9f,SAAS,CAAC,CAAC,CAAA;AAClD,MAAA,OAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAIqd,gBAAgB,CAAC3T,MAAM,CAAC,EAAE;MAC5B,IAAIsO,uBAAuB,GAAG4H,iBAAiB,EAAE;AAC/C;AACA;AACAJ,QAAAA,kBAAkB,CAAC5e,GAAG,EAAEkf,cAAc,CAAC9f,SAAS,CAAC,CAAC,CAAA;AAClD,QAAA,OAAA;AACF,OAAC,MAAM;AACLkY,QAAAA,gBAAgB,CAAClH,GAAG,CAACpQ,GAAG,CAAC,CAAA;AACzB,QAAA,MAAM0c,uBAAuB,CAACvd,KAAK,EAAE2J,MAAM,CAAC,CAAA;AAC5C,QAAA,OAAA;AACF,OAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAI6T,aAAa,CAAC7T,MAAM,CAAC,EAAE;MACzByV,eAAe,CAACve,GAAG,EAAEuc,OAAO,EAAEzT,MAAM,CAACjE,KAAK,CAAC,CAAA;AAC3C,MAAA,OAAA;AACF,KAAA;IAEA1B,SAAS,CAAC,CAAC0Z,gBAAgB,CAAC/T,MAAM,CAAC,EAAE,iCAAiC,CAAC,CAAA;;AAEvE;IACA8V,kBAAkB,CAAC5e,GAAG,EAAEkf,cAAc,CAACpW,MAAM,CAAC1B,IAAI,CAAC,CAAC,CAAA;AACtD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,eAAesV,uBAAuBA,CACpCvd,KAAkB,EAClB+S,QAAwB,EAAAuN,MAAA,EAUxB;IAAA,IATA;MACEnF,UAAU;MACV2B,iBAAiB;AACjB1a,MAAAA,OAAAA;AAKF,KAAC,GAAAke,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;IAEN,IAAIvN,QAAQ,CAAC0I,UAAU,EAAE;AACvB7D,MAAAA,sBAAsB,GAAG,IAAI,CAAA;AAC/B,KAAA;IAEA,IAAI2I,gBAAgB,GAAGxf,cAAc,CAACf,KAAK,CAACc,QAAQ,EAAEiS,QAAQ,CAACjS,QAAQ,EAAE;AACvE0Z,MAAAA,WAAW,EAAE,IAAA;AACf,KAAC,CAAC,CAAA;AACFxW,IAAAA,SAAS,CACPuc,gBAAgB,EAChB,gDACF,CAAC,CAAA;AAED,IAAA,IAAI1L,SAAS,EAAE;MACb,IAAI2L,gBAAgB,GAAG,KAAK,CAAA;MAE5B,IAAIzN,QAAQ,CAAC0N,cAAc,EAAE;AAC3B;AACAD,QAAAA,gBAAgB,GAAG,IAAI,CAAA;OACxB,MAAM,IAAIlM,kBAAkB,CAACxJ,IAAI,CAACiI,QAAQ,CAACjS,QAAQ,CAAC,EAAE;QACrD,MAAM6C,GAAG,GAAGyL,IAAI,CAAC7N,OAAO,CAACC,SAAS,CAACuR,QAAQ,CAACjS,QAAQ,CAAC,CAAA;QACrD0f,gBAAgB;AACd;AACA7c,QAAAA,GAAG,CAACmC,MAAM,KAAK8O,YAAY,CAAC9T,QAAQ,CAACgF,MAAM;AAC3C;QACAsB,aAAa,CAACzD,GAAG,CAAC3C,QAAQ,EAAEmG,QAAQ,CAAC,IAAI,IAAI,CAAA;AACjD,OAAA;AAEA,MAAA,IAAIqZ,gBAAgB,EAAE;AACpB,QAAA,IAAIpe,OAAO,EAAE;UACXwS,YAAY,CAAC9T,QAAQ,CAACsB,OAAO,CAAC2Q,QAAQ,CAACjS,QAAQ,CAAC,CAAA;AAClD,SAAC,MAAM;UACL8T,YAAY,CAAC9T,QAAQ,CAAC+E,MAAM,CAACkN,QAAQ,CAACjS,QAAQ,CAAC,CAAA;AACjD,SAAA;AACA,QAAA,OAAA;AACF,OAAA;AACF,KAAA;;AAEA;AACA;AACAyW,IAAAA,2BAA2B,GAAG,IAAI,CAAA;AAElC,IAAA,IAAImJ,qBAAqB,GACvBte,OAAO,KAAK,IAAI,GAAGiV,MAAa,CAAChV,OAAO,GAAGgV,MAAa,CAACrV,IAAI,CAAA;;AAE/D;AACA;IACA,IAAI;MAAE6R,UAAU;MAAEC,UAAU;AAAEC,MAAAA,WAAAA;KAAa,GAAG/T,KAAK,CAAC4W,UAAU,CAAA;IAC9D,IACE,CAACuE,UAAU,IACX,CAAC2B,iBAAiB,IAClBjJ,UAAU,IACVC,UAAU,IACVC,WAAW,EACX;AACAoH,MAAAA,UAAU,GAAGyC,2BAA2B,CAAC5d,KAAK,CAAC4W,UAAU,CAAC,CAAA;AAC5D,KAAA;;AAEA;AACA;AACA;AACA,IAAA,IAAI+G,gBAAgB,GAAGxC,UAAU,IAAI2B,iBAAiB,CAAA;AACtD,IAAA,IACEnJ,iCAAiC,CAAClE,GAAG,CAACsD,QAAQ,CAACzD,MAAM,CAAC,IACtDqO,gBAAgB,IAChBpD,gBAAgB,CAACoD,gBAAgB,CAAC9J,UAAU,CAAC,EAC7C;AACA,MAAA,MAAMmF,eAAe,CAAC0H,qBAAqB,EAAEH,gBAAgB,EAAE;QAC7DpF,UAAU,EAAArW,QAAA,CAAA,EAAA,EACL6Y,gBAAgB,EAAA;UACnB7J,UAAU,EAAEf,QAAQ,CAACjS,QAAAA;SACtB,CAAA;AACD;AACAgW,QAAAA,kBAAkB,EAAEQ,yBAAAA;AACtB,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;AACL;AACA;AACA,MAAA,IAAIsE,kBAAkB,GAAGe,oBAAoB,CAC3C4D,gBAAgB,EAChBpF,UACF,CAAC,CAAA;AACD,MAAA,MAAMnC,eAAe,CAAC0H,qBAAqB,EAAEH,gBAAgB,EAAE;QAC7D3E,kBAAkB;AAClB;QACAkB,iBAAiB;AACjB;AACAhG,QAAAA,kBAAkB,EAAEQ,yBAAAA;AACtB,OAAC,CAAC,CAAA;AACJ,KAAA;AACF,GAAA;EAEA,eAAeqH,8BAA8BA,CAC3CgC,cAAwC,EACxCnZ,OAAiC,EACjCqW,aAAuC,EACvC+C,cAAqC,EACrCzE,OAAgB,EAChB;AACA;AACA;AACA;AACA,IAAA,IAAIqC,OAAO,GAAG,MAAMlO,OAAO,CAACuQ,GAAG,CAAC,CAC9B,GAAGhD,aAAa,CAACje,GAAG,CAAEkI,KAAK,IACzBuV,kBAAkB,CAChB,QAAQ,EACRlB,OAAO,EACPrU,KAAK,EACLN,OAAO,EACPd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,EACRgO,MAAM,CAAChH,oBACT,CACF,CAAC,EACD,GAAGyS,cAAc,CAAChhB,GAAG,CAAE2e,CAAC,IAAK;MAC3B,IAAIA,CAAC,CAAC/W,OAAO,IAAI+W,CAAC,CAACzW,KAAK,IAAIyW,CAAC,CAAC/N,UAAU,EAAE;AACxC,QAAA,OAAO6M,kBAAkB,CACvB,QAAQ,EACRjB,uBAAuB,CAAChN,IAAI,CAAC7N,OAAO,EAAEgd,CAAC,CAAC5c,IAAI,EAAE4c,CAAC,CAAC/N,UAAU,CAACI,MAAM,CAAC,EAClE2N,CAAC,CAACzW,KAAK,EACPyW,CAAC,CAAC/W,OAAO,EACTd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,EACRgO,MAAM,CAAChH,oBACT,CAAC,CAAA;AACH,OAAC,MAAM;AACL,QAAA,IAAIzI,KAAkB,GAAG;UACvBwX,IAAI,EAAEjX,UAAU,CAACP,KAAK;AACtBA,UAAAA,KAAK,EAAEsQ,sBAAsB,CAAC,GAAG,EAAE;YAAEhV,QAAQ,EAAEud,CAAC,CAAC5c,IAAAA;WAAM,CAAA;SACxD,CAAA;AACD,QAAA,OAAO+D,KAAK,CAAA;AACd,OAAA;KACD,CAAC,CACH,CAAC,CAAA;IACF,IAAI+Y,aAAa,GAAGD,OAAO,CAAC1a,KAAK,CAAC,CAAC,EAAE+Z,aAAa,CAAC1d,MAAM,CAAC,CAAA;IAC1D,IAAIue,cAAc,GAAGF,OAAO,CAAC1a,KAAK,CAAC+Z,aAAa,CAAC1d,MAAM,CAAC,CAAA;AAExD,IAAA,MAAMmQ,OAAO,CAACuQ,GAAG,CAAC,CAChBC,sBAAsB,CACpBH,cAAc,EACd9C,aAAa,EACbY,aAAa,EACbA,aAAa,CAAC7e,GAAG,CAAC,MAAMuc,OAAO,CAACvL,MAAM,CAAC,EACvC,KAAK,EACL5Q,KAAK,CAAC+H,UACR,CAAC,EACD+Y,sBAAsB,CACpBH,cAAc,EACdC,cAAc,CAAChhB,GAAG,CAAE2e,CAAC,IAAKA,CAAC,CAACzW,KAAK,CAAC,EAClC4W,cAAc,EACdkC,cAAc,CAAChhB,GAAG,CAAE2e,CAAC,IAAMA,CAAC,CAAC/N,UAAU,GAAG+N,CAAC,CAAC/N,UAAU,CAACI,MAAM,GAAG,IAAK,CAAC,EACtE,IACF,CAAC,CACF,CAAC,CAAA;IAEF,OAAO;MAAE4N,OAAO;MAAEC,aAAa;AAAEC,MAAAA,cAAAA;KAAgB,CAAA;AACnD,GAAA;EAEA,SAAShD,oBAAoBA,GAAG;AAC9B;AACA9D,IAAAA,sBAAsB,GAAG,IAAI,CAAA;;AAE7B;AACA;AACAC,IAAAA,uBAAuB,CAAC9V,IAAI,CAAC,GAAGka,qBAAqB,EAAE,CAAC,CAAA;;AAExD;AACA7D,IAAAA,gBAAgB,CAACtP,OAAO,CAAC,CAACgE,CAAC,EAAEjM,GAAG,KAAK;AACnC,MAAA,IAAIkX,gBAAgB,CAACtI,GAAG,CAAC5O,GAAG,CAAC,EAAE;AAC7BiX,QAAAA,qBAAqB,CAAC/V,IAAI,CAAClB,GAAG,CAAC,CAAA;QAC/Bwd,YAAY,CAACxd,GAAG,CAAC,CAAA;AACnB,OAAA;AACF,KAAC,CAAC,CAAA;AACJ,GAAA;AAEA,EAAA,SAAS4e,kBAAkBA,CACzB5e,GAAW,EACXgZ,OAAgB,EAChBH,IAA6B,EAC7B;AAAA,IAAA,IADAA,IAA6B,KAAA,KAAA,CAAA,EAAA;MAA7BA,IAA6B,GAAG,EAAE,CAAA;AAAA,KAAA;IAElC1Z,KAAK,CAACiX,QAAQ,CAACvH,GAAG,CAAC7O,GAAG,EAAEgZ,OAAO,CAAC,CAAA;AAChCd,IAAAA,WAAW,CACT;AAAE9B,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAClX,KAAK,CAACiX,QAAQ,CAAA;AAAE,KAAC,EACrC;AAAEgD,MAAAA,SAAS,EAAE,CAACP,IAAI,IAAIA,IAAI,CAACO,SAAS,MAAM,IAAA;AAAK,KACjD,CAAC,CAAA;AACH,GAAA;EAEA,SAASmF,eAAeA,CACtBve,GAAW,EACXuc,OAAe,EACf1X,KAAU,EACVgU,IAA6B,EAC7B;AAAA,IAAA,IADAA,IAA6B,KAAA,KAAA,CAAA,EAAA;MAA7BA,IAA6B,GAAG,EAAE,CAAA;AAAA,KAAA;IAElC,IAAI+D,aAAa,GAAGnB,mBAAmB,CAACtc,KAAK,CAACwH,OAAO,EAAE4V,OAAO,CAAC,CAAA;IAC/D7D,aAAa,CAAC1Y,GAAG,CAAC,CAAA;AAClBkY,IAAAA,WAAW,CACT;AACEvC,MAAAA,MAAM,EAAE;AACN,QAAA,CAACiH,aAAa,CAACpX,KAAK,CAACO,EAAE,GAAGlB,KAAAA;OAC3B;AACDuR,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAClX,KAAK,CAACiX,QAAQ,CAAA;AAClC,KAAC,EACD;AAAEgD,MAAAA,SAAS,EAAE,CAACP,IAAI,IAAIA,IAAI,CAACO,SAAS,MAAM,IAAA;AAAK,KACjD,CAAC,CAAA;AACH,GAAA;EAEA,SAAS8G,UAAUA,CAAclgB,GAAW,EAAkB;IAC5D,IAAIsU,MAAM,CAACC,iBAAiB,EAAE;AAC5BiD,MAAAA,cAAc,CAAC3I,GAAG,CAAC7O,GAAG,EAAE,CAACwX,cAAc,CAAC7G,GAAG,CAAC3Q,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;AAC3D;AACA;AACA,MAAA,IAAIyX,eAAe,CAAC7I,GAAG,CAAC5O,GAAG,CAAC,EAAE;AAC5ByX,QAAAA,eAAe,CAAC5G,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC7B,OAAA;AACF,KAAA;IACA,OAAOb,KAAK,CAACiX,QAAQ,CAACzF,GAAG,CAAC3Q,GAAG,CAAC,IAAIqT,YAAY,CAAA;AAChD,GAAA;EAEA,SAASqF,aAAaA,CAAC1Y,GAAW,EAAQ;IACxC,IAAIgZ,OAAO,GAAG7Z,KAAK,CAACiX,QAAQ,CAACzF,GAAG,CAAC3Q,GAAG,CAAC,CAAA;AACrC;AACA;AACA;IACA,IACEkX,gBAAgB,CAACtI,GAAG,CAAC5O,GAAG,CAAC,IACzB,EAAEgZ,OAAO,IAAIA,OAAO,CAAC7Z,KAAK,KAAK,SAAS,IAAIkY,cAAc,CAACzI,GAAG,CAAC5O,GAAG,CAAC,CAAC,EACpE;MACAwd,YAAY,CAACxd,GAAG,CAAC,CAAA;AACnB,KAAA;AACAuX,IAAAA,gBAAgB,CAAC1G,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC5BqX,IAAAA,cAAc,CAACxG,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC1BsX,IAAAA,gBAAgB,CAACzG,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC5ByX,IAAAA,eAAe,CAAC5G,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC3Bb,IAAAA,KAAK,CAACiX,QAAQ,CAACvF,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC5B,GAAA;EAEA,SAASmgB,2BAA2BA,CAACngB,GAAW,EAAQ;IACtD,IAAIsU,MAAM,CAACC,iBAAiB,EAAE;AAC5B,MAAA,IAAI6L,KAAK,GAAG,CAAC5I,cAAc,CAAC7G,GAAG,CAAC3Q,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;MAC9C,IAAIogB,KAAK,IAAI,CAAC,EAAE;AACd5I,QAAAA,cAAc,CAAC3G,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC1ByX,QAAAA,eAAe,CAACrH,GAAG,CAACpQ,GAAG,CAAC,CAAA;AAC1B,OAAC,MAAM;AACLwX,QAAAA,cAAc,CAAC3I,GAAG,CAAC7O,GAAG,EAAEogB,KAAK,CAAC,CAAA;AAChC,OAAA;AACF,KAAC,MAAM;MACL1H,aAAa,CAAC1Y,GAAG,CAAC,CAAA;AACpB,KAAA;AACAkY,IAAAA,WAAW,CAAC;AAAE9B,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAClX,KAAK,CAACiX,QAAQ,CAAA;AAAE,KAAC,CAAC,CAAA;AACpD,GAAA;EAEA,SAASoH,YAAYA,CAACxd,GAAW,EAAE;AACjC,IAAA,IAAI2P,UAAU,GAAGuH,gBAAgB,CAACvG,GAAG,CAAC3Q,GAAG,CAAC,CAAA;AAC1CmD,IAAAA,SAAS,CAACwM,UAAU,EAAgC3P,6BAAAA,GAAAA,GAAK,CAAC,CAAA;IAC1D2P,UAAU,CAACyB,KAAK,EAAE,CAAA;AAClB8F,IAAAA,gBAAgB,CAACrG,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC9B,GAAA;EAEA,SAASqgB,gBAAgBA,CAACzG,IAAc,EAAE;AACxC,IAAA,KAAK,IAAI5Z,GAAG,IAAI4Z,IAAI,EAAE;AACpB,MAAA,IAAIZ,OAAO,GAAGkH,UAAU,CAAClgB,GAAG,CAAC,CAAA;AAC7B,MAAA,IAAIuf,WAAW,GAAGL,cAAc,CAAClG,OAAO,CAAC5R,IAAI,CAAC,CAAA;MAC9CjI,KAAK,CAACiX,QAAQ,CAACvH,GAAG,CAAC7O,GAAG,EAAEuf,WAAW,CAAC,CAAA;AACtC,KAAA;AACF,GAAA;EAEA,SAASnC,sBAAsBA,GAAY;IACzC,IAAIkD,QAAQ,GAAG,EAAE,CAAA;IACjB,IAAInD,eAAe,GAAG,KAAK,CAAA;AAC3B,IAAA,KAAK,IAAInd,GAAG,IAAIsX,gBAAgB,EAAE;MAChC,IAAI0B,OAAO,GAAG7Z,KAAK,CAACiX,QAAQ,CAACzF,GAAG,CAAC3Q,GAAG,CAAC,CAAA;AACrCmD,MAAAA,SAAS,CAAC6V,OAAO,EAAuBhZ,oBAAAA,GAAAA,GAAK,CAAC,CAAA;AAC9C,MAAA,IAAIgZ,OAAO,CAAC7Z,KAAK,KAAK,SAAS,EAAE;AAC/BmY,QAAAA,gBAAgB,CAACzG,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC5BsgB,QAAAA,QAAQ,CAACpf,IAAI,CAAClB,GAAG,CAAC,CAAA;AAClBmd,QAAAA,eAAe,GAAG,IAAI,CAAA;AACxB,OAAA;AACF,KAAA;IACAkD,gBAAgB,CAACC,QAAQ,CAAC,CAAA;AAC1B,IAAA,OAAOnD,eAAe,CAAA;AACxB,GAAA;EAEA,SAASiB,oBAAoBA,CAACmC,QAAgB,EAAW;IACvD,IAAIC,UAAU,GAAG,EAAE,CAAA;IACnB,KAAK,IAAI,CAACxgB,GAAG,EAAE+F,EAAE,CAAC,IAAIsR,cAAc,EAAE;MACpC,IAAItR,EAAE,GAAGwa,QAAQ,EAAE;QACjB,IAAIvH,OAAO,GAAG7Z,KAAK,CAACiX,QAAQ,CAACzF,GAAG,CAAC3Q,GAAG,CAAC,CAAA;AACrCmD,QAAAA,SAAS,CAAC6V,OAAO,EAAuBhZ,oBAAAA,GAAAA,GAAK,CAAC,CAAA;AAC9C,QAAA,IAAIgZ,OAAO,CAAC7Z,KAAK,KAAK,SAAS,EAAE;UAC/Bqe,YAAY,CAACxd,GAAG,CAAC,CAAA;AACjBqX,UAAAA,cAAc,CAACxG,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC1BwgB,UAAAA,UAAU,CAACtf,IAAI,CAAClB,GAAG,CAAC,CAAA;AACtB,SAAA;AACF,OAAA;AACF,KAAA;IACAqgB,gBAAgB,CAACG,UAAU,CAAC,CAAA;AAC5B,IAAA,OAAOA,UAAU,CAAClhB,MAAM,GAAG,CAAC,CAAA;AAC9B,GAAA;AAEA,EAAA,SAASmhB,UAAUA,CAACzgB,GAAW,EAAE4B,EAAmB,EAAE;IACpD,IAAI8e,OAAgB,GAAGvhB,KAAK,CAACmX,QAAQ,CAAC3F,GAAG,CAAC3Q,GAAG,CAAC,IAAIsT,YAAY,CAAA;IAE9D,IAAIqE,gBAAgB,CAAChH,GAAG,CAAC3Q,GAAG,CAAC,KAAK4B,EAAE,EAAE;AACpC+V,MAAAA,gBAAgB,CAAC9I,GAAG,CAAC7O,GAAG,EAAE4B,EAAE,CAAC,CAAA;AAC/B,KAAA;AAEA,IAAA,OAAO8e,OAAO,CAAA;AAChB,GAAA;EAEA,SAAS/H,aAAaA,CAAC3Y,GAAW,EAAE;AAClCb,IAAAA,KAAK,CAACmX,QAAQ,CAACzF,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC1B2X,IAAAA,gBAAgB,CAAC9G,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC9B,GAAA;;AAEA;AACA,EAAA,SAASiY,aAAaA,CAACjY,GAAW,EAAE2gB,UAAmB,EAAE;IACvD,IAAID,OAAO,GAAGvhB,KAAK,CAACmX,QAAQ,CAAC3F,GAAG,CAAC3Q,GAAG,CAAC,IAAIsT,YAAY,CAAA;;AAErD;AACA;AACAnQ,IAAAA,SAAS,CACNud,OAAO,CAACvhB,KAAK,KAAK,WAAW,IAAIwhB,UAAU,CAACxhB,KAAK,KAAK,SAAS,IAC7DuhB,OAAO,CAACvhB,KAAK,KAAK,SAAS,IAAIwhB,UAAU,CAACxhB,KAAK,KAAK,SAAU,IAC9DuhB,OAAO,CAACvhB,KAAK,KAAK,SAAS,IAAIwhB,UAAU,CAACxhB,KAAK,KAAK,YAAa,IACjEuhB,OAAO,CAACvhB,KAAK,KAAK,SAAS,IAAIwhB,UAAU,CAACxhB,KAAK,KAAK,WAAY,IAChEuhB,OAAO,CAACvhB,KAAK,KAAK,YAAY,IAAIwhB,UAAU,CAACxhB,KAAK,KAAK,WAAY,EAAA,oCAAA,GACjCuhB,OAAO,CAACvhB,KAAK,GAAA,MAAA,GAAOwhB,UAAU,CAACxhB,KACtE,CAAC,CAAA;IAED,IAAImX,QAAQ,GAAG,IAAID,GAAG,CAAClX,KAAK,CAACmX,QAAQ,CAAC,CAAA;AACtCA,IAAAA,QAAQ,CAACzH,GAAG,CAAC7O,GAAG,EAAE2gB,UAAU,CAAC,CAAA;AAC7BzI,IAAAA,WAAW,CAAC;AAAE5B,MAAAA,QAAAA;AAAS,KAAC,CAAC,CAAA;AAC3B,GAAA;EAEA,SAASyB,qBAAqBA,CAAA9H,KAAA,EAQP;IAAA,IARQ;MAC7B+H,eAAe;MACf5W,YAAY;AACZ0U,MAAAA,aAAAA;AAKF,KAAC,GAAA7F,KAAA,CAAA;AACC,IAAA,IAAI0H,gBAAgB,CAACnG,IAAI,KAAK,CAAC,EAAE;AAC/B,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA,IAAA,IAAImG,gBAAgB,CAACnG,IAAI,GAAG,CAAC,EAAE;AAC7BpR,MAAAA,OAAO,CAAC,KAAK,EAAE,8CAA8C,CAAC,CAAA;AAChE,KAAA;IAEA,IAAItB,OAAO,GAAGuQ,KAAK,CAACvB,IAAI,CAAC6J,gBAAgB,CAAC7Y,OAAO,EAAE,CAAC,CAAA;AACpD,IAAA,IAAI,CAACgZ,UAAU,EAAE8I,eAAe,CAAC,GAAG9hB,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,CAAA;IAC/D,IAAIohB,OAAO,GAAGvhB,KAAK,CAACmX,QAAQ,CAAC3F,GAAG,CAACmH,UAAU,CAAC,CAAA;AAE5C,IAAA,IAAI4I,OAAO,IAAIA,OAAO,CAACvhB,KAAK,KAAK,YAAY,EAAE;AAC7C;AACA;AACA,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA,IAAA,IAAIyhB,eAAe,CAAC;MAAE5I,eAAe;MAAE5W,YAAY;AAAE0U,MAAAA,aAAAA;AAAc,KAAC,CAAC,EAAE;AACrE,MAAA,OAAOgC,UAAU,CAAA;AACnB,KAAA;AACF,GAAA;EAEA,SAASsD,qBAAqBA,CAC5ByF,SAAwC,EAC9B;IACV,IAAIC,iBAA2B,GAAG,EAAE,CAAA;AACpCpJ,IAAAA,eAAe,CAACzP,OAAO,CAAC,CAAC8Y,GAAG,EAAExE,OAAO,KAAK;AACxC,MAAA,IAAI,CAACsE,SAAS,IAAIA,SAAS,CAACtE,OAAO,CAAC,EAAE;AACpC;AACA;AACA;QACAwE,GAAG,CAAC5P,MAAM,EAAE,CAAA;AACZ2P,QAAAA,iBAAiB,CAAC5f,IAAI,CAACqb,OAAO,CAAC,CAAA;AAC/B7E,QAAAA,eAAe,CAAC7G,MAAM,CAAC0L,OAAO,CAAC,CAAA;AACjC,OAAA;AACF,KAAC,CAAC,CAAA;AACF,IAAA,OAAOuE,iBAAiB,CAAA;AAC1B,GAAA;;AAEA;AACA;AACA,EAAA,SAASE,uBAAuBA,CAC9BC,SAAiC,EACjCC,WAAsC,EACtCC,MAAwC,EACxC;AACAvM,IAAAA,oBAAoB,GAAGqM,SAAS,CAAA;AAChCnM,IAAAA,iBAAiB,GAAGoM,WAAW,CAAA;IAC/BrM,uBAAuB,GAAGsM,MAAM,IAAI,IAAI,CAAA;;AAExC;AACA;AACA;IACA,IAAI,CAACpM,qBAAqB,IAAI5V,KAAK,CAAC4W,UAAU,KAAKhD,eAAe,EAAE;AAClEgC,MAAAA,qBAAqB,GAAG,IAAI,CAAA;MAC5B,IAAIqM,CAAC,GAAGpH,sBAAsB,CAAC7a,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAACwH,OAAO,CAAC,CAAA;MAC7D,IAAIya,CAAC,IAAI,IAAI,EAAE;AACblJ,QAAAA,WAAW,CAAC;AAAElC,UAAAA,qBAAqB,EAAEoL,CAAAA;AAAE,SAAC,CAAC,CAAA;AAC3C,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,MAAM;AACXxM,MAAAA,oBAAoB,GAAG,IAAI,CAAA;AAC3BE,MAAAA,iBAAiB,GAAG,IAAI,CAAA;AACxBD,MAAAA,uBAAuB,GAAG,IAAI,CAAA;KAC/B,CAAA;AACH,GAAA;AAEA,EAAA,SAASwM,YAAYA,CAACphB,QAAkB,EAAE0G,OAAiC,EAAE;AAC3E,IAAA,IAAIkO,uBAAuB,EAAE;MAC3B,IAAI7U,GAAG,GAAG6U,uBAAuB,CAC/B5U,QAAQ,EACR0G,OAAO,CAAC5H,GAAG,CAAEwW,CAAC,IAAKvO,0BAA0B,CAACuO,CAAC,EAAEpW,KAAK,CAAC+H,UAAU,CAAC,CACpE,CAAC,CAAA;AACD,MAAA,OAAOlH,GAAG,IAAIC,QAAQ,CAACD,GAAG,CAAA;AAC5B,KAAA;IACA,OAAOC,QAAQ,CAACD,GAAG,CAAA;AACrB,GAAA;AAEA,EAAA,SAASgb,kBAAkBA,CACzB/a,QAAkB,EAClB0G,OAAiC,EAC3B;IACN,IAAIiO,oBAAoB,IAAIE,iBAAiB,EAAE;AAC7C,MAAA,IAAI9U,GAAG,GAAGqhB,YAAY,CAACphB,QAAQ,EAAE0G,OAAO,CAAC,CAAA;AACzCiO,MAAAA,oBAAoB,CAAC5U,GAAG,CAAC,GAAG8U,iBAAiB,EAAE,CAAA;AACjD,KAAA;AACF,GAAA;AAEA,EAAA,SAASkF,sBAAsBA,CAC7B/Z,QAAkB,EAClB0G,OAAiC,EAClB;AACf,IAAA,IAAIiO,oBAAoB,EAAE;AACxB,MAAA,IAAI5U,GAAG,GAAGqhB,YAAY,CAACphB,QAAQ,EAAE0G,OAAO,CAAC,CAAA;AACzC,MAAA,IAAIya,CAAC,GAAGxM,oBAAoB,CAAC5U,GAAG,CAAC,CAAA;AACjC,MAAA,IAAI,OAAOohB,CAAC,KAAK,QAAQ,EAAE;AACzB,QAAA,OAAOA,CAAC,CAAA;AACV,OAAA;AACF,KAAA;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA,SAASE,kBAAkBA,CAACC,SAAoC,EAAE;IAChE1b,QAAQ,GAAG,EAAE,CAAA;IACbwO,kBAAkB,GAAG5O,yBAAyB,CAC5C8b,SAAS,EACT5b,kBAAkB,EAClBvG,SAAS,EACTyG,QACF,CAAC,CAAA;AACH,GAAA;AAEAgQ,EAAAA,MAAM,GAAG;IACP,IAAIvP,QAAQA,GAAG;AACb,MAAA,OAAOA,QAAQ,CAAA;KAChB;IACD,IAAIgO,MAAMA,GAAG;AACX,MAAA,OAAOA,MAAM,CAAA;KACd;IACD,IAAInV,KAAKA,GAAG;AACV,MAAA,OAAOA,KAAK,CAAA;KACb;IACD,IAAIuG,MAAMA,GAAG;AACX,MAAA,OAAO0O,UAAU,CAAA;KAClB;IACD,IAAIrS,MAAMA,GAAG;AACX,MAAA,OAAOgS,YAAY,CAAA;KACpB;IACD8D,UAAU;IACV3G,SAAS;IACT8P,uBAAuB;IACvB/G,QAAQ;IACRqE,KAAK;IACL1D,UAAU;AACV;AACA;IACApa,UAAU,EAAGT,EAAM,IAAKwO,IAAI,CAAC7N,OAAO,CAACF,UAAU,CAACT,EAAE,CAAC;IACnDc,cAAc,EAAGd,EAAM,IAAKwO,IAAI,CAAC7N,OAAO,CAACG,cAAc,CAACd,EAAE,CAAC;IAC3DmgB,UAAU;AACVxH,IAAAA,aAAa,EAAEyH,2BAA2B;IAC1C3H,OAAO;IACPiI,UAAU;IACV9H,aAAa;AACb6I,IAAAA,yBAAyB,EAAEtK,gBAAgB;AAC3CuK,IAAAA,wBAAwB,EAAE/J,eAAe;AACzC;AACA;AACA4J,IAAAA,kBAAAA;GACD,CAAA;AAED,EAAA,OAAOzL,MAAM,CAAA;AACf,CAAA;AACA;;AAEA;AACA;AACA;;MAEa6L,sBAAsB,GAAGC,MAAM,CAAC,UAAU,EAAC;;AAExD;AACA;AACA;;AAgBO,SAASC,mBAAmBA,CACjClc,MAA6B,EAC7BmT,IAAiC,EAClB;EACf1V,SAAS,CACPuC,MAAM,CAACpG,MAAM,GAAG,CAAC,EACjB,kEACF,CAAC,CAAA;EAED,IAAIuG,QAAuB,GAAG,EAAE,CAAA;EAChC,IAAIS,QAAQ,GAAG,CAACuS,IAAI,GAAGA,IAAI,CAACvS,QAAQ,GAAG,IAAI,KAAK,GAAG,CAAA;AACnD,EAAA,IAAIX,kBAA8C,CAAA;AAClD,EAAA,IAAIkT,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAElT,kBAAkB,EAAE;IAC5BA,kBAAkB,GAAGkT,IAAI,CAAClT,kBAAkB,CAAA;AAC9C,GAAC,MAAM,IAAIkT,IAAI,YAAJA,IAAI,CAAE1E,mBAAmB,EAAE;AACpC;AACA,IAAA,IAAIA,mBAAmB,GAAG0E,IAAI,CAAC1E,mBAAmB,CAAA;IAClDxO,kBAAkB,GAAIH,KAAK,KAAM;MAC/BmO,gBAAgB,EAAEQ,mBAAmB,CAAC3O,KAAK,CAAA;AAC7C,KAAC,CAAC,CAAA;AACJ,GAAC,MAAM;AACLG,IAAAA,kBAAkB,GAAG+N,yBAAyB,CAAA;AAChD,GAAA;AACA;EACA,IAAIY,MAAiC,GAAArQ,QAAA,CAAA;AACnCqJ,IAAAA,oBAAoB,EAAE,KAAK;AAC3BuU,IAAAA,mBAAmB,EAAE,KAAA;AAAK,GAAA,EACtBhJ,IAAI,GAAGA,IAAI,CAACvE,MAAM,GAAG,IAAI,CAC9B,CAAA;EAED,IAAIF,UAAU,GAAG3O,yBAAyB,CACxCC,MAAM,EACNC,kBAAkB,EAClBvG,SAAS,EACTyG,QACF,CAAC,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,eAAeic,KAAKA,CAClBxG,OAAgB,EAAAyG,MAAA,EAE0B;IAAA,IAD1C;AAAEC,MAAAA,cAAAA;AAA6C,KAAC,GAAAD,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;IAErD,IAAIjf,GAAG,GAAG,IAAIlC,GAAG,CAAC0a,OAAO,CAACxY,GAAG,CAAC,CAAA;AAC9B,IAAA,IAAIwZ,MAAM,GAAGhB,OAAO,CAACgB,MAAM,CAAA;AAC3B,IAAA,IAAIrc,QAAQ,GAAGC,cAAc,CAAC,EAAE,EAAEO,UAAU,CAACqC,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IACnE,IAAI6D,OAAO,GAAGP,WAAW,CAACgO,UAAU,EAAEnU,QAAQ,EAAEqG,QAAQ,CAAC,CAAA;;AAEzD;IACA,IAAI,CAAC2b,aAAa,CAAC3F,MAAM,CAAC,IAAIA,MAAM,KAAK,MAAM,EAAE;AAC/C,MAAA,IAAIzX,KAAK,GAAGsQ,sBAAsB,CAAC,GAAG,EAAE;AAAEmH,QAAAA,MAAAA;AAAO,OAAC,CAAC,CAAA;MACnD,IAAI;AAAE3V,QAAAA,OAAO,EAAEub,uBAAuB;AAAE1c,QAAAA,KAAAA;AAAM,OAAC,GAC7C4P,sBAAsB,CAAChB,UAAU,CAAC,CAAA;MACpC,OAAO;QACL9N,QAAQ;QACRrG,QAAQ;AACR0G,QAAAA,OAAO,EAAEub,uBAAuB;QAChChb,UAAU,EAAE,EAAE;AACdiP,QAAAA,UAAU,EAAE,IAAI;AAChBR,QAAAA,MAAM,EAAE;UACN,CAACnQ,KAAK,CAACO,EAAE,GAAGlB,KAAAA;SACb;QACDsd,UAAU,EAAEtd,KAAK,CAAC4J,MAAM;QACxB2T,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;AACjB3K,QAAAA,eAAe,EAAE,IAAA;OAClB,CAAA;AACH,KAAC,MAAM,IAAI,CAAC/Q,OAAO,EAAE;AACnB,MAAA,IAAI9B,KAAK,GAAGsQ,sBAAsB,CAAC,GAAG,EAAE;QAAEhV,QAAQ,EAAEF,QAAQ,CAACE,QAAAA;AAAS,OAAC,CAAC,CAAA;MACxE,IAAI;AAAEwG,QAAAA,OAAO,EAAEwU,eAAe;AAAE3V,QAAAA,KAAAA;AAAM,OAAC,GACrC4P,sBAAsB,CAAChB,UAAU,CAAC,CAAA;MACpC,OAAO;QACL9N,QAAQ;QACRrG,QAAQ;AACR0G,QAAAA,OAAO,EAAEwU,eAAe;QACxBjU,UAAU,EAAE,EAAE;AACdiP,QAAAA,UAAU,EAAE,IAAI;AAChBR,QAAAA,MAAM,EAAE;UACN,CAACnQ,KAAK,CAACO,EAAE,GAAGlB,KAAAA;SACb;QACDsd,UAAU,EAAEtd,KAAK,CAAC4J,MAAM;QACxB2T,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;AACjB3K,QAAAA,eAAe,EAAE,IAAA;OAClB,CAAA;AACH,KAAA;AAEA,IAAA,IAAI5O,MAAM,GAAG,MAAMwZ,SAAS,CAAChH,OAAO,EAAErb,QAAQ,EAAE0G,OAAO,EAAEqb,cAAc,CAAC,CAAA;AACxE,IAAA,IAAIO,UAAU,CAACzZ,MAAM,CAAC,EAAE;AACtB,MAAA,OAAOA,MAAM,CAAA;AACf,KAAA;;AAEA;AACA;AACA;AACA,IAAA,OAAA7E,QAAA,CAAA;MAAShE,QAAQ;AAAEqG,MAAAA,QAAAA;AAAQ,KAAA,EAAKwC,MAAM,CAAA,CAAA;AACxC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,eAAe0Z,UAAUA,CACvBlH,OAAgB,EAAAmH,MAAA,EAKF;IAAA,IAJd;MACElG,OAAO;AACPyF,MAAAA,cAAAA;AAC8C,KAAC,GAAAS,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;IAEtD,IAAI3f,GAAG,GAAG,IAAIlC,GAAG,CAAC0a,OAAO,CAACxY,GAAG,CAAC,CAAA;AAC9B,IAAA,IAAIwZ,MAAM,GAAGhB,OAAO,CAACgB,MAAM,CAAA;AAC3B,IAAA,IAAIrc,QAAQ,GAAGC,cAAc,CAAC,EAAE,EAAEO,UAAU,CAACqC,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IACnE,IAAI6D,OAAO,GAAGP,WAAW,CAACgO,UAAU,EAAEnU,QAAQ,EAAEqG,QAAQ,CAAC,CAAA;;AAEzD;AACA,IAAA,IAAI,CAAC2b,aAAa,CAAC3F,MAAM,CAAC,IAAIA,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,SAAS,EAAE;MACvE,MAAMnH,sBAAsB,CAAC,GAAG,EAAE;AAAEmH,QAAAA,MAAAA;AAAO,OAAC,CAAC,CAAA;AAC/C,KAAC,MAAM,IAAI,CAAC3V,OAAO,EAAE;MACnB,MAAMwO,sBAAsB,CAAC,GAAG,EAAE;QAAEhV,QAAQ,EAAEF,QAAQ,CAACE,QAAAA;AAAS,OAAC,CAAC,CAAA;AACpE,KAAA;IAEA,IAAI8G,KAAK,GAAGsV,OAAO,GACf5V,OAAO,CAAC+b,IAAI,CAAEnN,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACO,EAAE,KAAKwW,OAAO,CAAC,GAC3CH,cAAc,CAACzV,OAAO,EAAE1G,QAAQ,CAAC,CAAA;AAErC,IAAA,IAAIsc,OAAO,IAAI,CAACtV,KAAK,EAAE;MACrB,MAAMkO,sBAAsB,CAAC,GAAG,EAAE;QAChChV,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;AAC3Boc,QAAAA,OAAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM,IAAI,CAACtV,KAAK,EAAE;AACjB;MACA,MAAMkO,sBAAsB,CAAC,GAAG,EAAE;QAAEhV,QAAQ,EAAEF,QAAQ,CAACE,QAAAA;AAAS,OAAC,CAAC,CAAA;AACpE,KAAA;AAEA,IAAA,IAAI2I,MAAM,GAAG,MAAMwZ,SAAS,CAC1BhH,OAAO,EACPrb,QAAQ,EACR0G,OAAO,EACPqb,cAAc,EACd/a,KACF,CAAC,CAAA;AACD,IAAA,IAAIsb,UAAU,CAACzZ,MAAM,CAAC,EAAE;AACtB,MAAA,OAAOA,MAAM,CAAA;AACf,KAAA;AAEA,IAAA,IAAIjE,KAAK,GAAGiE,MAAM,CAAC6M,MAAM,GAAGjL,MAAM,CAACiY,MAAM,CAAC7Z,MAAM,CAAC6M,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGvW,SAAS,CAAA;IACvE,IAAIyF,KAAK,KAAKzF,SAAS,EAAE;AACvB;AACA;AACA;AACA;AACA,MAAA,MAAMyF,KAAK,CAAA;AACb,KAAA;;AAEA;IACA,IAAIiE,MAAM,CAACqN,UAAU,EAAE;MACrB,OAAOzL,MAAM,CAACiY,MAAM,CAAC7Z,MAAM,CAACqN,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;AAC5C,KAAA;IAEA,IAAIrN,MAAM,CAAC5B,UAAU,EAAE;AAAA,MAAA,IAAA0b,qBAAA,CAAA;AACrB,MAAA,IAAIxb,IAAI,GAAGsD,MAAM,CAACiY,MAAM,CAAC7Z,MAAM,CAAC5B,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9C,MAAA,IAAA,CAAA0b,qBAAA,GAAI9Z,MAAM,CAAC4O,eAAe,KAAtBkL,IAAAA,IAAAA,qBAAA,CAAyB3b,KAAK,CAACzB,KAAK,CAACO,EAAE,CAAC,EAAE;AAC5CqB,QAAAA,IAAI,CAACsa,sBAAsB,CAAC,GAAG5Y,MAAM,CAAC4O,eAAe,CAACzQ,KAAK,CAACzB,KAAK,CAACO,EAAE,CAAC,CAAA;AACvE,OAAA;AACA,MAAA,OAAOqB,IAAI,CAAA;AACb,KAAA;AAEA,IAAA,OAAOhI,SAAS,CAAA;AAClB,GAAA;EAEA,eAAekjB,SAASA,CACtBhH,OAAgB,EAChBrb,QAAkB,EAClB0G,OAAiC,EACjCqb,cAAuB,EACvBa,UAAmC,EACsC;AACzE1f,IAAAA,SAAS,CACPmY,OAAO,CAACvL,MAAM,EACd,sEACF,CAAC,CAAA;IAED,IAAI;MACF,IAAI2J,gBAAgB,CAAC4B,OAAO,CAACgB,MAAM,CAACjQ,WAAW,EAAE,CAAC,EAAE;QAClD,IAAIvD,MAAM,GAAG,MAAMga,MAAM,CACvBxH,OAAO,EACP3U,OAAO,EACPkc,UAAU,IAAIzG,cAAc,CAACzV,OAAO,EAAE1G,QAAQ,CAAC,EAC/C+hB,cAAc,EACda,UAAU,IAAI,IAChB,CAAC,CAAA;AACD,QAAA,OAAO/Z,MAAM,CAAA;AACf,OAAA;AAEA,MAAA,IAAIA,MAAM,GAAG,MAAMia,aAAa,CAC9BzH,OAAO,EACP3U,OAAO,EACPqb,cAAc,EACda,UACF,CAAC,CAAA;MACD,OAAON,UAAU,CAACzZ,MAAM,CAAC,GACrBA,MAAM,GAAA7E,QAAA,CAAA,EAAA,EAED6E,MAAM,EAAA;AACTqN,QAAAA,UAAU,EAAE,IAAI;AAChBkM,QAAAA,aAAa,EAAE,EAAC;OACjB,CAAA,CAAA;KACN,CAAC,OAAO3e,CAAC,EAAE;AACV;AACA;AACA;AACA,MAAA,IAAIsf,oBAAoB,CAACtf,CAAC,CAAC,EAAE;AAC3B,QAAA,IAAIA,CAAC,CAAC2Y,IAAI,KAAKjX,UAAU,CAACP,KAAK,EAAE;UAC/B,MAAMnB,CAAC,CAAC0O,QAAQ,CAAA;AAClB,SAAA;QACA,OAAO1O,CAAC,CAAC0O,QAAQ,CAAA;AACnB,OAAA;AACA;AACA;AACA,MAAA,IAAI6Q,kBAAkB,CAACvf,CAAC,CAAC,EAAE;AACzB,QAAA,OAAOA,CAAC,CAAA;AACV,OAAA;AACA,MAAA,MAAMA,CAAC,CAAA;AACT,KAAA;AACF,GAAA;EAEA,eAAeof,MAAMA,CACnBxH,OAAgB,EAChB3U,OAAiC,EACjCwV,WAAmC,EACnC6F,cAAuB,EACvBkB,cAAuB,EACkD;AACzE,IAAA,IAAIpa,MAAkB,CAAA;AAEtB,IAAA,IAAI,CAACqT,WAAW,CAAC3W,KAAK,CAACjG,MAAM,IAAI,CAAC4c,WAAW,CAAC3W,KAAK,CAACgQ,IAAI,EAAE;AACxD,MAAA,IAAI3Q,KAAK,GAAGsQ,sBAAsB,CAAC,GAAG,EAAE;QACtCmH,MAAM,EAAEhB,OAAO,CAACgB,MAAM;QACtBnc,QAAQ,EAAE,IAAIS,GAAG,CAAC0a,OAAO,CAACxY,GAAG,CAAC,CAAC3C,QAAQ;AACvCoc,QAAAA,OAAO,EAAEJ,WAAW,CAAC3W,KAAK,CAACO,EAAAA;AAC7B,OAAC,CAAC,CAAA;AACF,MAAA,IAAImd,cAAc,EAAE;AAClB,QAAA,MAAMre,KAAK,CAAA;AACb,OAAA;AACAiE,MAAAA,MAAM,GAAG;QACPuT,IAAI,EAAEjX,UAAU,CAACP,KAAK;AACtBA,QAAAA,KAAAA;OACD,CAAA;AACH,KAAC,MAAM;MACLiE,MAAM,GAAG,MAAM0T,kBAAkB,CAC/B,QAAQ,EACRlB,OAAO,EACPa,WAAW,EACXxV,OAAO,EACPd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,EACRgO,MAAM,CAAChH,oBAAoB,EAC3B;AAAE6V,QAAAA,eAAe,EAAE,IAAI;QAAED,cAAc;AAAElB,QAAAA,cAAAA;AAAe,OAC1D,CAAC,CAAA;AAED,MAAA,IAAI1G,OAAO,CAACvL,MAAM,CAACa,OAAO,EAAE;AAC1BwS,QAAAA,8BAA8B,CAAC9H,OAAO,EAAE4H,cAAc,EAAE5O,MAAM,CAAC,CAAA;AACjE,OAAA;AACF,KAAA;AAEA,IAAA,IAAImI,gBAAgB,CAAC3T,MAAM,CAAC,EAAE;AAC5B;AACA;AACA;AACA;AACA,MAAA,MAAM,IAAIgG,QAAQ,CAAC,IAAI,EAAE;QACvBL,MAAM,EAAE3F,MAAM,CAAC2F,MAAM;AACrBC,QAAAA,OAAO,EAAE;UACP2U,QAAQ,EAAEva,MAAM,CAAC7I,QAAAA;AACnB,SAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAA;AAEA,IAAA,IAAI4c,gBAAgB,CAAC/T,MAAM,CAAC,EAAE;AAC5B,MAAA,IAAIjE,KAAK,GAAGsQ,sBAAsB,CAAC,GAAG,EAAE;AAAEkH,QAAAA,IAAI,EAAE,cAAA;AAAe,OAAC,CAAC,CAAA;AACjE,MAAA,IAAI6G,cAAc,EAAE;AAClB,QAAA,MAAMre,KAAK,CAAA;AACb,OAAA;AACAiE,MAAAA,MAAM,GAAG;QACPuT,IAAI,EAAEjX,UAAU,CAACP,KAAK;AACtBA,QAAAA,KAAAA;OACD,CAAA;AACH,KAAA;AAEA,IAAA,IAAIqe,cAAc,EAAE;AAClB;AACA;AACA,MAAA,IAAIvG,aAAa,CAAC7T,MAAM,CAAC,EAAE;QACzB,MAAMA,MAAM,CAACjE,KAAK,CAAA;AACpB,OAAA;MAEA,OAAO;QACL8B,OAAO,EAAE,CAACwV,WAAW,CAAC;QACtBjV,UAAU,EAAE,EAAE;AACdiP,QAAAA,UAAU,EAAE;AAAE,UAAA,CAACgG,WAAW,CAAC3W,KAAK,CAACO,EAAE,GAAG+C,MAAM,CAAC1B,IAAAA;SAAM;AACnDuO,QAAAA,MAAM,EAAE,IAAI;AACZ;AACA;AACAwM,QAAAA,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;AACjB3K,QAAAA,eAAe,EAAE,IAAA;OAClB,CAAA;AACH,KAAA;AAEA,IAAA,IAAIiF,aAAa,CAAC7T,MAAM,CAAC,EAAE;AACzB;AACA;MACA,IAAI8T,aAAa,GAAGnB,mBAAmB,CAAC9U,OAAO,EAAEwV,WAAW,CAAC3W,KAAK,CAACO,EAAE,CAAC,CAAA;AACtE,MAAA,IAAIud,OAAO,GAAG,MAAMP,aAAa,CAC/BzH,OAAO,EACP3U,OAAO,EACPqb,cAAc,EACd5iB,SAAS,EACT;AACE,QAAA,CAACwd,aAAa,CAACpX,KAAK,CAACO,EAAE,GAAG+C,MAAM,CAACjE,KAAAA;AACnC,OACF,CAAC,CAAA;;AAED;MACA,OAAAZ,QAAA,KACKqf,OAAO,EAAA;AACVnB,QAAAA,UAAU,EAAE3P,oBAAoB,CAAC1J,MAAM,CAACjE,KAAK,CAAC,GAC1CiE,MAAM,CAACjE,KAAK,CAAC4J,MAAM,GACnB,GAAG;AACP0H,QAAAA,UAAU,EAAE,IAAI;AAChBkM,QAAAA,aAAa,EAAApe,QAAA,CAAA,EAAA,EACP6E,MAAM,CAAC4F,OAAO,GAAG;AAAE,UAAA,CAACyN,WAAW,CAAC3W,KAAK,CAACO,EAAE,GAAG+C,MAAM,CAAC4F,OAAAA;SAAS,GAAG,EAAE,CAAA;AACrE,OAAA,CAAA,CAAA;AAEL,KAAA;;AAEA;IACA,IAAI6U,aAAa,GAAG,IAAIxH,OAAO,CAACT,OAAO,CAACxY,GAAG,EAAE;MAC3C4L,OAAO,EAAE4M,OAAO,CAAC5M,OAAO;MACxBwD,QAAQ,EAAEoJ,OAAO,CAACpJ,QAAQ;MAC1BnC,MAAM,EAAEuL,OAAO,CAACvL,MAAAA;AAClB,KAAC,CAAC,CAAA;IACF,IAAIuT,OAAO,GAAG,MAAMP,aAAa,CAACQ,aAAa,EAAE5c,OAAO,EAAEqb,cAAc,CAAC,CAAA;AAEzE,IAAA,OAAA/d,QAAA,CACKqf,EAAAA,EAAAA,OAAO,EAENxa,MAAM,CAACqZ,UAAU,GAAG;MAAEA,UAAU,EAAErZ,MAAM,CAACqZ,UAAAA;KAAY,GAAG,EAAE,EAAA;AAC9DhM,MAAAA,UAAU,EAAE;AACV,QAAA,CAACgG,WAAW,CAAC3W,KAAK,CAACO,EAAE,GAAG+C,MAAM,CAAC1B,IAAAA;OAChC;AACDib,MAAAA,aAAa,EAAApe,QAAA,CAAA,EAAA,EACP6E,MAAM,CAAC4F,OAAO,GAAG;AAAE,QAAA,CAACyN,WAAW,CAAC3W,KAAK,CAACO,EAAE,GAAG+C,MAAM,CAAC4F,OAAAA;OAAS,GAAG,EAAE,CAAA;AACrE,KAAA,CAAA,CAAA;AAEL,GAAA;EAEA,eAAeqU,aAAaA,CAC1BzH,OAAgB,EAChB3U,OAAiC,EACjCqb,cAAuB,EACvBa,UAAmC,EACnChH,kBAA8B,EAO9B;AACA,IAAA,IAAIqH,cAAc,GAAGL,UAAU,IAAI,IAAI,CAAA;;AAEvC;AACA,IAAA,IACEK,cAAc,IACd,EAACL,UAAU,IAAVA,IAAAA,IAAAA,UAAU,CAAErd,KAAK,CAACkQ,MAAM,CACzB,IAAA,EAACmN,UAAU,IAAVA,IAAAA,IAAAA,UAAU,CAAErd,KAAK,CAACgQ,IAAI,CACvB,EAAA;MACA,MAAML,sBAAsB,CAAC,GAAG,EAAE;QAChCmH,MAAM,EAAEhB,OAAO,CAACgB,MAAM;QACtBnc,QAAQ,EAAE,IAAIS,GAAG,CAAC0a,OAAO,CAACxY,GAAG,CAAC,CAAC3C,QAAQ;AACvCoc,QAAAA,OAAO,EAAEsG,UAAU,IAAA,IAAA,GAAA,KAAA,CAAA,GAAVA,UAAU,CAAErd,KAAK,CAACO,EAAAA;AAC7B,OAAC,CAAC,CAAA;AACJ,KAAA;IAEA,IAAI2Y,cAAc,GAAGmE,UAAU,GAC3B,CAACA,UAAU,CAAC,GACZW,6BAA6B,CAC3B7c,OAAO,EACP+D,MAAM,CAACkP,IAAI,CAACiC,kBAAkB,IAAI,EAAE,CAAC,CAAC,CAAC,CACzC,CAAC,CAAA;AACL,IAAA,IAAImB,aAAa,GAAG0B,cAAc,CAAC5U,MAAM,CACtCyL,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACkQ,MAAM,IAAIH,CAAC,CAAC/P,KAAK,CAACgQ,IACnC,CAAC,CAAA;;AAED;AACA,IAAA,IAAIwH,aAAa,CAAC1d,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO;QACLqH,OAAO;AACP;AACAO,QAAAA,UAAU,EAAEP,OAAO,CAACoD,MAAM,CACxB,CAACiG,GAAG,EAAEuF,CAAC,KAAK7K,MAAM,CAAC1F,MAAM,CAACgL,GAAG,EAAE;AAAE,UAAA,CAACuF,CAAC,CAAC/P,KAAK,CAACO,EAAE,GAAG,IAAA;AAAK,SAAC,CAAC,EACtD,EACF,CAAC;QACD4P,MAAM,EAAEkG,kBAAkB,IAAI,IAAI;AAClCsG,QAAAA,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,EAAE;AACjB1K,QAAAA,eAAe,EAAE,IAAA;OAClB,CAAA;AACH,KAAA;AAEA,IAAA,IAAIiG,OAAO,GAAG,MAAMlO,OAAO,CAACuQ,GAAG,CAAC,CAC9B,GAAGhD,aAAa,CAACje,GAAG,CAAEkI,KAAK,IACzBuV,kBAAkB,CAChB,QAAQ,EACRlB,OAAO,EACPrU,KAAK,EACLN,OAAO,EACPd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,EACRgO,MAAM,CAAChH,oBAAoB,EAC3B;AAAE6V,MAAAA,eAAe,EAAE,IAAI;MAAED,cAAc;AAAElB,MAAAA,cAAAA;KAC3C,CACF,CAAC,CACF,CAAC,CAAA;AAEF,IAAA,IAAI1G,OAAO,CAACvL,MAAM,CAACa,OAAO,EAAE;AAC1BwS,MAAAA,8BAA8B,CAAC9H,OAAO,EAAE4H,cAAc,EAAE5O,MAAM,CAAC,CAAA;AACjE,KAAA;;AAEA;AACA,IAAA,IAAIoD,eAAe,GAAG,IAAIrB,GAAG,EAAwB,CAAA;AACrD,IAAA,IAAIiN,OAAO,GAAGG,sBAAsB,CAClC9c,OAAO,EACPqW,aAAa,EACbW,OAAO,EACP9B,kBAAkB,EAClBnE,eACF,CAAC,CAAA;;AAED;AACA,IAAA,IAAIgM,eAAe,GAAG,IAAIpe,GAAG,CAC3B0X,aAAa,CAACje,GAAG,CAAEkI,KAAK,IAAKA,KAAK,CAACzB,KAAK,CAACO,EAAE,CAC7C,CAAC,CAAA;AACDY,IAAAA,OAAO,CAACsB,OAAO,CAAEhB,KAAK,IAAK;MACzB,IAAI,CAACyc,eAAe,CAAC9U,GAAG,CAAC3H,KAAK,CAACzB,KAAK,CAACO,EAAE,CAAC,EAAE;QACxCud,OAAO,CAACpc,UAAU,CAACD,KAAK,CAACzB,KAAK,CAACO,EAAE,CAAC,GAAG,IAAI,CAAA;AAC3C,OAAA;AACF,KAAC,CAAC,CAAA;IAEF,OAAA9B,QAAA,KACKqf,OAAO,EAAA;MACV3c,OAAO;AACP+Q,MAAAA,eAAe,EACbA,eAAe,CAAClG,IAAI,GAAG,CAAC,GACpB9G,MAAM,CAACiZ,WAAW,CAACjM,eAAe,CAAC5Y,OAAO,EAAE,CAAC,GAC7C,IAAA;AAAI,KAAA,CAAA,CAAA;AAEd,GAAA;EAEA,OAAO;IACLsV,UAAU;IACV0N,KAAK;AACLU,IAAAA,UAAAA;GACD,CAAA;AACH,CAAA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACO,SAASoB,yBAAyBA,CACvCle,MAAiC,EACjC4d,OAA6B,EAC7Bze,KAAU,EACV;AACA,EAAA,IAAIgf,UAAgC,GAAA5f,QAAA,CAAA,EAAA,EAC/Bqf,OAAO,EAAA;IACVnB,UAAU,EAAE3P,oBAAoB,CAAC3N,KAAK,CAAC,GAAGA,KAAK,CAAC4J,MAAM,GAAG,GAAG;AAC5DkH,IAAAA,MAAM,EAAE;MACN,CAAC2N,OAAO,CAACQ,0BAA0B,IAAIpe,MAAM,CAAC,CAAC,CAAC,CAACK,EAAE,GAAGlB,KAAAA;AACxD,KAAA;GACD,CAAA,CAAA;AACD,EAAA,OAAOgf,UAAU,CAAA;AACnB,CAAA;AAEA,SAAST,8BAA8BA,CACrC9H,OAAgB,EAChB4H,cAAuB,EACvB5O,MAAiC,EACjC;EACA,IAAIA,MAAM,CAACuN,mBAAmB,IAAIvG,OAAO,CAACvL,MAAM,CAACgU,MAAM,KAAK3kB,SAAS,EAAE;AACrE,IAAA,MAAMkc,OAAO,CAACvL,MAAM,CAACgU,MAAM,CAAA;AAC7B,GAAA;AAEA,EAAA,IAAIzH,MAAM,GAAG4G,cAAc,GAAG,YAAY,GAAG,OAAO,CAAA;AACpD,EAAA,MAAM,IAAI5f,KAAK,CAAIgZ,MAAM,GAAoBhB,mBAAAA,GAAAA,OAAO,CAACgB,MAAM,GAAIhB,GAAAA,GAAAA,OAAO,CAACxY,GAAK,CAAC,CAAA;AAC/E,CAAA;AAEA,SAASkhB,sBAAsBA,CAC7BnL,IAAgC,EACG;EACnC,OACEA,IAAI,IAAI,IAAI,KACV,UAAU,IAAIA,IAAI,IAAIA,IAAI,CAAC1F,QAAQ,IAAI,IAAI,IAC1C,MAAM,IAAI0F,IAAI,IAAIA,IAAI,CAACoL,IAAI,KAAK7kB,SAAU,CAAC,CAAA;AAElD,CAAA;AAEA,SAAS+a,WAAWA,CAClBla,QAAc,EACd0G,OAAiC,EACjCL,QAAgB,EAChB4d,eAAwB,EACxBnkB,EAAa,EACbuN,oBAA6B,EAC7B8M,WAAoB,EACpBC,QAA8B,EAC9B;AACA,EAAA,IAAI8J,iBAA2C,CAAA;AAC/C,EAAA,IAAIC,gBAAoD,CAAA;AACxD,EAAA,IAAIhK,WAAW,EAAE;AACf;AACA;AACA+J,IAAAA,iBAAiB,GAAG,EAAE,CAAA;AACtB,IAAA,KAAK,IAAIld,KAAK,IAAIN,OAAO,EAAE;AACzBwd,MAAAA,iBAAiB,CAACjjB,IAAI,CAAC+F,KAAK,CAAC,CAAA;AAC7B,MAAA,IAAIA,KAAK,CAACzB,KAAK,CAACO,EAAE,KAAKqU,WAAW,EAAE;AAClCgK,QAAAA,gBAAgB,GAAGnd,KAAK,CAAA;AACxB,QAAA,MAAA;AACF,OAAA;AACF,KAAA;AACF,GAAC,MAAM;AACLkd,IAAAA,iBAAiB,GAAGxd,OAAO,CAAA;IAC3Byd,gBAAgB,GAAGzd,OAAO,CAACA,OAAO,CAACrH,MAAM,GAAG,CAAC,CAAC,CAAA;AAChD,GAAA;;AAEA;AACA,EAAA,IAAIwB,IAAI,GAAG0M,SAAS,CAClBzN,EAAE,GAAGA,EAAE,GAAG,GAAG,EACbsN,mBAAmB,CAAC8W,iBAAiB,EAAE7W,oBAAoB,CAAC,EAC5D/G,aAAa,CAACtG,QAAQ,CAACE,QAAQ,EAAEmG,QAAQ,CAAC,IAAIrG,QAAQ,CAACE,QAAQ,EAC/Dka,QAAQ,KAAK,MACf,CAAC,CAAA;;AAED;AACA;AACA;EACA,IAAIta,EAAE,IAAI,IAAI,EAAE;AACde,IAAAA,IAAI,CAACE,MAAM,GAAGf,QAAQ,CAACe,MAAM,CAAA;AAC7BF,IAAAA,IAAI,CAACG,IAAI,GAAGhB,QAAQ,CAACgB,IAAI,CAAA;AAC3B,GAAA;;AAEA;AACA,EAAA,IACE,CAAClB,EAAE,IAAI,IAAI,IAAIA,EAAE,KAAK,EAAE,IAAIA,EAAE,KAAK,GAAG,KACtCqkB,gBAAgB,IAChBA,gBAAgB,CAAC5e,KAAK,CAACvG,KAAK,IAC5B,CAAColB,kBAAkB,CAACvjB,IAAI,CAACE,MAAM,CAAC,EAChC;AACAF,IAAAA,IAAI,CAACE,MAAM,GAAGF,IAAI,CAACE,MAAM,GACrBF,IAAI,CAACE,MAAM,CAACO,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,GACrC,QAAQ,CAAA;AACd,GAAA;;AAEA;AACA;AACA;AACA;AACA,EAAA,IAAI2iB,eAAe,IAAI5d,QAAQ,KAAK,GAAG,EAAE;IACvCxF,IAAI,CAACX,QAAQ,GACXW,IAAI,CAACX,QAAQ,KAAK,GAAG,GAAGmG,QAAQ,GAAGsB,SAAS,CAAC,CAACtB,QAAQ,EAAExF,IAAI,CAACX,QAAQ,CAAC,CAAC,CAAA;AAC3E,GAAA;EAEA,OAAOM,UAAU,CAACK,IAAI,CAAC,CAAA;AACzB,CAAA;;AAEA;AACA;AACA,SAASyZ,wBAAwBA,CAC/B+J,mBAA4B,EAC5BC,SAAkB,EAClBzjB,IAAY,EACZ+X,IAAiC,EAKjC;AACA;EACA,IAAI,CAACA,IAAI,IAAI,CAACmL,sBAAsB,CAACnL,IAAI,CAAC,EAAE;IAC1C,OAAO;AAAE/X,MAAAA,IAAAA;KAAM,CAAA;AACjB,GAAA;EAEA,IAAI+X,IAAI,CAAC7F,UAAU,IAAI,CAACiP,aAAa,CAACpJ,IAAI,CAAC7F,UAAU,CAAC,EAAE;IACtD,OAAO;MACLlS,IAAI;AACJ+D,MAAAA,KAAK,EAAEsQ,sBAAsB,CAAC,GAAG,EAAE;QAAEmH,MAAM,EAAEzD,IAAI,CAAC7F,UAAAA;OAAY,CAAA;KAC/D,CAAA;AACH,GAAA;EAEA,IAAIwR,mBAAmB,GAAGA,OAAO;IAC/B1jB,IAAI;AACJ+D,IAAAA,KAAK,EAAEsQ,sBAAsB,CAAC,GAAG,EAAE;AAAEkH,MAAAA,IAAI,EAAE,cAAA;KAAgB,CAAA;AAC7D,GAAC,CAAC,CAAA;;AAEF;AACA,EAAA,IAAIoI,aAAa,GAAG5L,IAAI,CAAC7F,UAAU,IAAI,KAAK,CAAA;AAC5C,EAAA,IAAIA,UAAU,GAAGsR,mBAAmB,GAC/BG,aAAa,CAACC,WAAW,EAAE,GAC3BD,aAAa,CAACpY,WAAW,EAAiB,CAAA;AAC/C,EAAA,IAAI4G,UAAU,GAAG0R,iBAAiB,CAAC7jB,IAAI,CAAC,CAAA;AAExC,EAAA,IAAI+X,IAAI,CAACoL,IAAI,KAAK7kB,SAAS,EAAE;AAC3B,IAAA,IAAIyZ,IAAI,CAAC3F,WAAW,KAAK,YAAY,EAAE;AACrC;AACA,MAAA,IAAI,CAACwG,gBAAgB,CAAC1G,UAAU,CAAC,EAAE;QACjC,OAAOwR,mBAAmB,EAAE,CAAA;AAC9B,OAAA;MAEA,IAAIpR,IAAI,GACN,OAAOyF,IAAI,CAACoL,IAAI,KAAK,QAAQ,GACzBpL,IAAI,CAACoL,IAAI,GACTpL,IAAI,CAACoL,IAAI,YAAYW,QAAQ,IAC7B/L,IAAI,CAACoL,IAAI,YAAYY,eAAe;AACpC;AACAxV,MAAAA,KAAK,CAACvB,IAAI,CAAC+K,IAAI,CAACoL,IAAI,CAACnlB,OAAO,EAAE,CAAC,CAACiL,MAAM,CACpC,CAACiG,GAAG,EAAA0B,KAAA,KAAA;AAAA,QAAA,IAAE,CAAC3M,IAAI,EAAE3B,KAAK,CAAC,GAAAsO,KAAA,CAAA;AAAA,QAAA,OAAA,EAAA,GAAQ1B,GAAG,GAAGjL,IAAI,GAAA,GAAA,GAAI3B,KAAK,GAAA,IAAA,CAAA;OAAI,EAClD,EACF,CAAC,GACD6H,MAAM,CAAC4N,IAAI,CAACoL,IAAI,CAAC,CAAA;MAEvB,OAAO;QACLnjB,IAAI;AACJwZ,QAAAA,UAAU,EAAE;UACVtH,UAAU;UACVC,UAAU;UACVC,WAAW,EAAE2F,IAAI,CAAC3F,WAAW;AAC7BC,UAAAA,QAAQ,EAAE/T,SAAS;AACnBkP,UAAAA,IAAI,EAAElP,SAAS;AACfgU,UAAAA,IAAAA;AACF,SAAA;OACD,CAAA;AACH,KAAC,MAAM,IAAIyF,IAAI,CAAC3F,WAAW,KAAK,kBAAkB,EAAE;AAClD;AACA,MAAA,IAAI,CAACwG,gBAAgB,CAAC1G,UAAU,CAAC,EAAE;QACjC,OAAOwR,mBAAmB,EAAE,CAAA;AAC9B,OAAA;MAEA,IAAI;QACF,IAAIlW,IAAI,GACN,OAAOuK,IAAI,CAACoL,IAAI,KAAK,QAAQ,GAAG3jB,IAAI,CAACwkB,KAAK,CAACjM,IAAI,CAACoL,IAAI,CAAC,GAAGpL,IAAI,CAACoL,IAAI,CAAA;QAEnE,OAAO;UACLnjB,IAAI;AACJwZ,UAAAA,UAAU,EAAE;YACVtH,UAAU;YACVC,UAAU;YACVC,WAAW,EAAE2F,IAAI,CAAC3F,WAAW;AAC7BC,YAAAA,QAAQ,EAAE/T,SAAS;YACnBkP,IAAI;AACJ8E,YAAAA,IAAI,EAAEhU,SAAAA;AACR,WAAA;SACD,CAAA;OACF,CAAC,OAAOsE,CAAC,EAAE;QACV,OAAO8gB,mBAAmB,EAAE,CAAA;AAC9B,OAAA;AACF,KAAA;AACF,GAAA;AAEArhB,EAAAA,SAAS,CACP,OAAOyhB,QAAQ,KAAK,UAAU,EAC9B,+CACF,CAAC,CAAA;AAED,EAAA,IAAIG,YAA6B,CAAA;AACjC,EAAA,IAAI5R,QAAkB,CAAA;EAEtB,IAAI0F,IAAI,CAAC1F,QAAQ,EAAE;AACjB4R,IAAAA,YAAY,GAAGC,6BAA6B,CAACnM,IAAI,CAAC1F,QAAQ,CAAC,CAAA;IAC3DA,QAAQ,GAAG0F,IAAI,CAAC1F,QAAQ,CAAA;AAC1B,GAAC,MAAM,IAAI0F,IAAI,CAACoL,IAAI,YAAYW,QAAQ,EAAE;AACxCG,IAAAA,YAAY,GAAGC,6BAA6B,CAACnM,IAAI,CAACoL,IAAI,CAAC,CAAA;IACvD9Q,QAAQ,GAAG0F,IAAI,CAACoL,IAAI,CAAA;AACtB,GAAC,MAAM,IAAIpL,IAAI,CAACoL,IAAI,YAAYY,eAAe,EAAE;IAC/CE,YAAY,GAAGlM,IAAI,CAACoL,IAAI,CAAA;AACxB9Q,IAAAA,QAAQ,GAAG8R,6BAA6B,CAACF,YAAY,CAAC,CAAA;AACxD,GAAC,MAAM,IAAIlM,IAAI,CAACoL,IAAI,IAAI,IAAI,EAAE;AAC5Bc,IAAAA,YAAY,GAAG,IAAIF,eAAe,EAAE,CAAA;AACpC1R,IAAAA,QAAQ,GAAG,IAAIyR,QAAQ,EAAE,CAAA;AAC3B,GAAC,MAAM;IACL,IAAI;AACFG,MAAAA,YAAY,GAAG,IAAIF,eAAe,CAAChM,IAAI,CAACoL,IAAI,CAAC,CAAA;AAC7C9Q,MAAAA,QAAQ,GAAG8R,6BAA6B,CAACF,YAAY,CAAC,CAAA;KACvD,CAAC,OAAOrhB,CAAC,EAAE;MACV,OAAO8gB,mBAAmB,EAAE,CAAA;AAC9B,KAAA;AACF,GAAA;AAEA,EAAA,IAAIlK,UAAsB,GAAG;IAC3BtH,UAAU;IACVC,UAAU;AACVC,IAAAA,WAAW,EACR2F,IAAI,IAAIA,IAAI,CAAC3F,WAAW,IAAK,mCAAmC;IACnEC,QAAQ;AACR7E,IAAAA,IAAI,EAAElP,SAAS;AACfgU,IAAAA,IAAI,EAAEhU,SAAAA;GACP,CAAA;AAED,EAAA,IAAIsa,gBAAgB,CAACY,UAAU,CAACtH,UAAU,CAAC,EAAE;IAC3C,OAAO;MAAElS,IAAI;AAAEwZ,MAAAA,UAAAA;KAAY,CAAA;AAC7B,GAAA;;AAEA;AACA,EAAA,IAAInW,UAAU,GAAGpD,SAAS,CAACD,IAAI,CAAC,CAAA;AAChC;AACA;AACA;AACA,EAAA,IAAIyjB,SAAS,IAAIpgB,UAAU,CAACnD,MAAM,IAAIqjB,kBAAkB,CAAClgB,UAAU,CAACnD,MAAM,CAAC,EAAE;AAC3E+jB,IAAAA,YAAY,CAACG,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;AAClC,GAAA;EACA/gB,UAAU,CAACnD,MAAM,GAAA,GAAA,GAAO+jB,YAAc,CAAA;EAEtC,OAAO;AAAEjkB,IAAAA,IAAI,EAAEL,UAAU,CAAC0D,UAAU,CAAC;AAAEmW,IAAAA,UAAAA;GAAY,CAAA;AACrD,CAAA;;AAEA;AACA;AACA,SAASkJ,6BAA6BA,CACpC7c,OAAiC,EACjCwe,UAAmB,EACnB;EACA,IAAIC,eAAe,GAAGze,OAAO,CAAA;AAC7B,EAAA,IAAIwe,UAAU,EAAE;AACd,IAAA,IAAIlmB,KAAK,GAAG0H,OAAO,CAAC0e,SAAS,CAAE9P,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACO,EAAE,KAAKof,UAAU,CAAC,CAAA;IAC/D,IAAIlmB,KAAK,IAAI,CAAC,EAAE;MACdmmB,eAAe,GAAGze,OAAO,CAAC1D,KAAK,CAAC,CAAC,EAAEhE,KAAK,CAAC,CAAA;AAC3C,KAAA;AACF,GAAA;AACA,EAAA,OAAOmmB,eAAe,CAAA;AACxB,CAAA;AAEA,SAASlI,gBAAgBA,CACvBxc,OAAgB,EAChBvB,KAAkB,EAClBwH,OAAiC,EACjC2T,UAAkC,EAClCra,QAAkB,EAClBqlB,aAAsB,EACtBvO,sBAA+B,EAC/BC,uBAAiC,EACjCC,qBAA+B,EAC/BQ,eAA4B,EAC5BF,gBAA6C,EAC7CD,gBAA6B,EAC7B2D,WAAsC,EACtC3U,QAA4B,EAC5BkV,iBAA6B,EAC7Bf,YAAwB,EAC2B;EACnD,IAAIwE,YAAY,GAAGxE,YAAY,GAC3B/P,MAAM,CAACiY,MAAM,CAAClI,YAAY,CAAC,CAAC,CAAC,CAAC,GAC9Be,iBAAiB,GACjB9Q,MAAM,CAACiY,MAAM,CAACnH,iBAAiB,CAAC,CAAC,CAAC,CAAC,GACnCpc,SAAS,CAAA;EAEb,IAAImmB,UAAU,GAAG7kB,OAAO,CAACC,SAAS,CAACxB,KAAK,CAACc,QAAQ,CAAC,CAAA;AAClD,EAAA,IAAIulB,OAAO,GAAG9kB,OAAO,CAACC,SAAS,CAACV,QAAQ,CAAC,CAAA;;AAEzC;AACA,EAAA,IAAIklB,UAAU,GAAG1K,YAAY,GAAG/P,MAAM,CAACkP,IAAI,CAACa,YAAY,CAAC,CAAC,CAAC,CAAC,GAAGrb,SAAS,CAAA;AACxE,EAAA,IAAIgmB,eAAe,GAAG5B,6BAA6B,CAAC7c,OAAO,EAAEwe,UAAU,CAAC,CAAA;EAExE,IAAIM,iBAAiB,GAAGL,eAAe,CAACtb,MAAM,CAAC,CAAC7C,KAAK,EAAEhI,KAAK,KAAK;IAC/D,IAAI;AAAEuG,MAAAA,KAAAA;AAAM,KAAC,GAAGyB,KAAK,CAAA;IACrB,IAAIzB,KAAK,CAACgQ,IAAI,EAAE;AACd;AACA,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AAEA,IAAA,IAAIhQ,KAAK,CAACkQ,MAAM,IAAI,IAAI,EAAE;AACxB,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AAEA,IAAA,IAAI4P,aAAa,EAAE;AACjB,MAAA,IAAI9f,KAAK,CAACkQ,MAAM,CAACE,OAAO,EAAE;AACxB,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;MACA,OACEzW,KAAK,CAAC+H,UAAU,CAAC1B,KAAK,CAACO,EAAE,CAAC,KAAK3G,SAAS;AACxC;AACC,MAAA,CAACD,KAAK,CAACwW,MAAM,IAAIxW,KAAK,CAACwW,MAAM,CAACnQ,KAAK,CAACO,EAAE,CAAC,KAAK3G,SAAS,CAAC,CAAA;AAE3D,KAAA;;AAEA;AACA,IAAA,IACEsmB,WAAW,CAACvmB,KAAK,CAAC+H,UAAU,EAAE/H,KAAK,CAACwH,OAAO,CAAC1H,KAAK,CAAC,EAAEgI,KAAK,CAAC,IAC1D+P,uBAAuB,CAACnN,IAAI,CAAE9D,EAAE,IAAKA,EAAE,KAAKkB,KAAK,CAACzB,KAAK,CAACO,EAAE,CAAC,EAC3D;AACA,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA;AACA;AACA;AACA,IAAA,IAAI4f,iBAAiB,GAAGxmB,KAAK,CAACwH,OAAO,CAAC1H,KAAK,CAAC,CAAA;IAC5C,IAAI2mB,cAAc,GAAG3e,KAAK,CAAA;AAE1B,IAAA,OAAO4e,sBAAsB,CAAC5e,KAAK,EAAAhD,QAAA,CAAA;MACjCshB,UAAU;MACVO,aAAa,EAAEH,iBAAiB,CAACxe,MAAM;MACvCqe,OAAO;MACPO,UAAU,EAAEH,cAAc,CAACze,MAAAA;AAAM,KAAA,EAC9BmT,UAAU,EAAA;MACb2E,YAAY;MACZ+G,uBAAuB;AACrB;MACAjP,sBAAsB;AACtB;AACAwO,MAAAA,UAAU,CAACplB,QAAQ,GAAGolB,UAAU,CAACvkB,MAAM,KACrCwkB,OAAO,CAACrlB,QAAQ,GAAGqlB,OAAO,CAACxkB,MAAM;AACnC;MACAukB,UAAU,CAACvkB,MAAM,KAAKwkB,OAAO,CAACxkB,MAAM,IACpCilB,kBAAkB,CAACN,iBAAiB,EAAEC,cAAc,CAAA;AAAC,KAAA,CACxD,CAAC,CAAA;AACJ,GAAC,CAAC,CAAA;;AAEF;EACA,IAAI3I,oBAA2C,GAAG,EAAE,CAAA;AACpD1F,EAAAA,gBAAgB,CAACtP,OAAO,CAAC,CAACyV,CAAC,EAAE1d,GAAG,KAAK;AACnC;AACA;AACA;AACA;AACA;IACA,IACEslB,aAAa,IACb,CAAC3e,OAAO,CAACkD,IAAI,CAAE0L,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACO,EAAE,KAAK2X,CAAC,CAACnB,OAAO,CAAC,IAC9C9E,eAAe,CAAC7I,GAAG,CAAC5O,GAAG,CAAC,EACxB;AACA,MAAA,OAAA;AACF,KAAA;IAEA,IAAIkmB,cAAc,GAAG9f,WAAW,CAAC6U,WAAW,EAAEyC,CAAC,CAAC5c,IAAI,EAAEwF,QAAQ,CAAC,CAAA;;AAE/D;AACA;AACA;AACA;IACA,IAAI,CAAC4f,cAAc,EAAE;MACnBjJ,oBAAoB,CAAC/b,IAAI,CAAC;QACxBlB,GAAG;QACHuc,OAAO,EAAEmB,CAAC,CAACnB,OAAO;QAClBzb,IAAI,EAAE4c,CAAC,CAAC5c,IAAI;AACZ6F,QAAAA,OAAO,EAAE,IAAI;AACbM,QAAAA,KAAK,EAAE,IAAI;AACX0I,QAAAA,UAAU,EAAE,IAAA;AACd,OAAC,CAAC,CAAA;AACF,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA;IACA,IAAIqJ,OAAO,GAAG7Z,KAAK,CAACiX,QAAQ,CAACzF,GAAG,CAAC3Q,GAAG,CAAC,CAAA;IACrC,IAAImmB,YAAY,GAAG/J,cAAc,CAAC8J,cAAc,EAAExI,CAAC,CAAC5c,IAAI,CAAC,CAAA;IAEzD,IAAIslB,gBAAgB,GAAG,KAAK,CAAA;AAC5B,IAAA,IAAI9O,gBAAgB,CAAC1I,GAAG,CAAC5O,GAAG,CAAC,EAAE;AAC7B;AACAomB,MAAAA,gBAAgB,GAAG,KAAK,CAAA;KACzB,MAAM,IAAInP,qBAAqB,CAAC9O,QAAQ,CAACnI,GAAG,CAAC,EAAE;AAC9C;AACAomB,MAAAA,gBAAgB,GAAG,IAAI,CAAA;AACzB,KAAC,MAAM,IACLpN,OAAO,IACPA,OAAO,CAAC7Z,KAAK,KAAK,MAAM,IACxB6Z,OAAO,CAAC5R,IAAI,KAAKhI,SAAS,EAC1B;AACA;AACA;AACA;AACAgnB,MAAAA,gBAAgB,GAAGrP,sBAAsB,CAAA;AAC3C,KAAC,MAAM;AACL;AACA;AACAqP,MAAAA,gBAAgB,GAAGP,sBAAsB,CAACM,YAAY,EAAAliB,QAAA,CAAA;QACpDshB,UAAU;AACVO,QAAAA,aAAa,EAAE3mB,KAAK,CAACwH,OAAO,CAACxH,KAAK,CAACwH,OAAO,CAACrH,MAAM,GAAG,CAAC,CAAC,CAAC6H,MAAM;QAC7Dqe,OAAO;QACPO,UAAU,EAAEpf,OAAO,CAACA,OAAO,CAACrH,MAAM,GAAG,CAAC,CAAC,CAAC6H,MAAAA;AAAM,OAAA,EAC3CmT,UAAU,EAAA;QACb2E,YAAY;AACZ+G,QAAAA,uBAAuB,EAAEjP,sBAAAA;AAAsB,OAAA,CAChD,CAAC,CAAA;AACJ,KAAA;AAEA,IAAA,IAAIqP,gBAAgB,EAAE;MACpBnJ,oBAAoB,CAAC/b,IAAI,CAAC;QACxBlB,GAAG;QACHuc,OAAO,EAAEmB,CAAC,CAACnB,OAAO;QAClBzb,IAAI,EAAE4c,CAAC,CAAC5c,IAAI;AACZ6F,QAAAA,OAAO,EAAEuf,cAAc;AACvBjf,QAAAA,KAAK,EAAEkf,YAAY;QACnBxW,UAAU,EAAE,IAAIC,eAAe,EAAC;AAClC,OAAC,CAAC,CAAA;AACJ,KAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,OAAO,CAAC6V,iBAAiB,EAAExI,oBAAoB,CAAC,CAAA;AAClD,CAAA;AAEA,SAASyI,WAAWA,CAClBW,iBAA4B,EAC5BC,YAAoC,EACpCrf,KAA6B,EAC7B;AACA,EAAA,IAAIsf,KAAK;AACP;AACA,EAAA,CAACD,YAAY;AACb;EACArf,KAAK,CAACzB,KAAK,CAACO,EAAE,KAAKugB,YAAY,CAAC9gB,KAAK,CAACO,EAAE,CAAA;;AAE1C;AACA;EACA,IAAIygB,aAAa,GAAGH,iBAAiB,CAACpf,KAAK,CAACzB,KAAK,CAACO,EAAE,CAAC,KAAK3G,SAAS,CAAA;;AAEnE;EACA,OAAOmnB,KAAK,IAAIC,aAAa,CAAA;AAC/B,CAAA;AAEA,SAASP,kBAAkBA,CACzBK,YAAoC,EACpCrf,KAA6B,EAC7B;AACA,EAAA,IAAIwf,WAAW,GAAGH,YAAY,CAAC9gB,KAAK,CAAC1E,IAAI,CAAA;AACzC,EAAA;AACE;AACAwlB,IAAAA,YAAY,CAACnmB,QAAQ,KAAK8G,KAAK,CAAC9G,QAAQ;AACxC;AACA;IACCsmB,WAAW,IAAI,IAAI,IAClBA,WAAW,CAAC9d,QAAQ,CAAC,GAAG,CAAC,IACzB2d,YAAY,CAACnf,MAAM,CAAC,GAAG,CAAC,KAAKF,KAAK,CAACE,MAAM,CAAC,GAAG,CAAA;AAAE,IAAA;AAErD,CAAA;AAEA,SAAS0e,sBAAsBA,CAC7Ba,WAAmC,EACnCC,GAAiC,EACjC;AACA,EAAA,IAAID,WAAW,CAAClhB,KAAK,CAAC4gB,gBAAgB,EAAE;IACtC,IAAIQ,WAAW,GAAGF,WAAW,CAAClhB,KAAK,CAAC4gB,gBAAgB,CAACO,GAAG,CAAC,CAAA;AACzD,IAAA,IAAI,OAAOC,WAAW,KAAK,SAAS,EAAE;AACpC,MAAA,OAAOA,WAAW,CAAA;AACpB,KAAA;AACF,GAAA;EAEA,OAAOD,GAAG,CAACX,uBAAuB,CAAA;AACpC,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAea,mBAAmBA,CAChCrhB,KAA8B,EAC9BG,kBAA8C,EAC9CE,QAAuB,EACvB;AACA,EAAA,IAAI,CAACL,KAAK,CAACgQ,IAAI,EAAE;AACf,IAAA,OAAA;AACF,GAAA;AAEA,EAAA,IAAIsR,SAAS,GAAG,MAAMthB,KAAK,CAACgQ,IAAI,EAAE,CAAA;;AAElC;AACA;AACA;AACA,EAAA,IAAI,CAAChQ,KAAK,CAACgQ,IAAI,EAAE;AACf,IAAA,OAAA;AACF,GAAA;AAEA,EAAA,IAAIuR,aAAa,GAAGlhB,QAAQ,CAACL,KAAK,CAACO,EAAE,CAAC,CAAA;AACtC5C,EAAAA,SAAS,CAAC4jB,aAAa,EAAE,4BAA4B,CAAC,CAAA;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,IAAIC,YAAiC,GAAG,EAAE,CAAA;AAC1C,EAAA,KAAK,IAAIC,iBAAiB,IAAIH,SAAS,EAAE;AACvC,IAAA,IAAII,gBAAgB,GAClBH,aAAa,CAACE,iBAAiB,CAA+B,CAAA;AAEhE,IAAA,IAAIE,2BAA2B,GAC7BD,gBAAgB,KAAK9nB,SAAS;AAC9B;AACA;AACA6nB,IAAAA,iBAAiB,KAAK,kBAAkB,CAAA;AAE1C7mB,IAAAA,OAAO,CACL,CAAC+mB,2BAA2B,EAC5B,aAAUJ,aAAa,CAAChhB,EAAE,GAAA,6BAAA,GAA4BkhB,iBAAiB,GAAA,KAAA,GAAA,6EACQ,IACjDA,4BAAAA,GAAAA,iBAAiB,yBACjD,CAAC,CAAA;IAED,IACE,CAACE,2BAA2B,IAC5B,CAAC9hB,kBAAkB,CAACuJ,GAAG,CAACqY,iBAAsC,CAAC,EAC/D;AACAD,MAAAA,YAAY,CAACC,iBAAiB,CAAC,GAC7BH,SAAS,CAACG,iBAAiB,CAA2B,CAAA;AAC1D,KAAA;AACF,GAAA;;AAEA;AACA;AACAvc,EAAAA,MAAM,CAAC1F,MAAM,CAAC+hB,aAAa,EAAEC,YAAY,CAAC,CAAA;;AAE1C;AACA;AACA;EACAtc,MAAM,CAAC1F,MAAM,CAAC+hB,aAAa,EAAA9iB,QAAA,CAKtB0B,EAAAA,EAAAA,kBAAkB,CAACohB,aAAa,CAAC,EAAA;AACpCvR,IAAAA,IAAI,EAAEpW,SAAAA;AAAS,GAAA,CAChB,CAAC,CAAA;AACJ,CAAA;AAEA,eAAeod,kBAAkBA,CAC/BH,IAAyB,EACzBf,OAAgB,EAChBrU,KAA6B,EAC7BN,OAAiC,EACjCd,QAAuB,EACvBF,kBAA8C,EAC9CW,QAAgB,EAChBgH,oBAA6B,EAC7BuL,IAIC,EACoB;AAAA,EAAA,IALrBA,IAIC,KAAA,KAAA,CAAA,EAAA;IAJDA,IAIC,GAAG,EAAE,CAAA;AAAA,GAAA;AAEN,EAAA,IAAIuO,UAAU,CAAA;AACd,EAAA,IAAIte,MAAM,CAAA;AACV,EAAA,IAAIue,QAAkC,CAAA;EAEtC,IAAIC,UAAU,GAAIC,OAAwC,IAAK;AAC7D;AACA,IAAA,IAAIhY,MAAkB,CAAA;AACtB,IAAA,IAAIC,YAAY,GAAG,IAAIC,OAAO,CAAC,CAACxD,CAAC,EAAEyD,CAAC,KAAMH,MAAM,GAAGG,CAAE,CAAC,CAAA;AACtD2X,IAAAA,QAAQ,GAAGA,MAAM9X,MAAM,EAAE,CAAA;IACzB+L,OAAO,CAACvL,MAAM,CAAC7K,gBAAgB,CAAC,OAAO,EAAEmiB,QAAQ,CAAC,CAAA;AAClD,IAAA,OAAO5X,OAAO,CAACa,IAAI,CAAC,CAClBiX,OAAO,CAAC;MACNjM,OAAO;MACPnU,MAAM,EAAEF,KAAK,CAACE,MAAM;MACpBmc,OAAO,EAAEzK,IAAI,CAACmJ,cAAAA;AAChB,KAAC,CAAC,EACFxS,YAAY,CACb,CAAC,CAAA;GACH,CAAA;EAED,IAAI;AACF,IAAA,IAAI+X,OAAO,GAAGtgB,KAAK,CAACzB,KAAK,CAAC6W,IAAI,CAAC,CAAA;AAE/B,IAAA,IAAIpV,KAAK,CAACzB,KAAK,CAACgQ,IAAI,EAAE;AACpB,MAAA,IAAI+R,OAAO,EAAE;AACX;AACA,QAAA,IAAIC,YAAY,CAAA;AAChB,QAAA,IAAI7E,MAAM,GAAG,MAAMlT,OAAO,CAACuQ,GAAG,CAAC;AAC7B;AACA;AACA;AACAsH,QAAAA,UAAU,CAACC,OAAO,CAAC,CAAC9W,KAAK,CAAE/M,CAAC,IAAK;AAC/B8jB,UAAAA,YAAY,GAAG9jB,CAAC,CAAA;AAClB,SAAC,CAAC,EACFmjB,mBAAmB,CAAC5f,KAAK,CAACzB,KAAK,EAAEG,kBAAkB,EAAEE,QAAQ,CAAC,CAC/D,CAAC,CAAA;AACF,QAAA,IAAI2hB,YAAY,EAAE;AAChB,UAAA,MAAMA,YAAY,CAAA;AACpB,SAAA;AACA1e,QAAAA,MAAM,GAAG6Z,MAAM,CAAC,CAAC,CAAC,CAAA;AACpB,OAAC,MAAM;AACL;QACA,MAAMkE,mBAAmB,CAAC5f,KAAK,CAACzB,KAAK,EAAEG,kBAAkB,EAAEE,QAAQ,CAAC,CAAA;AAEpE0hB,QAAAA,OAAO,GAAGtgB,KAAK,CAACzB,KAAK,CAAC6W,IAAI,CAAC,CAAA;AAC3B,QAAA,IAAIkL,OAAO,EAAE;AACX;AACA;AACA;AACAze,UAAAA,MAAM,GAAG,MAAMwe,UAAU,CAACC,OAAO,CAAC,CAAA;AACpC,SAAC,MAAM,IAAIlL,IAAI,KAAK,QAAQ,EAAE;UAC5B,IAAIvZ,GAAG,GAAG,IAAIlC,GAAG,CAAC0a,OAAO,CAACxY,GAAG,CAAC,CAAA;UAC9B,IAAI3C,QAAQ,GAAG2C,GAAG,CAAC3C,QAAQ,GAAG2C,GAAG,CAAC9B,MAAM,CAAA;UACxC,MAAMmU,sBAAsB,CAAC,GAAG,EAAE;YAChCmH,MAAM,EAAEhB,OAAO,CAACgB,MAAM;YACtBnc,QAAQ;AACRoc,YAAAA,OAAO,EAAEtV,KAAK,CAACzB,KAAK,CAACO,EAAAA;AACvB,WAAC,CAAC,CAAA;AACJ,SAAC,MAAM;AACL;AACA;UACA,OAAO;YAAEsW,IAAI,EAAEjX,UAAU,CAACgC,IAAI;AAAEA,YAAAA,IAAI,EAAEhI,SAAAA;WAAW,CAAA;AACnD,SAAA;AACF,OAAA;AACF,KAAC,MAAM,IAAI,CAACmoB,OAAO,EAAE;MACnB,IAAIzkB,GAAG,GAAG,IAAIlC,GAAG,CAAC0a,OAAO,CAACxY,GAAG,CAAC,CAAA;MAC9B,IAAI3C,QAAQ,GAAG2C,GAAG,CAAC3C,QAAQ,GAAG2C,GAAG,CAAC9B,MAAM,CAAA;MACxC,MAAMmU,sBAAsB,CAAC,GAAG,EAAE;AAChChV,QAAAA,QAAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;AACL2I,MAAAA,MAAM,GAAG,MAAMwe,UAAU,CAACC,OAAO,CAAC,CAAA;AACpC,KAAA;IAEApkB,SAAS,CACP2F,MAAM,KAAK1J,SAAS,EACpB,cAAeid,IAAAA,IAAI,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAU,CAAA,GAAA,aAAA,IAAA,IAAA,GACrDpV,KAAK,CAACzB,KAAK,CAACO,EAAE,GAA4CsW,2CAAAA,GAAAA,IAAI,GAAK,IAAA,CAAA,GAAA,4CAE3E,CAAC,CAAA;GACF,CAAC,OAAO3Y,CAAC,EAAE;IACV0jB,UAAU,GAAGhiB,UAAU,CAACP,KAAK,CAAA;AAC7BiE,IAAAA,MAAM,GAAGpF,CAAC,CAAA;AACZ,GAAC,SAAS;AACR,IAAA,IAAI2jB,QAAQ,EAAE;MACZ/L,OAAO,CAACvL,MAAM,CAAC5K,mBAAmB,CAAC,OAAO,EAAEkiB,QAAQ,CAAC,CAAA;AACvD,KAAA;AACF,GAAA;AAEA,EAAA,IAAI9E,UAAU,CAACzZ,MAAM,CAAC,EAAE;AACtB,IAAA,IAAI2F,MAAM,GAAG3F,MAAM,CAAC2F,MAAM,CAAA;;AAE1B;AACA,IAAA,IAAIoE,mBAAmB,CAACjE,GAAG,CAACH,MAAM,CAAC,EAAE;MACnC,IAAIxO,QAAQ,GAAG6I,MAAM,CAAC4F,OAAO,CAACiC,GAAG,CAAC,UAAU,CAAC,CAAA;AAC7CxN,MAAAA,SAAS,CACPlD,QAAQ,EACR,4EACF,CAAC,CAAA;;AAED;AACA,MAAA,IAAI,CAACwT,kBAAkB,CAACxJ,IAAI,CAAChK,QAAQ,CAAC,EAAE;AACtCA,QAAAA,QAAQ,GAAGka,WAAW,CACpB,IAAIvZ,GAAG,CAAC0a,OAAO,CAACxY,GAAG,CAAC,EACpB6D,OAAO,CAAC1D,KAAK,CAAC,CAAC,EAAE0D,OAAO,CAAC3D,OAAO,CAACiE,KAAK,CAAC,GAAG,CAAC,CAAC,EAC5CX,QAAQ,EACR,IAAI,EACJrG,QAAQ,EACRqN,oBACF,CAAC,CAAA;AACH,OAAC,MAAM,IAAI,CAACuL,IAAI,CAACsK,eAAe,EAAE;AAChC;AACA;AACA;QACA,IAAIoC,UAAU,GAAG,IAAI3kB,GAAG,CAAC0a,OAAO,CAACxY,GAAG,CAAC,CAAA;QACrC,IAAIA,GAAG,GAAG7C,QAAQ,CAACsC,UAAU,CAAC,IAAI,CAAC,GAC/B,IAAI3B,GAAG,CAAC2kB,UAAU,CAACkC,QAAQ,GAAGxnB,QAAQ,CAAC,GACvC,IAAIW,GAAG,CAACX,QAAQ,CAAC,CAAA;QACrB,IAAIynB,cAAc,GAAGnhB,aAAa,CAACzD,GAAG,CAAC3C,QAAQ,EAAEmG,QAAQ,CAAC,IAAI,IAAI,CAAA;QAClE,IAAIxD,GAAG,CAACmC,MAAM,KAAKsgB,UAAU,CAACtgB,MAAM,IAAIyiB,cAAc,EAAE;UACtDznB,QAAQ,GAAG6C,GAAG,CAAC3C,QAAQ,GAAG2C,GAAG,CAAC9B,MAAM,GAAG8B,GAAG,CAAC7B,IAAI,CAAA;AACjD,SAAA;AACF,OAAA;;AAEA;AACA;AACA;AACA;MACA,IAAI4X,IAAI,CAACsK,eAAe,EAAE;QACxBra,MAAM,CAAC4F,OAAO,CAACG,GAAG,CAAC,UAAU,EAAE5O,QAAQ,CAAC,CAAA;AACxC,QAAA,MAAM6I,MAAM,CAAA;AACd,OAAA;MAEA,OAAO;QACLuT,IAAI,EAAEjX,UAAU,CAAC8M,QAAQ;QACzBzD,MAAM;QACNxO,QAAQ;QACR2a,UAAU,EAAE9R,MAAM,CAAC4F,OAAO,CAACiC,GAAG,CAAC,oBAAoB,CAAC,KAAK,IAAI;QAC7DiP,cAAc,EAAE9W,MAAM,CAAC4F,OAAO,CAACiC,GAAG,CAAC,yBAAyB,CAAC,KAAK,IAAA;OACnE,CAAA;AACH,KAAA;;AAEA;AACA;AACA;IACA,IAAIkI,IAAI,CAACqK,cAAc,EAAE;AACvB,MAAA,IAAIyE,kBAAsC,GAAG;AAC3CtL,QAAAA,IAAI,EACF+K,UAAU,KAAKhiB,UAAU,CAACP,KAAK,GAAGO,UAAU,CAACP,KAAK,GAAGO,UAAU,CAACgC,IAAI;AACtEgL,QAAAA,QAAQ,EAAEtJ,MAAAA;OACX,CAAA;AACD,MAAA,MAAM6e,kBAAkB,CAAA;AAC1B,KAAA;AAEA,IAAA,IAAIvgB,IAAS,CAAA;IAEb,IAAI;MACF,IAAIwgB,WAAW,GAAG9e,MAAM,CAAC4F,OAAO,CAACiC,GAAG,CAAC,cAAc,CAAC,CAAA;AACpD;AACA;MACA,IAAIiX,WAAW,IAAI,uBAAuB,CAAC3d,IAAI,CAAC2d,WAAW,CAAC,EAAE;AAC5D,QAAA,IAAI9e,MAAM,CAACmb,IAAI,IAAI,IAAI,EAAE;AACvB7c,UAAAA,IAAI,GAAG,IAAI,CAAA;AACb,SAAC,MAAM;AACLA,UAAAA,IAAI,GAAG,MAAM0B,MAAM,CAACwF,IAAI,EAAE,CAAA;AAC5B,SAAA;AACF,OAAC,MAAM;AACLlH,QAAAA,IAAI,GAAG,MAAM0B,MAAM,CAACsK,IAAI,EAAE,CAAA;AAC5B,OAAA;KACD,CAAC,OAAO1P,CAAC,EAAE;MACV,OAAO;QAAE2Y,IAAI,EAAEjX,UAAU,CAACP,KAAK;AAAEA,QAAAA,KAAK,EAAEnB,CAAAA;OAAG,CAAA;AAC7C,KAAA;AAEA,IAAA,IAAI0jB,UAAU,KAAKhiB,UAAU,CAACP,KAAK,EAAE;MACnC,OAAO;AACLwX,QAAAA,IAAI,EAAE+K,UAAU;QAChBviB,KAAK,EAAE,IAAIwN,iBAAiB,CAAC5D,MAAM,EAAE3F,MAAM,CAACwJ,UAAU,EAAElL,IAAI,CAAC;QAC7DsH,OAAO,EAAE5F,MAAM,CAAC4F,OAAAA;OACjB,CAAA;AACH,KAAA;IAEA,OAAO;MACL2N,IAAI,EAAEjX,UAAU,CAACgC,IAAI;MACrBA,IAAI;MACJ+a,UAAU,EAAErZ,MAAM,CAAC2F,MAAM;MACzBC,OAAO,EAAE5F,MAAM,CAAC4F,OAAAA;KACjB,CAAA;AACH,GAAA;AAEA,EAAA,IAAI0Y,UAAU,KAAKhiB,UAAU,CAACP,KAAK,EAAE;IACnC,OAAO;AAAEwX,MAAAA,IAAI,EAAE+K,UAAU;AAAEviB,MAAAA,KAAK,EAAEiE,MAAAA;KAAQ,CAAA;AAC5C,GAAA;AAEA,EAAA,IAAI+e,cAAc,CAAC/e,MAAM,CAAC,EAAE;IAAA,IAAAgf,YAAA,EAAAC,aAAA,CAAA;IAC1B,OAAO;MACL1L,IAAI,EAAEjX,UAAU,CAAC4iB,QAAQ;AACzB9J,MAAAA,YAAY,EAAEpV,MAAM;MACpBqZ,UAAU,EAAA,CAAA2F,YAAA,GAAEhf,MAAM,CAACyF,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAXuZ,YAAA,CAAarZ,MAAM;AAC/BC,MAAAA,OAAO,EAAE,CAAAqZ,CAAAA,aAAA,GAAAjf,MAAM,CAACyF,IAAI,KAAXwZ,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,aAAA,CAAarZ,OAAO,KAAI,IAAIC,OAAO,CAAC7F,MAAM,CAACyF,IAAI,CAACG,OAAO,CAAA;KACjE,CAAA;AACH,GAAA;EAEA,OAAO;IAAE2N,IAAI,EAAEjX,UAAU,CAACgC,IAAI;AAAEA,IAAAA,IAAI,EAAE0B,MAAAA;GAAQ,CAAA;AAChD,CAAA;;AAEA;AACA;AACA;AACA,SAASyS,uBAAuBA,CAC9B7a,OAAgB,EAChBT,QAA2B,EAC3B8P,MAAmB,EACnBuK,UAAuB,EACd;AACT,EAAA,IAAIxX,GAAG,GAAGpC,OAAO,CAACC,SAAS,CAACgkB,iBAAiB,CAAC1kB,QAAQ,CAAC,CAAC,CAAC4D,QAAQ,EAAE,CAAA;AACnE,EAAA,IAAI0K,IAAiB,GAAG;AAAEwB,IAAAA,MAAAA;GAAQ,CAAA;EAElC,IAAIuK,UAAU,IAAIZ,gBAAgB,CAACY,UAAU,CAACtH,UAAU,CAAC,EAAE;IACzD,IAAI;MAAEA,UAAU;AAAEE,MAAAA,WAAAA;AAAY,KAAC,GAAGoH,UAAU,CAAA;AAC5C;AACA;AACA;AACA/L,IAAAA,IAAI,CAAC+N,MAAM,GAAGtJ,UAAU,CAAC0R,WAAW,EAAE,CAAA;IAEtC,IAAIxR,WAAW,KAAK,kBAAkB,EAAE;AACtC3E,MAAAA,IAAI,CAACG,OAAO,GAAG,IAAIC,OAAO,CAAC;AAAE,QAAA,cAAc,EAAEuE,WAAAA;AAAY,OAAC,CAAC,CAAA;MAC3D3E,IAAI,CAAC0V,IAAI,GAAG3jB,IAAI,CAACC,SAAS,CAAC+Z,UAAU,CAAChM,IAAI,CAAC,CAAA;AAC7C,KAAC,MAAM,IAAI4E,WAAW,KAAK,YAAY,EAAE;AACvC;AACA3E,MAAAA,IAAI,CAAC0V,IAAI,GAAG3J,UAAU,CAAClH,IAAI,CAAA;KAC5B,MAAM,IACLF,WAAW,KAAK,mCAAmC,IACnDoH,UAAU,CAACnH,QAAQ,EACnB;AACA;MACA5E,IAAI,CAAC0V,IAAI,GAAGe,6BAA6B,CAAC1K,UAAU,CAACnH,QAAQ,CAAC,CAAA;AAChE,KAAC,MAAM;AACL;AACA5E,MAAAA,IAAI,CAAC0V,IAAI,GAAG3J,UAAU,CAACnH,QAAQ,CAAA;AACjC,KAAA;AACF,GAAA;AAEA,EAAA,OAAO,IAAI4I,OAAO,CAACjZ,GAAG,EAAEyL,IAAI,CAAC,CAAA;AAC/B,CAAA;AAEA,SAASyW,6BAA6BA,CAAC7R,QAAkB,EAAmB;AAC1E,EAAA,IAAI4R,YAAY,GAAG,IAAIF,eAAe,EAAE,CAAA;AAExC,EAAA,KAAK,IAAI,CAAC7kB,GAAG,EAAEoD,KAAK,CAAC,IAAI+P,QAAQ,CAACrU,OAAO,EAAE,EAAE;AAC3C;AACAimB,IAAAA,YAAY,CAACG,MAAM,CAACllB,GAAG,EAAE,OAAOoD,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC2B,IAAI,CAAC,CAAA;AAC1E,GAAA;AAEA,EAAA,OAAOggB,YAAY,CAAA;AACrB,CAAA;AAEA,SAASE,6BAA6BA,CACpCF,YAA6B,EACnB;AACV,EAAA,IAAI5R,QAAQ,GAAG,IAAIyR,QAAQ,EAAE,CAAA;AAC7B,EAAA,KAAK,IAAI,CAAC5kB,GAAG,EAAEoD,KAAK,CAAC,IAAI2hB,YAAY,CAACjmB,OAAO,EAAE,EAAE;AAC/CqU,IAAAA,QAAQ,CAAC+R,MAAM,CAACllB,GAAG,EAAEoD,KAAK,CAAC,CAAA;AAC7B,GAAA;AACA,EAAA,OAAO+P,QAAQ,CAAA;AACjB,CAAA;AAEA,SAASsQ,sBAAsBA,CAC7B9c,OAAiC,EACjCqW,aAAuC,EACvCW,OAAqB,EACrBlD,YAAmC,EACnC/C,eAA0C,EAM1C;AACA;EACA,IAAIxQ,UAAqC,GAAG,EAAE,CAAA;EAC9C,IAAIyO,MAAoC,GAAG,IAAI,CAAA;AAC/C,EAAA,IAAIwM,UAA8B,CAAA;EAClC,IAAI8F,UAAU,GAAG,KAAK,CAAA;EACtB,IAAI7F,aAAsC,GAAG,EAAE,CAAA;;AAE/C;AACAzE,EAAAA,OAAO,CAAC1V,OAAO,CAAC,CAACa,MAAM,EAAE7J,KAAK,KAAK;IACjC,IAAI8G,EAAE,GAAGiX,aAAa,CAAC/d,KAAK,CAAC,CAACuG,KAAK,CAACO,EAAE,CAAA;IACtC5C,SAAS,CACP,CAACsZ,gBAAgB,CAAC3T,MAAM,CAAC,EACzB,qDACF,CAAC,CAAA;AACD,IAAA,IAAI6T,aAAa,CAAC7T,MAAM,CAAC,EAAE;AACzB;AACA;AACA,MAAA,IAAI8T,aAAa,GAAGnB,mBAAmB,CAAC9U,OAAO,EAAEZ,EAAE,CAAC,CAAA;AACpD,MAAA,IAAIlB,KAAK,GAAGiE,MAAM,CAACjE,KAAK,CAAA;AACxB;AACA;AACA;AACA,MAAA,IAAI4V,YAAY,EAAE;QAChB5V,KAAK,GAAG6F,MAAM,CAACiY,MAAM,CAAClI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;AACtCA,QAAAA,YAAY,GAAGrb,SAAS,CAAA;AAC1B,OAAA;AAEAuW,MAAAA,MAAM,GAAGA,MAAM,IAAI,EAAE,CAAA;;AAErB;MACA,IAAIA,MAAM,CAACiH,aAAa,CAACpX,KAAK,CAACO,EAAE,CAAC,IAAI,IAAI,EAAE;QAC1C4P,MAAM,CAACiH,aAAa,CAACpX,KAAK,CAACO,EAAE,CAAC,GAAGlB,KAAK,CAAA;AACxC,OAAA;;AAEA;AACAqC,MAAAA,UAAU,CAACnB,EAAE,CAAC,GAAG3G,SAAS,CAAA;;AAE1B;AACA;MACA,IAAI,CAAC6oB,UAAU,EAAE;AACfA,QAAAA,UAAU,GAAG,IAAI,CAAA;AACjB9F,QAAAA,UAAU,GAAG3P,oBAAoB,CAAC1J,MAAM,CAACjE,KAAK,CAAC,GAC3CiE,MAAM,CAACjE,KAAK,CAAC4J,MAAM,GACnB,GAAG,CAAA;AACT,OAAA;MACA,IAAI3F,MAAM,CAAC4F,OAAO,EAAE;AAClB0T,QAAAA,aAAa,CAACrc,EAAE,CAAC,GAAG+C,MAAM,CAAC4F,OAAO,CAAA;AACpC,OAAA;AACF,KAAC,MAAM;AACL,MAAA,IAAImO,gBAAgB,CAAC/T,MAAM,CAAC,EAAE;QAC5B4O,eAAe,CAAC7I,GAAG,CAAC9I,EAAE,EAAE+C,MAAM,CAACoV,YAAY,CAAC,CAAA;QAC5ChX,UAAU,CAACnB,EAAE,CAAC,GAAG+C,MAAM,CAACoV,YAAY,CAAC9W,IAAI,CAAA;AAC3C,OAAC,MAAM;AACLF,QAAAA,UAAU,CAACnB,EAAE,CAAC,GAAG+C,MAAM,CAAC1B,IAAI,CAAA;AAC9B,OAAA;;AAEA;AACA;AACA,MAAA,IACE0B,MAAM,CAACqZ,UAAU,IAAI,IAAI,IACzBrZ,MAAM,CAACqZ,UAAU,KAAK,GAAG,IACzB,CAAC8F,UAAU,EACX;QACA9F,UAAU,GAAGrZ,MAAM,CAACqZ,UAAU,CAAA;AAChC,OAAA;MACA,IAAIrZ,MAAM,CAAC4F,OAAO,EAAE;AAClB0T,QAAAA,aAAa,CAACrc,EAAE,CAAC,GAAG+C,MAAM,CAAC4F,OAAO,CAAA;AACpC,OAAA;AACF,KAAA;AACF,GAAC,CAAC,CAAA;;AAEF;AACA;AACA;AACA,EAAA,IAAI+L,YAAY,EAAE;AAChB9E,IAAAA,MAAM,GAAG8E,YAAY,CAAA;AACrBvT,IAAAA,UAAU,CAACwD,MAAM,CAACkP,IAAI,CAACa,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGrb,SAAS,CAAA;AACtD,GAAA;EAEA,OAAO;IACL8H,UAAU;IACVyO,MAAM;IACNwM,UAAU,EAAEA,UAAU,IAAI,GAAG;AAC7BC,IAAAA,aAAAA;GACD,CAAA;AACH,CAAA;AAEA,SAASnE,iBAAiBA,CACxB9e,KAAkB,EAClBwH,OAAiC,EACjCqW,aAAuC,EACvCW,OAAqB,EACrBlD,YAAmC,EACnCwC,oBAA2C,EAC3CY,cAA4B,EAC5BnG,eAA0C,EAI1C;EACA,IAAI;IAAExQ,UAAU;AAAEyO,IAAAA,MAAAA;AAAO,GAAC,GAAG8N,sBAAsB,CACjD9c,OAAO,EACPqW,aAAa,EACbW,OAAO,EACPlD,YAAY,EACZ/C,eACF,CAAC,CAAA;;AAED;AACA,EAAA,KAAK,IAAIzY,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGge,oBAAoB,CAAC3d,MAAM,EAAEL,KAAK,EAAE,EAAE;IAChE,IAAI;MAAEe,GAAG;MAAEiH,KAAK;AAAE0I,MAAAA,UAAAA;AAAW,KAAC,GAAGsN,oBAAoB,CAAChe,KAAK,CAAC,CAAA;AAC5DkE,IAAAA,SAAS,CACP0a,cAAc,KAAKze,SAAS,IAAIye,cAAc,CAAC5e,KAAK,CAAC,KAAKG,SAAS,EACnE,2CACF,CAAC,CAAA;AACD,IAAA,IAAI0J,MAAM,GAAG+U,cAAc,CAAC5e,KAAK,CAAC,CAAA;;AAElC;AACA,IAAA,IAAI0Q,UAAU,IAAIA,UAAU,CAACI,MAAM,CAACa,OAAO,EAAE;AAC3C;AACA,MAAA,SAAA;AACF,KAAC,MAAM,IAAI+L,aAAa,CAAC7T,MAAM,CAAC,EAAE;AAChC,MAAA,IAAI8T,aAAa,GAAGnB,mBAAmB,CAACtc,KAAK,CAACwH,OAAO,EAAEM,KAAK,oBAALA,KAAK,CAAEzB,KAAK,CAACO,EAAE,CAAC,CAAA;AACvE,MAAA,IAAI,EAAE4P,MAAM,IAAIA,MAAM,CAACiH,aAAa,CAACpX,KAAK,CAACO,EAAE,CAAC,CAAC,EAAE;QAC/C4P,MAAM,GAAA1R,QAAA,CAAA,EAAA,EACD0R,MAAM,EAAA;AACT,UAAA,CAACiH,aAAa,CAACpX,KAAK,CAACO,EAAE,GAAG+C,MAAM,CAACjE,KAAAA;SAClC,CAAA,CAAA;AACH,OAAA;AACA1F,MAAAA,KAAK,CAACiX,QAAQ,CAACvF,MAAM,CAAC7Q,GAAG,CAAC,CAAA;AAC5B,KAAC,MAAM,IAAIyc,gBAAgB,CAAC3T,MAAM,CAAC,EAAE;AACnC;AACA;AACA3F,MAAAA,SAAS,CAAC,KAAK,EAAE,yCAAyC,CAAC,CAAA;AAC7D,KAAC,MAAM,IAAI0Z,gBAAgB,CAAC/T,MAAM,CAAC,EAAE;AACnC;AACA;AACA3F,MAAAA,SAAS,CAAC,KAAK,EAAE,iCAAiC,CAAC,CAAA;AACrD,KAAC,MAAM;AACL,MAAA,IAAIoc,WAAW,GAAGL,cAAc,CAACpW,MAAM,CAAC1B,IAAI,CAAC,CAAA;MAC7CjI,KAAK,CAACiX,QAAQ,CAACvH,GAAG,CAAC7O,GAAG,EAAEuf,WAAW,CAAC,CAAA;AACtC,KAAA;AACF,GAAA;EAEA,OAAO;IAAErY,UAAU;AAAEyO,IAAAA,MAAAA;GAAQ,CAAA;AAC/B,CAAA;AAEA,SAASkE,eAAeA,CACtB3S,UAAqB,EACrBghB,aAAwB,EACxBvhB,OAAiC,EACjCgP,MAAoC,EACzB;AACX,EAAA,IAAIwS,gBAAgB,GAAAlkB,QAAA,CAAA,EAAA,EAAQikB,aAAa,CAAE,CAAA;AAC3C,EAAA,KAAK,IAAIjhB,KAAK,IAAIN,OAAO,EAAE;AACzB,IAAA,IAAIZ,EAAE,GAAGkB,KAAK,CAACzB,KAAK,CAACO,EAAE,CAAA;AACvB,IAAA,IAAImiB,aAAa,CAACE,cAAc,CAACriB,EAAE,CAAC,EAAE;AACpC,MAAA,IAAImiB,aAAa,CAACniB,EAAE,CAAC,KAAK3G,SAAS,EAAE;AACnC+oB,QAAAA,gBAAgB,CAACpiB,EAAE,CAAC,GAAGmiB,aAAa,CAACniB,EAAE,CAAC,CAAA;AAC1C,OAGE;AAEJ,KAAC,MAAM,IAAImB,UAAU,CAACnB,EAAE,CAAC,KAAK3G,SAAS,IAAI6H,KAAK,CAACzB,KAAK,CAACkQ,MAAM,EAAE;AAC7D;AACA;AACAyS,MAAAA,gBAAgB,CAACpiB,EAAE,CAAC,GAAGmB,UAAU,CAACnB,EAAE,CAAC,CAAA;AACvC,KAAA;IAEA,IAAI4P,MAAM,IAAIA,MAAM,CAACyS,cAAc,CAACriB,EAAE,CAAC,EAAE;AACvC;AACA,MAAA,MAAA;AACF,KAAA;AACF,GAAA;AACA,EAAA,OAAOoiB,gBAAgB,CAAA;AACzB,CAAA;;AAEA;AACA;AACA;AACA,SAAS1M,mBAAmBA,CAC1B9U,OAAiC,EACjC4V,OAAgB,EACQ;AACxB,EAAA,IAAI8L,eAAe,GAAG9L,OAAO,GACzB5V,OAAO,CAAC1D,KAAK,CAAC,CAAC,EAAE0D,OAAO,CAAC0e,SAAS,CAAE9P,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACO,EAAE,KAAKwW,OAAO,CAAC,GAAG,CAAC,CAAC,GACtE,CAAC,GAAG5V,OAAO,CAAC,CAAA;EAChB,OACE0hB,eAAe,CAACC,OAAO,EAAE,CAAC5F,IAAI,CAAEnN,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACmO,gBAAgB,KAAK,IAAI,CAAC,IACxEhN,OAAO,CAAC,CAAC,CAAC,CAAA;AAEd,CAAA;AAEA,SAASyO,sBAAsBA,CAAC1P,MAAiC,EAG/D;AACA;AACA,EAAA,IAAIF,KAAK,GACPE,MAAM,CAACpG,MAAM,KAAK,CAAC,GACfoG,MAAM,CAAC,CAAC,CAAC,GACTA,MAAM,CAACgd,IAAI,CAAEhT,CAAC,IAAKA,CAAC,CAACzQ,KAAK,IAAI,CAACyQ,CAAC,CAAC5O,IAAI,IAAI4O,CAAC,CAAC5O,IAAI,KAAK,GAAG,CAAC,IAAI;IAC1DiF,EAAE,EAAA,sBAAA;GACH,CAAA;EAEP,OAAO;AACLY,IAAAA,OAAO,EAAE,CACP;MACEQ,MAAM,EAAE,EAAE;AACVhH,MAAAA,QAAQ,EAAE,EAAE;AACZwK,MAAAA,YAAY,EAAE,EAAE;AAChBnF,MAAAA,KAAAA;AACF,KAAC,CACF;AACDA,IAAAA,KAAAA;GACD,CAAA;AACH,CAAA;AAEA,SAAS2P,sBAAsBA,CAC7B1G,MAAc,EAAA8Z,MAAA,EAYd;EAAA,IAXA;IACEpoB,QAAQ;IACRoc,OAAO;IACPD,MAAM;AACND,IAAAA,IAAAA;AAMF,GAAC,GAAAkM,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;EAEN,IAAIjW,UAAU,GAAG,sBAAsB,CAAA;EACvC,IAAIkW,YAAY,GAAG,iCAAiC,CAAA;EAEpD,IAAI/Z,MAAM,KAAK,GAAG,EAAE;AAClB6D,IAAAA,UAAU,GAAG,aAAa,CAAA;AAC1B,IAAA,IAAIgK,MAAM,IAAInc,QAAQ,IAAIoc,OAAO,EAAE;MACjCiM,YAAY,GACV,gBAAclM,MAAM,GAAA,gBAAA,GAAgBnc,QAAQ,GACDoc,SAAAA,IAAAA,yCAAAA,GAAAA,OAAO,UAAK,GACZ,2CAAA,CAAA;AAC/C,KAAC,MAAM,IAAIF,IAAI,KAAK,cAAc,EAAE;AAClCmM,MAAAA,YAAY,GAAG,qCAAqC,CAAA;AACtD,KAAC,MAAM,IAAInM,IAAI,KAAK,cAAc,EAAE;AAClCmM,MAAAA,YAAY,GAAG,kCAAkC,CAAA;AACnD,KAAA;AACF,GAAC,MAAM,IAAI/Z,MAAM,KAAK,GAAG,EAAE;AACzB6D,IAAAA,UAAU,GAAG,WAAW,CAAA;AACxBkW,IAAAA,YAAY,GAAajM,UAAAA,GAAAA,OAAO,GAAyBpc,0BAAAA,GAAAA,QAAQ,GAAG,IAAA,CAAA;AACtE,GAAC,MAAM,IAAIsO,MAAM,KAAK,GAAG,EAAE;AACzB6D,IAAAA,UAAU,GAAG,WAAW,CAAA;IACxBkW,YAAY,GAAA,yBAAA,GAA4BroB,QAAQ,GAAG,IAAA,CAAA;AACrD,GAAC,MAAM,IAAIsO,MAAM,KAAK,GAAG,EAAE;AACzB6D,IAAAA,UAAU,GAAG,oBAAoB,CAAA;AACjC,IAAA,IAAIgK,MAAM,IAAInc,QAAQ,IAAIoc,OAAO,EAAE;AACjCiM,MAAAA,YAAY,GACV,aAAA,GAAclM,MAAM,CAACoI,WAAW,EAAE,GAAA,gBAAA,GAAgBvkB,QAAQ,GAAA,SAAA,IAAA,0CAAA,GACdoc,OAAO,GAAA,MAAA,CAAK,GACb,2CAAA,CAAA;KAC9C,MAAM,IAAID,MAAM,EAAE;AACjBkM,MAAAA,YAAY,iCAA8BlM,MAAM,CAACoI,WAAW,EAAE,GAAG,IAAA,CAAA;AACnE,KAAA;AACF,GAAA;AAEA,EAAA,OAAO,IAAIrS,iBAAiB,CAC1B5D,MAAM,IAAI,GAAG,EACb6D,UAAU,EACV,IAAIhP,KAAK,CAACklB,YAAY,CAAC,EACvB,IACF,CAAC,CAAA;AACH,CAAA;;AAEA;AACA,SAASzK,YAAYA,CACnBJ,OAAqB,EACgC;AACrD,EAAA,KAAK,IAAI/W,CAAC,GAAG+W,OAAO,CAACre,MAAM,GAAG,CAAC,EAAEsH,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AAC5C,IAAA,IAAIkC,MAAM,GAAG6U,OAAO,CAAC/W,CAAC,CAAC,CAAA;AACvB,IAAA,IAAI6V,gBAAgB,CAAC3T,MAAM,CAAC,EAAE;MAC5B,OAAO;QAAEA,MAAM;AAAE/E,QAAAA,GAAG,EAAE6C,CAAAA;OAAG,CAAA;AAC3B,KAAA;AACF,GAAA;AACF,CAAA;AAEA,SAAS+d,iBAAiBA,CAAC7jB,IAAQ,EAAE;AACnC,EAAA,IAAIqD,UAAU,GAAG,OAAOrD,IAAI,KAAK,QAAQ,GAAGC,SAAS,CAACD,IAAI,CAAC,GAAGA,IAAI,CAAA;AAClE,EAAA,OAAOL,UAAU,CAAAwD,QAAA,CAAA,EAAA,EAAME,UAAU,EAAA;AAAElD,IAAAA,IAAI,EAAE,EAAA;AAAE,GAAA,CAAE,CAAC,CAAA;AAChD,CAAA;AAEA,SAASoa,gBAAgBA,CAACpS,CAAW,EAAEC,CAAW,EAAW;AAC3D,EAAA,IAAID,CAAC,CAAC9I,QAAQ,KAAK+I,CAAC,CAAC/I,QAAQ,IAAI8I,CAAC,CAACjI,MAAM,KAAKkI,CAAC,CAAClI,MAAM,EAAE;AACtD,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AAEA,EAAA,IAAIiI,CAAC,CAAChI,IAAI,KAAK,EAAE,EAAE;AACjB;AACA,IAAA,OAAOiI,CAAC,CAACjI,IAAI,KAAK,EAAE,CAAA;GACrB,MAAM,IAAIgI,CAAC,CAAChI,IAAI,KAAKiI,CAAC,CAACjI,IAAI,EAAE;AAC5B;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAC,MAAM,IAAIiI,CAAC,CAACjI,IAAI,KAAK,EAAE,EAAE;AACxB;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACA;AACA,EAAA,OAAO,KAAK,CAAA;AACd,CAAA;AAEA,SAAS4b,gBAAgBA,CAAC/T,MAAkB,EAA4B;AACtE,EAAA,OAAOA,MAAM,CAACuT,IAAI,KAAKjX,UAAU,CAAC4iB,QAAQ,CAAA;AAC5C,CAAA;AAEA,SAASrL,aAAaA,CAAC7T,MAAkB,EAAyB;AAChE,EAAA,OAAOA,MAAM,CAACuT,IAAI,KAAKjX,UAAU,CAACP,KAAK,CAAA;AACzC,CAAA;AAEA,SAAS4X,gBAAgBA,CAAC3T,MAAmB,EAA4B;EACvE,OAAO,CAACA,MAAM,IAAIA,MAAM,CAACuT,IAAI,MAAMjX,UAAU,CAAC8M,QAAQ,CAAA;AACxD,CAAA;AAEO,SAAS2V,cAAcA,CAACzkB,KAAU,EAAyB;EAChE,IAAI4kB,QAAsB,GAAG5kB,KAAK,CAAA;AAClC,EAAA,OACE4kB,QAAQ,IACR,OAAOA,QAAQ,KAAK,QAAQ,IAC5B,OAAOA,QAAQ,CAAC5gB,IAAI,KAAK,QAAQ,IACjC,OAAO4gB,QAAQ,CAAC9W,SAAS,KAAK,UAAU,IACxC,OAAO8W,QAAQ,CAAC7W,MAAM,KAAK,UAAU,IACrC,OAAO6W,QAAQ,CAAC1W,WAAW,KAAK,UAAU,CAAA;AAE9C,CAAA;AAEA,SAASiR,UAAUA,CAACnf,KAAU,EAAqB;AACjD,EAAA,OACEA,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,CAACqL,MAAM,KAAK,QAAQ,IAChC,OAAOrL,KAAK,CAACkP,UAAU,KAAK,QAAQ,IACpC,OAAOlP,KAAK,CAACsL,OAAO,KAAK,QAAQ,IACjC,OAAOtL,KAAK,CAAC6gB,IAAI,KAAK,WAAW,CAAA;AAErC,CAAA;AAEA,SAAShB,kBAAkBA,CAACna,MAAW,EAAsB;AAC3D,EAAA,IAAI,CAACyZ,UAAU,CAACzZ,MAAM,CAAC,EAAE;AACvB,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AAEA,EAAA,IAAI2F,MAAM,GAAG3F,MAAM,CAAC2F,MAAM,CAAA;EAC1B,IAAIxO,QAAQ,GAAG6I,MAAM,CAAC4F,OAAO,CAACiC,GAAG,CAAC,UAAU,CAAC,CAAA;EAC7C,OAAOlC,MAAM,IAAI,GAAG,IAAIA,MAAM,IAAI,GAAG,IAAIxO,QAAQ,IAAI,IAAI,CAAA;AAC3D,CAAA;AAEA,SAAS+iB,oBAAoBA,CAACyF,GAAQ,EAA6B;EACjE,OACEA,GAAG,IACHlG,UAAU,CAACkG,GAAG,CAACrW,QAAQ,CAAC,KACvBqW,GAAG,CAACpM,IAAI,KAAKjX,UAAU,CAACgC,IAAI,IAAIqhB,GAAG,CAACpM,IAAI,KAAKjX,UAAU,CAACP,KAAK,CAAC,CAAA;AAEnE,CAAA;AAEA,SAASod,aAAaA,CAAC3F,MAAc,EAAwC;EAC3E,OAAO1J,mBAAmB,CAAChE,GAAG,CAAC0N,MAAM,CAACjQ,WAAW,EAAgB,CAAC,CAAA;AACpE,CAAA;AAEA,SAASqN,gBAAgBA,CACvB4C,MAAc,EACwC;EACtD,OAAO5J,oBAAoB,CAAC9D,GAAG,CAAC0N,MAAM,CAACjQ,WAAW,EAAwB,CAAC,CAAA;AAC7E,CAAA;AAEA,eAAe4T,sBAAsBA,CACnCH,cAAwC,EACxC9C,aAAgD,EAChDW,OAAqB,EACrB+K,OAA+B,EAC/BnE,SAAkB,EAClB8B,iBAA6B,EAC7B;AACA,EAAA,KAAK,IAAIpnB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG0e,OAAO,CAACre,MAAM,EAAEL,KAAK,EAAE,EAAE;AACnD,IAAA,IAAI6J,MAAM,GAAG6U,OAAO,CAAC1e,KAAK,CAAC,CAAA;AAC3B,IAAA,IAAIgI,KAAK,GAAG+V,aAAa,CAAC/d,KAAK,CAAC,CAAA;AAChC;AACA;AACA;IACA,IAAI,CAACgI,KAAK,EAAE;AACV,MAAA,SAAA;AACF,KAAA;AAEA,IAAA,IAAIqf,YAAY,GAAGxG,cAAc,CAAC4C,IAAI,CACnCnN,CAAC,IAAKA,CAAC,CAAC/P,KAAK,CAACO,EAAE,KAAKkB,KAAK,CAAEzB,KAAK,CAACO,EACrC,CAAC,CAAA;IACD,IAAI4iB,oBAAoB,GACtBrC,YAAY,IAAI,IAAI,IACpB,CAACL,kBAAkB,CAACK,YAAY,EAAErf,KAAK,CAAC,IACxC,CAACof,iBAAiB,IAAIA,iBAAiB,CAACpf,KAAK,CAACzB,KAAK,CAACO,EAAE,CAAC,MAAM3G,SAAS,CAAA;IAExE,IAAIyd,gBAAgB,CAAC/T,MAAM,CAAC,KAAKyb,SAAS,IAAIoE,oBAAoB,CAAC,EAAE;AACnE;AACA;AACA;AACA,MAAA,IAAI5Y,MAAM,GAAG2Y,OAAO,CAACzpB,KAAK,CAAC,CAAA;AAC3BkE,MAAAA,SAAS,CACP4M,MAAM,EACN,kEACF,CAAC,CAAA;AACD,MAAA,MAAMyP,mBAAmB,CAAC1W,MAAM,EAAEiH,MAAM,EAAEwU,SAAS,CAAC,CAAChU,IAAI,CAAEzH,MAAM,IAAK;AACpE,QAAA,IAAIA,MAAM,EAAE;UACV6U,OAAO,CAAC1e,KAAK,CAAC,GAAG6J,MAAM,IAAI6U,OAAO,CAAC1e,KAAK,CAAC,CAAA;AAC3C,SAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAA;AACF,GAAA;AACF,CAAA;AAEA,eAAeugB,mBAAmBA,CAChC1W,MAAsB,EACtBiH,MAAmB,EACnB6Y,MAAM,EAC4C;AAAA,EAAA,IADlDA,MAAM,KAAA,KAAA,CAAA,EAAA;AAANA,IAAAA,MAAM,GAAG,KAAK,CAAA;AAAA,GAAA;EAEd,IAAIhY,OAAO,GAAG,MAAM9H,MAAM,CAACoV,YAAY,CAAC5M,WAAW,CAACvB,MAAM,CAAC,CAAA;AAC3D,EAAA,IAAIa,OAAO,EAAE;AACX,IAAA,OAAA;AACF,GAAA;AAEA,EAAA,IAAIgY,MAAM,EAAE;IACV,IAAI;MACF,OAAO;QACLvM,IAAI,EAAEjX,UAAU,CAACgC,IAAI;AACrBA,QAAAA,IAAI,EAAE0B,MAAM,CAACoV,YAAY,CAACzM,aAAAA;OAC3B,CAAA;KACF,CAAC,OAAO/N,CAAC,EAAE;AACV;MACA,OAAO;QACL2Y,IAAI,EAAEjX,UAAU,CAACP,KAAK;AACtBA,QAAAA,KAAK,EAAEnB,CAAAA;OACR,CAAA;AACH,KAAA;AACF,GAAA;EAEA,OAAO;IACL2Y,IAAI,EAAEjX,UAAU,CAACgC,IAAI;AACrBA,IAAAA,IAAI,EAAE0B,MAAM,CAACoV,YAAY,CAAC9W,IAAAA;GAC3B,CAAA;AACH,CAAA;AAEA,SAASid,kBAAkBA,CAACrjB,MAAc,EAAW;AACnD,EAAA,OAAO,IAAI6jB,eAAe,CAAC7jB,MAAM,CAAC,CAAC6nB,MAAM,CAAC,OAAO,CAAC,CAAChf,IAAI,CAAEsC,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC,CAAA;AAC1E,CAAA;AAEA,SAASiQ,cAAcA,CACrBzV,OAAiC,EACjC1G,QAA2B,EAC3B;AACA,EAAA,IAAIe,MAAM,GACR,OAAOf,QAAQ,KAAK,QAAQ,GAAGc,SAAS,CAACd,QAAQ,CAAC,CAACe,MAAM,GAAGf,QAAQ,CAACe,MAAM,CAAA;AAC7E,EAAA,IACE2F,OAAO,CAACA,OAAO,CAACrH,MAAM,GAAG,CAAC,CAAC,CAACkG,KAAK,CAACvG,KAAK,IACvColB,kBAAkB,CAACrjB,MAAM,IAAI,EAAE,CAAC,EAChC;AACA;AACA,IAAA,OAAO2F,OAAO,CAACA,OAAO,CAACrH,MAAM,GAAG,CAAC,CAAC,CAAA;AACpC,GAAA;AACA;AACA;AACA,EAAA,IAAIiO,WAAW,GAAGH,0BAA0B,CAACzG,OAAO,CAAC,CAAA;AACrD,EAAA,OAAO4G,WAAW,CAACA,WAAW,CAACjO,MAAM,GAAG,CAAC,CAAC,CAAA;AAC5C,CAAA;AAEA,SAASyd,2BAA2BA,CAClChH,UAAsB,EACE;EACxB,IAAI;IAAE/C,UAAU;IAAEC,UAAU;IAAEC,WAAW;IAAEE,IAAI;IAAED,QAAQ;AAAE7E,IAAAA,IAAAA;AAAK,GAAC,GAC/DyH,UAAU,CAAA;EACZ,IAAI,CAAC/C,UAAU,IAAI,CAACC,UAAU,IAAI,CAACC,WAAW,EAAE;AAC9C,IAAA,OAAA;AACF,GAAA;EAEA,IAAIE,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO;MACLJ,UAAU;MACVC,UAAU;MACVC,WAAW;AACXC,MAAAA,QAAQ,EAAE/T,SAAS;AACnBkP,MAAAA,IAAI,EAAElP,SAAS;AACfgU,MAAAA,IAAAA;KACD,CAAA;AACH,GAAC,MAAM,IAAID,QAAQ,IAAI,IAAI,EAAE;IAC3B,OAAO;MACLH,UAAU;MACVC,UAAU;MACVC,WAAW;MACXC,QAAQ;AACR7E,MAAAA,IAAI,EAAElP,SAAS;AACfgU,MAAAA,IAAI,EAAEhU,SAAAA;KACP,CAAA;AACH,GAAC,MAAM,IAAIkP,IAAI,KAAKlP,SAAS,EAAE;IAC7B,OAAO;MACL4T,UAAU;MACVC,UAAU;MACVC,WAAW;AACXC,MAAAA,QAAQ,EAAE/T,SAAS;MACnBkP,IAAI;AACJ8E,MAAAA,IAAI,EAAEhU,SAAAA;KACP,CAAA;AACH,GAAA;AACF,CAAA;AAEA,SAAS0c,oBAAoBA,CAC3B7b,QAAkB,EAClBqa,UAAuB,EACM;AAC7B,EAAA,IAAIA,UAAU,EAAE;AACd,IAAA,IAAIvE,UAAuC,GAAG;AAC5C5W,MAAAA,KAAK,EAAE,SAAS;MAChBc,QAAQ;MACR+S,UAAU,EAAEsH,UAAU,CAACtH,UAAU;MACjCC,UAAU,EAAEqH,UAAU,CAACrH,UAAU;MACjCC,WAAW,EAAEoH,UAAU,CAACpH,WAAW;MACnCC,QAAQ,EAAEmH,UAAU,CAACnH,QAAQ;MAC7B7E,IAAI,EAAEgM,UAAU,CAAChM,IAAI;MACrB8E,IAAI,EAAEkH,UAAU,CAAClH,IAAAA;KAClB,CAAA;AACD,IAAA,OAAO2C,UAAU,CAAA;AACnB,GAAC,MAAM;AACL,IAAA,IAAIA,UAAuC,GAAG;AAC5C5W,MAAAA,KAAK,EAAE,SAAS;MAChBc,QAAQ;AACR+S,MAAAA,UAAU,EAAE5T,SAAS;AACrB6T,MAAAA,UAAU,EAAE7T,SAAS;AACrB8T,MAAAA,WAAW,EAAE9T,SAAS;AACtB+T,MAAAA,QAAQ,EAAE/T,SAAS;AACnBkP,MAAAA,IAAI,EAAElP,SAAS;AACfgU,MAAAA,IAAI,EAAEhU,SAAAA;KACP,CAAA;AACD,IAAA,OAAO2W,UAAU,CAAA;AACnB,GAAA;AACF,CAAA;AAEA,SAASmG,uBAAuBA,CAC9Bjc,QAAkB,EAClBqa,UAAsB,EACU;AAChC,EAAA,IAAIvE,UAA0C,GAAG;AAC/C5W,IAAAA,KAAK,EAAE,YAAY;IACnBc,QAAQ;IACR+S,UAAU,EAAEsH,UAAU,CAACtH,UAAU;IACjCC,UAAU,EAAEqH,UAAU,CAACrH,UAAU;IACjCC,WAAW,EAAEoH,UAAU,CAACpH,WAAW;IACnCC,QAAQ,EAAEmH,UAAU,CAACnH,QAAQ;IAC7B7E,IAAI,EAAEgM,UAAU,CAAChM,IAAI;IACrB8E,IAAI,EAAEkH,UAAU,CAAClH,IAAAA;GAClB,CAAA;AACD,EAAA,OAAO2C,UAAU,CAAA;AACnB,CAAA;AAEA,SAASwH,iBAAiBA,CACxBjD,UAAuB,EACvBlT,IAAsB,EACI;AAC1B,EAAA,IAAIkT,UAAU,EAAE;AACd,IAAA,IAAItB,OAAiC,GAAG;AACtC7Z,MAAAA,KAAK,EAAE,SAAS;MAChB6T,UAAU,EAAEsH,UAAU,CAACtH,UAAU;MACjCC,UAAU,EAAEqH,UAAU,CAACrH,UAAU;MACjCC,WAAW,EAAEoH,UAAU,CAACpH,WAAW;MACnCC,QAAQ,EAAEmH,UAAU,CAACnH,QAAQ;MAC7B7E,IAAI,EAAEgM,UAAU,CAAChM,IAAI;MACrB8E,IAAI,EAAEkH,UAAU,CAAClH,IAAI;AACrBhM,MAAAA,IAAAA;KACD,CAAA;AACD,IAAA,OAAO4R,OAAO,CAAA;AAChB,GAAC,MAAM;AACL,IAAA,IAAIA,OAAiC,GAAG;AACtC7Z,MAAAA,KAAK,EAAE,SAAS;AAChB6T,MAAAA,UAAU,EAAE5T,SAAS;AACrB6T,MAAAA,UAAU,EAAE7T,SAAS;AACrB8T,MAAAA,WAAW,EAAE9T,SAAS;AACtB+T,MAAAA,QAAQ,EAAE/T,SAAS;AACnBkP,MAAAA,IAAI,EAAElP,SAAS;AACfgU,MAAAA,IAAI,EAAEhU,SAAS;AACfgI,MAAAA,IAAAA;KACD,CAAA;AACD,IAAA,OAAO4R,OAAO,CAAA;AAChB,GAAA;AACF,CAAA;AAEA,SAAS6F,oBAAoBA,CAC3BvE,UAAsB,EACtBqE,eAAyB,EACI;AAC7B,EAAA,IAAI3F,OAAoC,GAAG;AACzC7Z,IAAAA,KAAK,EAAE,YAAY;IACnB6T,UAAU,EAAEsH,UAAU,CAACtH,UAAU;IACjCC,UAAU,EAAEqH,UAAU,CAACrH,UAAU;IACjCC,WAAW,EAAEoH,UAAU,CAACpH,WAAW;IACnCC,QAAQ,EAAEmH,UAAU,CAACnH,QAAQ;IAC7B7E,IAAI,EAAEgM,UAAU,CAAChM,IAAI;IACrB8E,IAAI,EAAEkH,UAAU,CAAClH,IAAI;AACrBhM,IAAAA,IAAI,EAAEuX,eAAe,GAAGA,eAAe,CAACvX,IAAI,GAAGhI,SAAAA;GAChD,CAAA;AACD,EAAA,OAAO4Z,OAAO,CAAA;AAChB,CAAA;AAEA,SAASkG,cAAcA,CAAC9X,IAAqB,EAAyB;AACpE,EAAA,IAAI4R,OAA8B,GAAG;AACnC7Z,IAAAA,KAAK,EAAE,MAAM;AACb6T,IAAAA,UAAU,EAAE5T,SAAS;AACrB6T,IAAAA,UAAU,EAAE7T,SAAS;AACrB8T,IAAAA,WAAW,EAAE9T,SAAS;AACtB+T,IAAAA,QAAQ,EAAE/T,SAAS;AACnBkP,IAAAA,IAAI,EAAElP,SAAS;AACfgU,IAAAA,IAAI,EAAEhU,SAAS;AACfgI,IAAAA,IAAAA;GACD,CAAA;AACD,EAAA,OAAO4R,OAAO,CAAA;AAChB,CAAA;AAEA,SAASZ,yBAAyBA,CAChC0Q,OAAe,EACfC,WAAqC,EACrC;EACA,IAAI;IACF,IAAIC,gBAAgB,GAAGF,OAAO,CAACG,cAAc,CAACC,OAAO,CACnDrV,uBACF,CAAC,CAAA;AACD,IAAA,IAAImV,gBAAgB,EAAE;AACpB,MAAA,IAAI1a,IAAI,GAAGhO,IAAI,CAACwkB,KAAK,CAACkE,gBAAgB,CAAC,CAAA;AACvC,MAAA,KAAK,IAAI,CAAC3X,CAAC,EAAElF,CAAC,CAAC,IAAIzB,MAAM,CAAC5L,OAAO,CAACwP,IAAI,IAAI,EAAE,CAAC,EAAE;QAC7C,IAAInC,CAAC,IAAIkD,KAAK,CAACC,OAAO,CAACnD,CAAC,CAAC,EAAE;AACzB4c,UAAAA,WAAW,CAACla,GAAG,CAACwC,CAAC,EAAE,IAAI/L,GAAG,CAAC6G,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;AACtC,SAAA;AACF,OAAA;AACF,KAAA;GACD,CAAC,OAAOzI,CAAC,EAAE;AACV;AAAA,GAAA;AAEJ,CAAA;AAEA,SAAS4U,yBAAyBA,CAChCwQ,OAAe,EACfC,WAAqC,EACrC;AACA,EAAA,IAAIA,WAAW,CAACvX,IAAI,GAAG,CAAC,EAAE;IACxB,IAAIlD,IAA8B,GAAG,EAAE,CAAA;IACvC,KAAK,IAAI,CAAC+C,CAAC,EAAElF,CAAC,CAAC,IAAI4c,WAAW,EAAE;AAC9Bza,MAAAA,IAAI,CAAC+C,CAAC,CAAC,GAAG,CAAC,GAAGlF,CAAC,CAAC,CAAA;AAClB,KAAA;IACA,IAAI;AACF2c,MAAAA,OAAO,CAACG,cAAc,CAACE,OAAO,CAC5BtV,uBAAuB,EACvBvT,IAAI,CAACC,SAAS,CAAC+N,IAAI,CACrB,CAAC,CAAA;KACF,CAAC,OAAOzJ,KAAK,EAAE;AACdzE,MAAAA,OAAO,CACL,KAAK,EACyDyE,6DAAAA,GAAAA,KAAK,OACrE,CAAC,CAAA;AACH,KAAA;AACF,GAAA;AACF,CAAA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}