/**
 * React Router DOM v6.22.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
import*as e from"react";import*as t from"react-dom";import{UNSAFE_mapRouteProperties as n,UNSAFE_DataRouterContext as r,UNSAFE_DataRouterStateContext as a,Router as o,UNSAFE_useRoutesImpl as i,UNSAFE_NavigationContext as s,useHref as u,useResolvedPath as l,useLocation as c,useNavigate as f,createPath as d,UNSAFE_useRouteId as m,UNSAFE_RouteContext as h,useMatches as p,useNavigation as w,useBlocker as v}from"react-router";export{AbortedDeferredError,Await,MemoryRouter,Navigate,NavigationType,Outlet,Route,Router,Routes,UNSAFE_DataRouterContext,UNSAFE_DataRouterStateContext,UNSAFE_LocationContext,UNSAFE_NavigationContext,UNSAFE_RouteContext,UNSAFE_useRouteId,createMemoryRouter,createPath,createRoutesFromChildren,createRoutesFromElements,defer,generatePath,isRouteErrorResponse,json,matchPath,matchRoutes,parsePath,redirect,redirectDocument,renderMatches,resolvePath,useActionData,useAsyncError,useAsyncValue,useBlocker,useHref,useInRouterContext,useLoaderData,useLocation,useMatch,useMatches,useNavigate,useNavigation,useNavigationType,useOutlet,useOutletContext,useParams,useResolvedPath,useRevalidator,useRouteError,useRouteLoaderData,useRoutes}from"react-router";import{stripBasename as g,createRouter as y,createBrowserHistory as b,createHashHistory as S,UNSAFE_ErrorResponseImpl as R,UNSAFE_invariant as E,joinPaths as T,IDLE_FETCHER as _,matchPath as L}from"@remix-run/router";const C="application/x-www-form-urlencoded";function x(e){return null!=e&&"string"==typeof e.tagName}function A(e=""){return new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map((e=>[n,e])):[[n,r]])}),[]))}let F=null;const U=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function k(e){return null==e||U.has(e)?e:null}function P(e,t){let n,r,a,o,i;if(x(s=e)&&"form"===s.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?g(i,t):null,n=e.getAttribute("method")||"get",a=k(e.getAttribute("enctype"))||C,o=new FormData(e)}else if(function(e){return x(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return x(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let s=e.getAttribute("formaction")||i.getAttribute("action");if(r=s?g(s,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||"get",a=k(e.getAttribute("formenctype"))||k(i.getAttribute("enctype"))||C,o=new FormData(i,e),!function(){if(null===F)try{new FormData(document.createElement("form"),0),F=!1}catch(e){F=!0}return F}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,r)}}else{if(x(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n="get",r=null,a=C,i=e}var s;return o&&"text/plain"===a&&(i=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:i}}try{window.__reactRouterVersion="6"}catch(ye){}function D(e,t){return y({basename:t?.basename,future:{...t?.future,v7_prependBasename:!0},history:b({window:t?.window}),hydrationData:t?.hydrationData||K(),routes:e,mapRouteProperties:n,window:t?.window}).initialize()}function N(e,t){return y({basename:t?.basename,future:{...t?.future,v7_prependBasename:!0},history:S({window:t?.window}),hydrationData:t?.hydrationData||K(),routes:e,mapRouteProperties:n,window:t?.window}).initialize()}function K(){let e=window?.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:M(e.errors)}),e}function M(e){if(!e)return null;let t=Object.entries(e),n={};for(let[r,a]of t)if(a&&"RouteErrorResponse"===a.__type)n[r]=new R(a.status,a.statusText,a.data,!0===a.internal);else if(a&&"Error"===a.__type){if(a.__subType){let e=window[a.__subType];if("function"==typeof e)try{let t=new e(a.message);t.stack="",n[r]=t}catch(ye){}}if(null==n[r]){let e=new Error(a.message);e.stack="",n[r]=e}}else n[r]=a;return n}const O=e.createContext({isTransitioning:!1}),V=e.createContext(new Map),j=e.startTransition,I=t.flushSync,H=e.useId;function z(e){I?I(e):e()}class B{status="pending";constructor(){this.promise=new Promise(((e,t)=>{this.resolve=t=>{"pending"===this.status&&(this.status="resolved",e(t))},this.reject=e=>{"pending"===this.status&&(this.status="rejected",t(e))}}))}}function $({fallbackElement:t,router:n,future:i}){let[s,u]=e.useState(n.state),[l,c]=e.useState(),[f,d]=e.useState({isTransitioning:!1}),[m,h]=e.useState(),[p,w]=e.useState(),[v,g]=e.useState(),y=e.useRef(new Map),{v7_startTransition:b}=i||{},S=e.useCallback((e=>{b?function(e){j?j(e):e()}(e):e()}),[b]),R=e.useCallback(((e,{deletedFetchers:t,unstable_flushSync:r,unstable_viewTransitionOpts:a})=>{t.forEach((e=>y.current.delete(e))),e.fetchers.forEach(((e,t)=>{void 0!==e.data&&y.current.set(t,e.data)}));let o=null==n.window||"function"!=typeof n.window.document.startViewTransition;if(a&&!o){if(r){z((()=>{p&&(m&&m.resolve(),p.skipTransition()),d({isTransitioning:!0,flushSync:!0,currentLocation:a.currentLocation,nextLocation:a.nextLocation})}));let t=n.window.document.startViewTransition((()=>{z((()=>u(e)))}));return t.finished.finally((()=>{z((()=>{h(void 0),w(void 0),c(void 0),d({isTransitioning:!1})}))})),void z((()=>w(t)))}p?(m&&m.resolve(),p.skipTransition(),g({state:e,currentLocation:a.currentLocation,nextLocation:a.nextLocation})):(c(e),d({isTransitioning:!0,flushSync:!1,currentLocation:a.currentLocation,nextLocation:a.nextLocation}))}else r?z((()=>u(e))):S((()=>u(e)))}),[n.window,p,m,y,S]);e.useLayoutEffect((()=>n.subscribe(R)),[n,R]),e.useEffect((()=>{f.isTransitioning&&!f.flushSync&&h(new B)}),[f]),e.useEffect((()=>{if(m&&l&&n.window){let e=l,t=m.promise,r=n.window.document.startViewTransition((async()=>{S((()=>u(e))),await t}));r.finished.finally((()=>{h(void 0),w(void 0),c(void 0),d({isTransitioning:!1})})),w(r)}}),[S,l,m,n.window]),e.useEffect((()=>{m&&l&&s.location.key===l.location.key&&m.resolve()}),[m,p,s.location,l]),e.useEffect((()=>{!f.isTransitioning&&v&&(c(v.state),d({isTransitioning:!0,flushSync:!1,currentLocation:v.currentLocation,nextLocation:v.nextLocation}),g(void 0))}),[f.isTransitioning,v]),e.useEffect((()=>{}),[]);let E=e.useMemo((()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:e=>n.navigate(e),push:(e,t,r)=>n.navigate(e,{state:t,preventScrollReset:r?.preventScrollReset}),replace:(e,t,r)=>n.navigate(e,{replace:!0,state:t,preventScrollReset:r?.preventScrollReset})})),[n]),T=n.basename||"/",_=e.useMemo((()=>({router:n,navigator:E,static:!1,basename:T})),[n,E,T]);return e.createElement(e.Fragment,null,e.createElement(r.Provider,{value:_},e.createElement(a.Provider,{value:s},e.createElement(V.Provider,{value:y.current},e.createElement(O.Provider,{value:f},e.createElement(o,{basename:T,location:s.location,navigationType:s.historyAction,navigator:E,future:{v7_relativeSplatPath:n.future.v7_relativeSplatPath}},s.initialized||n.future.v7_partialHydration?e.createElement(W,{routes:n.routes,future:n.future,state:s}):t))))),null)}function W({routes:e,future:t,state:n}){return i(e,void 0,n,t)}function Y({basename:t,children:n,future:r,window:a}){let i=e.useRef();null==i.current&&(i.current=b({window:a,v5Compat:!0}));let s=i.current,[u,l]=e.useState({action:s.action,location:s.location}),{v7_startTransition:c}=r||{},f=e.useCallback((e=>{c&&j?j((()=>l(e))):l(e)}),[l,c]);return e.useLayoutEffect((()=>s.listen(f)),[s,f]),e.createElement(o,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:s,future:r})}function J({basename:t,children:n,future:r,window:a}){let i=e.useRef();null==i.current&&(i.current=S({window:a,v5Compat:!0}));let s=i.current,[u,l]=e.useState({action:s.action,location:s.location}),{v7_startTransition:c}=r||{},f=e.useCallback((e=>{c&&j?j((()=>l(e))):l(e)}),[l,c]);return e.useLayoutEffect((()=>s.listen(f)),[s,f]),e.createElement(o,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:s,future:r})}function q({basename:t,children:n,future:r,history:a}){let[i,s]=e.useState({action:a.action,location:a.location}),{v7_startTransition:u}=r||{},l=e.useCallback((e=>{u&&j?j((()=>s(e))):s(e)}),[s,u]);return e.useLayoutEffect((()=>a.listen(l)),[a,l]),e.createElement(o,{basename:t,children:n,location:i.location,navigationType:i.action,navigator:a,future:r})}const G="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,Q=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,X=e.forwardRef((function({onClick:t,relative:n,reloadDocument:r,replace:a,state:o,target:i,to:l,preventScrollReset:c,unstable_viewTransition:f,...d},m){let h,{basename:p}=e.useContext(s),w=!1;if("string"==typeof l&&Q.test(l)&&(h=l,G))try{let e=new URL(window.location.href),t=l.startsWith("//")?new URL(e.protocol+l):new URL(l),n=g(t.pathname,p);t.origin===e.origin&&null!=n?l=n+t.search+t.hash:w=!0}catch(ye){}let v=u(l,{relative:n}),y=ie(l,{replace:a,state:o,target:i,preventScrollReset:c,relative:n,unstable_viewTransition:f});return e.createElement("a",Object.assign({},d,{href:h||v,onClick:w||r?t:function(e){t&&t(e),e.defaultPrevented||y(e)},ref:m,target:i}))})),Z=e.forwardRef((function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:o=!1,style:i,to:u,unstable_viewTransition:f,children:d,...m},h){let p=l(u,{relative:m.relative}),w=c(),v=e.useContext(a),{navigator:y,basename:b}=e.useContext(s),S=null!=v&&ge(p)&&!0===f,R=y.encodeLocation?y.encodeLocation(p).pathname:p.pathname,E=w.pathname,T=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;n||(E=E.toLowerCase(),T=T?T.toLowerCase():null,R=R.toLowerCase()),T&&b&&(T=g(T,b)||T);const _="/"!==R&&R.endsWith("/")?R.length-1:R.length;let L,C=E===R||!o&&E.startsWith(R)&&"/"===E.charAt(_),x=null!=T&&(T===R||!o&&T.startsWith(R)&&"/"===T.charAt(R.length)),A={isActive:C,isPending:x,isTransitioning:S},F=C?t:void 0;L="function"==typeof r?r(A):[r,C?"active":null,x?"pending":null,S?"transitioning":null].filter(Boolean).join(" ");let U="function"==typeof i?i(A):i;return e.createElement(X,Object.assign({},m,{"aria-current":F,className:L,ref:h,style:U,to:u,unstable_viewTransition:f}),"function"==typeof d?d(A):d)})),ee=e.forwardRef((({fetcherKey:t,navigate:n,reloadDocument:r,replace:a,state:o,method:i="get",action:s,onSubmit:u,relative:l,preventScrollReset:c,unstable_viewTransition:f,...d},m)=>{let h=ce(),p=fe(s,{relative:l}),w="get"===i.toLowerCase()?"get":"post";return e.createElement("form",Object.assign({ref:m,method:w,action:p,onSubmit:r?u:e=>{if(u&&u(e),e.defaultPrevented)return;e.preventDefault();let r=e.nativeEvent.submitter,s=r?.getAttribute("formmethod")||i;h(r||e.currentTarget,{fetcherKey:t,method:s,navigate:n,replace:a,state:o,relative:l,preventScrollReset:c,unstable_viewTransition:f})}},d))}));function te({getKey:e,storageKey:t}){return pe({getKey:e,storageKey:t}),null}var ne=function(e){return e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState",e}(ne||{}),re=function(e){return e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration",e}(re||{});function ae(t){let n=e.useContext(r);return n||E(!1),n}function oe(t){let n=e.useContext(a);return n||E(!1),n}function ie(t,{target:n,replace:r,state:a,preventScrollReset:o,relative:i,unstable_viewTransition:s}={}){let u=f(),m=c(),h=l(t,{relative:i});return e.useCallback((e=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(e,n)){e.preventDefault();let n=void 0!==r?r:d(m)===d(h);u(t,{replace:n,state:a,preventScrollReset:o,relative:i,unstable_viewTransition:s})}}),[m,u,h,r,a,n,t,o,i,s])}function se(t){let n=e.useRef(A(t)),r=e.useRef(!1),a=c(),o=e.useMemo((()=>function(e,t){let n=A(e);return t&&t.forEach(((e,r)=>{n.has(r)||t.getAll(r).forEach((e=>{n.append(r,e)}))})),n}(a.search,r.current?null:n.current)),[a.search]),i=f(),s=e.useCallback(((e,t)=>{const n=A("function"==typeof e?e(o):e);r.current=!0,i("?"+n,t)}),[i,o]);return[o,s]}let ue=0,le=()=>`__${String(++ue)}__`;function ce(){let{router:t}=ae(ne.UseSubmit),{basename:n}=e.useContext(s),r=m();return e.useCallback(((e,a={})=>{!function(){if("undefined"==typeof document)throw new Error("You are calling submit during the server render. Try calling submit within a `useEffect` or callback instead.")}();let{action:o,method:i,encType:s,formData:u,body:l}=P(e,n);if(!1===a.navigate){let e=a.fetcherKey||le();t.fetch(e,r,a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:l,formMethod:a.method||i,formEncType:a.encType||s,unstable_flushSync:a.unstable_flushSync})}else t.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:l,formMethod:a.method||i,formEncType:a.encType||s,replace:a.replace,state:a.state,fromRouteId:r,unstable_flushSync:a.unstable_flushSync,unstable_viewTransition:a.unstable_viewTransition})}),[t,n,r])}function fe(t,{relative:n}={}){let{basename:r}=e.useContext(s),a=e.useContext(h);a||E(!1);let[o]=a.matches.slice(-1),i={...l(t||".",{relative:n})},u=c();if(null==t){i.search=u.search;let e=new URLSearchParams(i.search);e.has("index")&&""===e.get("index")&&(e.delete("index"),i.search=e.toString()?`?${e.toString()}`:"")}return t&&"."!==t||!o.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),"/"!==r&&(i.pathname="/"===i.pathname?r:T([r,i.pathname])),d(i)}function de({key:t}={}){let{router:n}=ae(ne.UseFetcher),r=oe(re.UseFetcher),a=e.useContext(V),o=e.useContext(h),i=o.matches[o.matches.length-1]?.route.id;a||E(!1),o||E(!1),null==i&&E(!1);let s=H?H():"",[u,l]=e.useState(t||s);t&&t!==u?l(t):u||l(le()),e.useEffect((()=>(n.getFetcher(u),()=>{n.deleteFetcher(u)})),[n,u]);let c=e.useCallback(((e,t)=>{i||E(!1),n.fetch(u,i,e,t)}),[u,i,n]),f=ce(),d=e.useCallback(((e,t)=>{f(e,{...t,navigate:!1,fetcherKey:u})}),[u,f]),m=e.useMemo((()=>e.forwardRef(((t,n)=>e.createElement(ee,Object.assign({},t,{navigate:!1,fetcherKey:u,ref:n}))))),[u]),p=r.fetchers.get(u)||_,w=a.get(u);return e.useMemo((()=>({Form:m,submit:d,load:c,...p,data:w})),[m,d,c,p,w])}function me(){let e=oe(re.UseFetchers);return Array.from(e.fetchers.entries()).map((([e,t])=>({...t,key:e})))}let he={};function pe({getKey:t,storageKey:n}={}){let{router:r}=ae(ne.UseScrollRestoration),{restoreScrollPosition:a,preventScrollReset:o}=oe(re.UseScrollRestoration),{basename:i}=e.useContext(s),u=c(),l=p(),f=w();e.useEffect((()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"})),[]),function(t,n){let{capture:r}=n||{};e.useEffect((()=>{let e=null!=r?{capture:r}:void 0;return window.addEventListener("pagehide",t,e),()=>{window.removeEventListener("pagehide",t,e)}}),[t,r])}(e.useCallback((()=>{if("idle"===f.state){let e=(t?t(u,l):null)||u.key;he[e]=window.scrollY}try{sessionStorage.setItem(n||"react-router-scroll-positions",JSON.stringify(he))}catch(e){}window.history.scrollRestoration="auto"}),[n,t,f.state,u,l])),"undefined"!=typeof document&&(e.useLayoutEffect((()=>{try{let e=sessionStorage.getItem(n||"react-router-scroll-positions");e&&(he=JSON.parse(e))}catch(ye){}}),[n]),e.useLayoutEffect((()=>{let e=t&&"/"!==i?(e,n)=>t({...e,pathname:g(e.pathname,i)||e.pathname},n):t,n=r?.enableScrollRestoration(he,(()=>window.scrollY),e);return()=>n&&n()}),[r,i,t]),e.useLayoutEffect((()=>{if(!1!==a)if("number"!=typeof a){if(u.hash){let e=document.getElementById(decodeURIComponent(u.hash.slice(1)));if(e)return void e.scrollIntoView()}!0!==o&&window.scrollTo(0,0)}else window.scrollTo(0,a)}),[u,a,o]))}function we(t,n){let{capture:r}=n||{};e.useEffect((()=>{let e=null!=r?{capture:r}:void 0;return window.addEventListener("beforeunload",t,e),()=>{window.removeEventListener("beforeunload",t,e)}}),[t,r])}function ve({when:t,message:n}){let r=v(t);e.useEffect((()=>{if("blocked"===r.state){window.confirm(n)?setTimeout(r.proceed,0):r.reset()}}),[r,n]),e.useEffect((()=>{"blocked"!==r.state||t||r.reset()}),[r,t])}function ge(t,n={}){let r=e.useContext(O);null==r&&E(!1);let{basename:a}=ae(ne.useViewTransitionState),o=l(t,{relative:n.relative});if(!r.isTransitioning)return!1;let i=g(r.currentLocation.pathname,a)||r.currentLocation.pathname,s=g(r.nextLocation.pathname,a)||r.nextLocation.pathname;return null!=L(o.pathname,s)||null!=L(o.pathname,i)}export{Y as BrowserRouter,ee as Form,J as HashRouter,X as Link,Z as NavLink,$ as RouterProvider,te as ScrollRestoration,V as UNSAFE_FetchersContext,O as UNSAFE_ViewTransitionContext,pe as UNSAFE_useScrollRestoration,D as createBrowserRouter,N as createHashRouter,A as createSearchParams,q as unstable_HistoryRouter,ve as unstable_usePrompt,ge as unstable_useViewTransitionState,we as useBeforeUnload,de as useFetcher,me as useFetchers,fe as useFormAction,ie as useLinkClickHandler,se as useSearchParams,ce as useSubmit};
//# sourceMappingURL=react-router-dom.production.min.js.map
