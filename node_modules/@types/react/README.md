# Installation
> `npm install --save @types/react`

# Summary
This package contains type definitions for react (https://react.dev/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react.

### Additional Details
 * Last updated: Fri, 23 Feb 2024 08:07:48 GMT
 * Dependencies: [@types/prop-types](https://npmjs.com/package/@types/prop-types), [@types/scheduler](https://npmjs.com/package/@types/scheduler), [csstype](https://npmjs.com/package/csstype)

# Credits
These definitions were written by [<PERSON><PERSON>](https://asana.com), [AssureSign](http://www.assuresign.com), [Microsoft](https://microsoft.com), [<PERSON>](https://github.com/johnnyreilly), [Benoit Benezech](https://github.com/bbenezech), [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/ericanderson), [<PERSON><PERSON><PERSON>](https://github.com/Dovy<PERSON>), [<PERSON>](https://github.com/theruther4d), [Guilherme Hübner](https://github.com/guilhermehubner), [Ferdy Budhidharma](https://github.com/ferdaber), [Johann Rakotoharisoa](https://github.com/jrakotoharisoa), [Olivier Pascal](https://github.com/pascaloliv), [Martin Hochel](https://github.com/hotell), [Frank Li](https://github.com/franklixuefei), [Jessica Franco](https://github.com/Jessidhia), [Saransh Kataria](https://github.com/saranshkataria), [Kanitkorn Sujautra](https://github.com/lukyth), [Sebastian Silbermann](https://github.com/eps1lon), [Kyle Scully](https://github.com/zieka), [Cong Zhang](https://github.com/dancerphil), [Dimitri Mitropoulos](https://github.com/dimitropoulos), [JongChan Choi](https://github.com/disjukr), [Victor Magalhães](https://github.com/vhfmag), [Dale Tan](https://github.com/hellatan), [Priyanshu Rav](https://github.com/priyanshurav), and [Dmitry Semigradsky](https://github.com/Semigradsky).
