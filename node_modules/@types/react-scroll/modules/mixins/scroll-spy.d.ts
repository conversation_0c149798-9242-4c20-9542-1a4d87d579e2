interface ScrollSpy {
    update(): void;
    unmount(stateHandler: any, spyHandler: any): void;
    updateStates(): void;
    addSpyHandler(handler: any, scrollSpyContainer: any): void;
    addStateHandler(handler: any): void;
    scrollHandler(scrollSpyContainer: any): void;
    currentPositionY(scrollSpyContainer: any): number;
    isMounted(scrollSpyContainer: any): boolean;
    mount(scrollSpyContainer: any): void;
    spyCallbacks: any[];
    spySetState: any[];
    scrollSpyContainers: any[];
}

declare const scrollSpy: ScrollSpy;
export default scrollSpy;
