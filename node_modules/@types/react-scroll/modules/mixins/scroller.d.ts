export function unmount(): void;
export function register(name: string, element: any): void;
export function unregister(name: string): void;
export function get(name: string): any;
export function setActiveLink(link: string): void;
export function getActiveLink(): string;
export function scrollTo(to: string, props: any): void;

export interface Scroller {
    unmount: typeof unmount;
    register: typeof register;
    unregister: typeof unregister;
    get: typeof get;
    setActiveLink: typeof setActiveLink;
    getActiveLink: typeof getActiveLink;
    scrollTo: typeof scrollTo;
}
