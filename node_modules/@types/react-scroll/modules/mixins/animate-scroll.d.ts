export function animateTopScroll(y: number, options: any, to: string, target: any): void;
export function getAnimationType(options: { smooth: boolean | string }): (x: number) => number;
export function scrollToTop(options?: any): void;
export function scrollToBottom(options?: any): void;
export function scrollTo(toY: number, options?: any): void;
export function scrollMore(toY: number, options?: any): void;
