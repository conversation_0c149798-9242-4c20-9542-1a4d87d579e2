import{r as a,M as Jr,j as m,u as Zr,P as ru,a as Qr,L as ou,m as su,c as iu,f as Hs,i as dn,b as au,d as cu,e as lu,g as xe,h as K,R as W,k as Fe,l as Tn,$ as Vs,V as uu,n as du,_ as U,o as fn,p as ve,q as zs,s as me,t as fu,B as zo}from"./index-BYQPfkjs.js";class pu extends a.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function mu({children:e,isPresent:t}){const n=a.useId(),r=a.useRef(null),o=a.useRef({width:0,height:0,top:0,left:0}),{nonce:s}=a.useContext(Jr);return a.useInsertionEffect(()=>{const{width:i,height:c,top:l,left:u}=o.current;if(t||!r.current||!i||!c)return;r.current.dataset.motionPopId=n;const d=document.createElement("style");return s&&(d.nonce=s),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${i}px !important;
            height: ${c}px !important;
            top: ${l}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[t]),m.jsx(pu,{isPresent:t,childRef:r,sizeRef:o,children:a.cloneElement(e,{ref:r})})}const hu=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:s,mode:i})=>{const c=Zr(vu),l=a.useId(),u=a.useMemo(()=>({id:l,initial:t,isPresent:n,custom:o,onExitComplete:d=>{c.set(d,!0);for(const f of c.values())if(!f)return;r&&r()},register:d=>(c.set(d,!1),()=>c.delete(d))}),s?[Math.random()]:[n]);return a.useMemo(()=>{c.forEach((d,f)=>c.set(f,!1))},[n]),a.useEffect(()=>{!n&&!c.size&&r&&r()},[n]),i==="popLayout"&&(e=m.jsx(mu,{isPresent:n,children:e})),m.jsx(ru.Provider,{value:u,children:e})};function vu(){return new Map}const Gt=e=>e.key||"";function Ko(e){const t=[];return a.Children.forEach(e,n=>{a.isValidElement(n)&&t.push(n)}),t}const Ks=({children:e,exitBeforeEnter:t,custom:n,initial:r=!0,onExitComplete:o,presenceAffectsLayout:s=!0,mode:i="sync"})=>{const c=a.useMemo(()=>Ko(e),[e]),l=c.map(Gt),u=a.useRef(!0),d=a.useRef(c),f=Zr(()=>new Map),[v,g]=a.useState(c),[h,p]=a.useState(c);Qr(()=>{u.current=!1,d.current=c;for(let y=0;y<h.length;y++){const w=Gt(h[y]);l.includes(w)?f.delete(w):f.get(w)!==!0&&f.set(w,!1)}},[h,l.length,l.join("-")]);const b=[];if(c!==v){let y=[...c];for(let w=0;w<h.length;w++){const C=h[w],E=Gt(C);l.includes(E)||(y.splice(w,0,C),b.push(C))}i==="wait"&&b.length&&(y=b),p(Ko(y)),g(c);return}const{forceRender:x}=a.useContext(ou);return m.jsx(m.Fragment,{children:h.map(y=>{const w=Gt(y),C=c===h||l.includes(w),E=()=>{if(f.has(w))f.set(w,!0);else return;let P=!0;f.forEach(S=>{S||(P=!1)}),P&&(x==null||x(),p(d.current),o&&o())};return m.jsx(hu,{isPresent:C,initial:!u.current||r?void 0:!1,custom:C?void 0:n,presenceAffectsLayout:s,mode:i,onExitComplete:C?void 0:E,children:y},w)})})};function At(e){const t=Zr(()=>su(e)),{isStatic:n}=a.useContext(Jr);if(n){const[,r]=a.useState(e);a.useEffect(()=>t.on("change",r),[])}return t}function gu(e,t){const n=At(t()),r=()=>n.set(t());return r(),Qr(()=>{const o=()=>Hs.preRender(r,!1,!0),s=e.map(i=>i.on("change",o));return()=>{s.forEach(i=>i()),iu(r)}}),n}function bu(e,...t){const n=e.length;function r(){let o="";for(let s=0;s<n;s++){o+=e[s];const i=t[s];i&&(o+=dn(i)?i.get():i)}return o}return gu(t.filter(dn),r)}function Go(e){return typeof e=="number"?e:parseFloat(e)}function xu(e,t={}){const{isStatic:n}=a.useContext(Jr),r=a.useRef(null),o=At(dn(e)?Go(e.get()):e),s=a.useRef(o.get()),i=a.useRef(()=>{}),c=()=>{const u=r.current;u&&u.time===0&&u.sample(au.delta),l(),r.current=cu({keyframes:[o.get(),s.current],velocity:o.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...t,onUpdate:i.current})},l=()=>{r.current&&r.current.stop()};return a.useInsertionEffect(()=>o.attach((u,d)=>n?d(u):(s.current=u,i.current=d,Hs.update(c),o.get()),l),[JSON.stringify(t)]),Qr(()=>{if(dn(e))return e.on("change",u=>o.set(Go(u)))},[o]),o}function yu(e,t,n){var r;if(typeof e=="string"){let o=document;t&&(lu(!!t.current),o=t.current),n?((r=n[e])!==null&&r!==void 0||(n[e]=o.querySelectorAll(e)),e=n[e]):e=o.querySelectorAll(e)}else e instanceof Element&&(e=[e]);return Array.from(e||[])}const wu={some:0,all:1};function Cu(e,t,{root:n,margin:r,amount:o="some"}={}){const s=yu(e),i=new WeakMap,c=u=>{u.forEach(d=>{const f=i.get(d.target);if(d.isIntersecting!==!!f)if(d.isIntersecting){const v=t(d);typeof v=="function"?i.set(d.target,v):l.unobserve(d.target)}else f&&(f(d),i.delete(d.target))})},l=new IntersectionObserver(c,{root:n,rootMargin:r,threshold:typeof o=="number"?o:wu[o]});return s.forEach(u=>l.observe(u)),()=>l.disconnect()}function Eu(e,{root:t,margin:n,amount:r,once:o=!1}={}){const[s,i]=a.useState(!1);return a.useEffect(()=>{if(!e.current||o&&s)return;const c=()=>(i(!0),o?void 0:()=>i(!1)),l={root:t&&t.current||void 0,margin:n,amount:r};return Cu(e.current,c,l)},[t,e,n,o,r]),s}/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Su=xe("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eo=xe("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ot=xe("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $u=xe("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gs=xe("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ru=xe("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pu=xe("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tu=xe("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nu=xe("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Au=xe("FileWarning",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ou=xe("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _u=xe("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);function qs(e,t){return function(){return e.apply(t,arguments)}}const{toString:Mu}=Object.prototype,{getPrototypeOf:to}=Object,{iterator:Nn,toStringTag:Ys}=Symbol,An=(e=>t=>{const n=Mu.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),$e=e=>(e=e.toLowerCase(),t=>An(t)===e),On=e=>t=>typeof t===e,{isArray:ht}=Array,_t=On("undefined");function Lt(e){return e!==null&&!_t(e)&&e.constructor!==null&&!_t(e.constructor)&&de(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Xs=$e("ArrayBuffer");function Iu(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Xs(e.buffer),t}const Du=On("string"),de=On("function"),Js=On("number"),Ft=e=>e!==null&&typeof e=="object",ju=e=>e===!0||e===!1,sn=e=>{if(An(e)!=="object")return!1;const t=to(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ys in e)&&!(Nn in e)},ku=e=>{if(!Ft(e)||Lt(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Lu=$e("Date"),Fu=$e("File"),Bu=$e("Blob"),Uu=$e("FileList"),Wu=e=>Ft(e)&&de(e.pipe),Hu=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||de(e.append)&&((t=An(e))==="formdata"||t==="object"&&de(e.toString)&&e.toString()==="[object FormData]"))},Vu=$e("URLSearchParams"),[zu,Ku,Gu,qu]=["ReadableStream","Request","Response","Headers"].map($e),Yu=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Bt(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,o;if(typeof e!="object"&&(e=[e]),ht(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{if(Lt(e))return;const s=n?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let c;for(r=0;r<i;r++)c=s[r],t.call(null,e[c],c,e)}}function Zs(e,t){if(Lt(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const Ye=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Qs=e=>!_t(e)&&e!==Ye;function pr(){const{caseless:e}=Qs(this)&&this||{},t={},n=(r,o)=>{const s=e&&Zs(t,o)||o;sn(t[s])&&sn(r)?t[s]=pr(t[s],r):sn(r)?t[s]=pr({},r):ht(r)?t[s]=r.slice():t[s]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&Bt(arguments[r],n);return t}const Xu=(e,t,n,{allOwnKeys:r}={})=>(Bt(t,(o,s)=>{n&&de(o)?e[s]=qs(o,n):e[s]=o},{allOwnKeys:r}),e),Ju=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Zu=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Qu=(e,t,n,r)=>{let o,s,i;const c={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&to(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},ed=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},td=e=>{if(!e)return null;if(ht(e))return e;let t=e.length;if(!Js(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},nd=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&to(Uint8Array)),rd=(e,t)=>{const r=(e&&e[Nn]).call(e);let o;for(;(o=r.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},od=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},sd=$e("HTMLFormElement"),id=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),qo=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),ad=$e("RegExp"),ei=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Bt(n,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(r[s]=i||o)}),Object.defineProperties(e,r)},cd=e=>{ei(e,(t,n)=>{if(de(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(de(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ld=(e,t)=>{const n={},r=o=>{o.forEach(s=>{n[s]=!0})};return ht(e)?r(e):r(String(e).split(t)),n},ud=()=>{},dd=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function fd(e){return!!(e&&de(e.append)&&e[Ys]==="FormData"&&e[Nn])}const pd=e=>{const t=new Array(10),n=(r,o)=>{if(Ft(r)){if(t.indexOf(r)>=0)return;if(Lt(r))return r;if(!("toJSON"in r)){t[o]=r;const s=ht(r)?[]:{};return Bt(r,(i,c)=>{const l=n(i,o+1);!_t(l)&&(s[c]=l)}),t[o]=void 0,s}}return r};return n(e,0)},md=$e("AsyncFunction"),hd=e=>e&&(Ft(e)||de(e))&&de(e.then)&&de(e.catch),ti=((e,t)=>e?setImmediate:t?((n,r)=>(Ye.addEventListener("message",({source:o,data:s})=>{o===Ye&&s===n&&r.length&&r.shift()()},!1),o=>{r.push(o),Ye.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",de(Ye.postMessage)),vd=typeof queueMicrotask<"u"?queueMicrotask.bind(Ye):typeof process<"u"&&process.nextTick||ti,gd=e=>e!=null&&de(e[Nn]),$={isArray:ht,isArrayBuffer:Xs,isBuffer:Lt,isFormData:Hu,isArrayBufferView:Iu,isString:Du,isNumber:Js,isBoolean:ju,isObject:Ft,isPlainObject:sn,isEmptyObject:ku,isReadableStream:zu,isRequest:Ku,isResponse:Gu,isHeaders:qu,isUndefined:_t,isDate:Lu,isFile:Fu,isBlob:Bu,isRegExp:ad,isFunction:de,isStream:Wu,isURLSearchParams:Vu,isTypedArray:nd,isFileList:Uu,forEach:Bt,merge:pr,extend:Xu,trim:Yu,stripBOM:Ju,inherits:Zu,toFlatObject:Qu,kindOf:An,kindOfTest:$e,endsWith:ed,toArray:td,forEachEntry:rd,matchAll:od,isHTMLForm:sd,hasOwnProperty:qo,hasOwnProp:qo,reduceDescriptors:ei,freezeMethods:cd,toObjectSet:ld,toCamelCase:id,noop:ud,toFiniteNumber:dd,findKey:Zs,global:Ye,isContextDefined:Qs,isSpecCompliantForm:fd,toJSONObject:pd,isAsyncFn:md,isThenable:hd,setImmediate:ti,asap:vd,isIterable:gd};function B(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}$.inherits(B,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:$.toJSONObject(this.config),code:this.code,status:this.status}}});const ni=B.prototype,ri={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ri[e]={value:e}});Object.defineProperties(B,ri);Object.defineProperty(ni,"isAxiosError",{value:!0});B.from=(e,t,n,r,o,s)=>{const i=Object.create(ni);return $.toFlatObject(e,i,function(l){return l!==Error.prototype},c=>c!=="isAxiosError"),B.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};const bd=null;function mr(e){return $.isPlainObject(e)||$.isArray(e)}function oi(e){return $.endsWith(e,"[]")?e.slice(0,-2):e}function Yo(e,t,n){return e?e.concat(t).map(function(o,s){return o=oi(o),!n&&s?"["+o+"]":o}).join(n?".":""):t}function xd(e){return $.isArray(e)&&!e.some(mr)}const yd=$.toFlatObject($,{},null,function(t){return/^is[A-Z]/.test(t)});function _n(e,t,n){if(!$.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=$.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,b){return!$.isUndefined(b[p])});const r=n.metaTokens,o=n.visitor||d,s=n.dots,i=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&$.isSpecCompliantForm(t);if(!$.isFunction(o))throw new TypeError("visitor must be a function");function u(h){if(h===null)return"";if($.isDate(h))return h.toISOString();if($.isBoolean(h))return h.toString();if(!l&&$.isBlob(h))throw new B("Blob is not supported. Use a Buffer instead.");return $.isArrayBuffer(h)||$.isTypedArray(h)?l&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function d(h,p,b){let x=h;if(h&&!b&&typeof h=="object"){if($.endsWith(p,"{}"))p=r?p:p.slice(0,-2),h=JSON.stringify(h);else if($.isArray(h)&&xd(h)||($.isFileList(h)||$.endsWith(p,"[]"))&&(x=$.toArray(h)))return p=oi(p),x.forEach(function(w,C){!($.isUndefined(w)||w===null)&&t.append(i===!0?Yo([p],C,s):i===null?p:p+"[]",u(w))}),!1}return mr(h)?!0:(t.append(Yo(b,p,s),u(h)),!1)}const f=[],v=Object.assign(yd,{defaultVisitor:d,convertValue:u,isVisitable:mr});function g(h,p){if(!$.isUndefined(h)){if(f.indexOf(h)!==-1)throw Error("Circular reference detected in "+p.join("."));f.push(h),$.forEach(h,function(x,y){(!($.isUndefined(x)||x===null)&&o.call(t,x,$.isString(y)?y.trim():y,p,v))===!0&&g(x,p?p.concat(y):[y])}),f.pop()}}if(!$.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Xo(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function no(e,t){this._pairs=[],e&&_n(e,this,t)}const si=no.prototype;si.append=function(t,n){this._pairs.push([t,n])};si.toString=function(t){const n=t?function(r){return t.call(this,r,Xo)}:Xo;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function wd(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ii(e,t,n){if(!t)return e;const r=n&&n.encode||wd;$.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(o?s=o(t,n):s=$.isURLSearchParams(t)?t.toString():new no(t,n).toString(r),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Jo{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){$.forEach(this.handlers,function(r){r!==null&&t(r)})}}const ai={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Cd=typeof URLSearchParams<"u"?URLSearchParams:no,Ed=typeof FormData<"u"?FormData:null,Sd=typeof Blob<"u"?Blob:null,$d={isBrowser:!0,classes:{URLSearchParams:Cd,FormData:Ed,Blob:Sd},protocols:["http","https","file","blob","url","data"]},ro=typeof window<"u"&&typeof document<"u",hr=typeof navigator=="object"&&navigator||void 0,Rd=ro&&(!hr||["ReactNative","NativeScript","NS"].indexOf(hr.product)<0),Pd=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Td=ro&&window.location.href||"http://localhost",Nd=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ro,hasStandardBrowserEnv:Rd,hasStandardBrowserWebWorkerEnv:Pd,navigator:hr,origin:Td},Symbol.toStringTag,{value:"Module"})),ce={...Nd,...$d};function Ad(e,t){return _n(e,new ce.classes.URLSearchParams,{visitor:function(n,r,o,s){return ce.isNode&&$.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)},...t})}function Od(e){return $.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function _d(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}function ci(e){function t(n,r,o,s){let i=n[s++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),l=s>=n.length;return i=!i&&$.isArray(o)?o.length:i,l?($.hasOwnProp(o,i)?o[i]=[o[i],r]:o[i]=r,!c):((!o[i]||!$.isObject(o[i]))&&(o[i]=[]),t(n,r,o[i],s)&&$.isArray(o[i])&&(o[i]=_d(o[i])),!c)}if($.isFormData(e)&&$.isFunction(e.entries)){const n={};return $.forEachEntry(e,(r,o)=>{t(Od(r),o,n,0)}),n}return null}function Md(e,t,n){if($.isString(e))try{return(t||JSON.parse)(e),$.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const oo={transitional:ai,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,s=$.isObject(t);if(s&&$.isHTMLForm(t)&&(t=new FormData(t)),$.isFormData(t))return o?JSON.stringify(ci(t)):t;if($.isArrayBuffer(t)||$.isBuffer(t)||$.isStream(t)||$.isFile(t)||$.isBlob(t)||$.isReadableStream(t))return t;if($.isArrayBufferView(t))return t.buffer;if($.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Ad(t,this.formSerializer).toString();if((c=$.isFileList(t))||r.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return _n(c?{"files[]":t}:t,l&&new l,this.formSerializer)}}return s||o?(n.setContentType("application/json",!1),Md(t)):t}],transformResponse:[function(t){const n=this.transitional||oo.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if($.isResponse(t)||$.isReadableStream(t))return t;if(t&&$.isString(t)&&(r&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?B.from(c,B.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ce.classes.FormData,Blob:ce.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};$.forEach(["delete","get","head","post","put","patch"],e=>{oo.headers[e]={}});const so=oo,Id=$.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Dd=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),r=i.substring(o+1).trim(),!(!n||t[n]&&Id[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Zo=Symbol("internals");function $t(e){return e&&String(e).trim().toLowerCase()}function an(e){return e===!1||e==null?e:$.isArray(e)?e.map(an):String(e)}function jd(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const kd=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Jn(e,t,n,r,o){if($.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!$.isString(t)){if($.isString(r))return t.indexOf(r)!==-1;if($.isRegExp(r))return r.test(t)}}function Ld(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Fd(e,t){const n=$.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,s,i){return this[r].call(this,t,o,s,i)},configurable:!0})})}class Mn{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function s(c,l,u){const d=$t(l);if(!d)throw new Error("header name must be a non-empty string");const f=$.findKey(o,d);(!f||o[f]===void 0||u===!0||u===void 0&&o[f]!==!1)&&(o[f||l]=an(c))}const i=(c,l)=>$.forEach(c,(u,d)=>s(u,d,l));if($.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if($.isString(t)&&(t=t.trim())&&!kd(t))i(Dd(t),n);else if($.isObject(t)&&$.isIterable(t)){let c={},l,u;for(const d of t){if(!$.isArray(d))throw TypeError("Object iterator must return a key-value pair");c[u=d[0]]=(l=c[u])?$.isArray(l)?[...l,d[1]]:[l,d[1]]:d[1]}i(c,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=$t(t),t){const r=$.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return jd(o);if($.isFunction(n))return n.call(this,o,r);if($.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=$t(t),t){const r=$.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Jn(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function s(i){if(i=$t(i),i){const c=$.findKey(r,i);c&&(!n||Jn(r,r[c],c,n))&&(delete r[c],o=!0)}}return $.isArray(t)?t.forEach(s):s(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const s=n[r];(!t||Jn(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const n=this,r={};return $.forEach(this,(o,s)=>{const i=$.findKey(r,s);if(i){n[i]=an(o),delete n[s];return}const c=t?Ld(s):String(s).trim();c!==s&&delete n[s],n[c]=an(o),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return $.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&$.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[Zo]=this[Zo]={accessors:{}}).accessors,o=this.prototype;function s(i){const c=$t(i);r[c]||(Fd(o,i),r[c]=!0)}return $.isArray(t)?t.forEach(s):s(t),this}}Mn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);$.reduceDescriptors(Mn.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});$.freezeMethods(Mn);const Ee=Mn;function Zn(e,t){const n=this||so,r=t||n,o=Ee.from(r.headers);let s=r.data;return $.forEach(e,function(c){s=c.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function li(e){return!!(e&&e.__CANCEL__)}function vt(e,t,n){B.call(this,e??"canceled",B.ERR_CANCELED,t,n),this.name="CanceledError"}$.inherits(vt,B,{__CANCEL__:!0});function ui(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new B("Request failed with status code "+n.status,[B.ERR_BAD_REQUEST,B.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Bd(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Ud(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),d=r[s];i||(i=u),n[o]=l,r[o]=u;let f=s,v=0;for(;f!==o;)v+=n[f++],f=f%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),u-i<t)return;const g=d&&u-d;return g?Math.round(v*1e3/g):void 0}}function Wd(e,t){let n=0,r=1e3/t,o,s;const i=(u,d=Date.now())=>{n=d,o=null,s&&(clearTimeout(s),s=null),e(...u)};return[(...u)=>{const d=Date.now(),f=d-n;f>=r?i(u,d):(o=u,s||(s=setTimeout(()=>{s=null,i(o)},r-f)))},()=>o&&i(o)]}const pn=(e,t,n=3)=>{let r=0;const o=Ud(50,250);return Wd(s=>{const i=s.loaded,c=s.lengthComputable?s.total:void 0,l=i-r,u=o(l),d=i<=c;r=i;const f={loaded:i,total:c,progress:c?i/c:void 0,bytes:l,rate:u||void 0,estimated:u&&c&&d?(c-i)/u:void 0,event:s,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(f)},n)},Qo=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},es=e=>(...t)=>$.asap(()=>e(...t)),Hd=ce.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,ce.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(ce.origin),ce.navigator&&/(msie|trident)/i.test(ce.navigator.userAgent)):()=>!0,Vd=ce.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];$.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),$.isString(r)&&i.push("path="+r),$.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function zd(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Kd(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function di(e,t,n){let r=!zd(t);return e&&(r||n==!1)?Kd(e,t):t}const ts=e=>e instanceof Ee?{...e}:e;function Xe(e,t){t=t||{};const n={};function r(u,d,f,v){return $.isPlainObject(u)&&$.isPlainObject(d)?$.merge.call({caseless:v},u,d):$.isPlainObject(d)?$.merge({},d):$.isArray(d)?d.slice():d}function o(u,d,f,v){if($.isUndefined(d)){if(!$.isUndefined(u))return r(void 0,u,f,v)}else return r(u,d,f,v)}function s(u,d){if(!$.isUndefined(d))return r(void 0,d)}function i(u,d){if($.isUndefined(d)){if(!$.isUndefined(u))return r(void 0,u)}else return r(void 0,d)}function c(u,d,f){if(f in t)return r(u,d);if(f in e)return r(void 0,u)}const l={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(u,d,f)=>o(ts(u),ts(d),f,!0)};return $.forEach(Object.keys({...e,...t}),function(d){const f=l[d]||o,v=f(e[d],t[d],d);$.isUndefined(v)&&f!==c||(n[d]=v)}),n}const fi=e=>{const t=Xe({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:c}=t;t.headers=i=Ee.from(i),t.url=ii(di(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let l;if($.isFormData(n)){if(ce.hasStandardBrowserEnv||ce.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[u,...d]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...d].join("; "))}}if(ce.hasStandardBrowserEnv&&(r&&$.isFunction(r)&&(r=r(t)),r||r!==!1&&Hd(t.url))){const u=o&&s&&Vd.read(s);u&&i.set(o,u)}return t},Gd=typeof XMLHttpRequest<"u",qd=Gd&&function(e){return new Promise(function(n,r){const o=fi(e);let s=o.data;const i=Ee.from(o.headers).normalize();let{responseType:c,onUploadProgress:l,onDownloadProgress:u}=o,d,f,v,g,h;function p(){g&&g(),h&&h(),o.cancelToken&&o.cancelToken.unsubscribe(d),o.signal&&o.signal.removeEventListener("abort",d)}let b=new XMLHttpRequest;b.open(o.method.toUpperCase(),o.url,!0),b.timeout=o.timeout;function x(){if(!b)return;const w=Ee.from("getAllResponseHeaders"in b&&b.getAllResponseHeaders()),E={data:!c||c==="text"||c==="json"?b.responseText:b.response,status:b.status,statusText:b.statusText,headers:w,config:e,request:b};ui(function(S){n(S),p()},function(S){r(S),p()},E),b=null}"onloadend"in b?b.onloadend=x:b.onreadystatechange=function(){!b||b.readyState!==4||b.status===0&&!(b.responseURL&&b.responseURL.indexOf("file:")===0)||setTimeout(x)},b.onabort=function(){b&&(r(new B("Request aborted",B.ECONNABORTED,e,b)),b=null)},b.onerror=function(){r(new B("Network Error",B.ERR_NETWORK,e,b)),b=null},b.ontimeout=function(){let C=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const E=o.transitional||ai;o.timeoutErrorMessage&&(C=o.timeoutErrorMessage),r(new B(C,E.clarifyTimeoutError?B.ETIMEDOUT:B.ECONNABORTED,e,b)),b=null},s===void 0&&i.setContentType(null),"setRequestHeader"in b&&$.forEach(i.toJSON(),function(C,E){b.setRequestHeader(E,C)}),$.isUndefined(o.withCredentials)||(b.withCredentials=!!o.withCredentials),c&&c!=="json"&&(b.responseType=o.responseType),u&&([v,h]=pn(u,!0),b.addEventListener("progress",v)),l&&b.upload&&([f,g]=pn(l),b.upload.addEventListener("progress",f),b.upload.addEventListener("loadend",g)),(o.cancelToken||o.signal)&&(d=w=>{b&&(r(!w||w.type?new vt(null,e,b):w),b.abort(),b=null)},o.cancelToken&&o.cancelToken.subscribe(d),o.signal&&(o.signal.aborted?d():o.signal.addEventListener("abort",d)));const y=Bd(o.url);if(y&&ce.protocols.indexOf(y)===-1){r(new B("Unsupported protocol "+y+":",B.ERR_BAD_REQUEST,e));return}b.send(s||null)})},Yd=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,o;const s=function(u){if(!o){o=!0,c();const d=u instanceof Error?u:this.reason;r.abort(d instanceof B?d:new vt(d instanceof Error?d.message:d))}};let i=t&&setTimeout(()=>{i=null,s(new B(`timeout ${t} of ms exceeded`,B.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(s):u.removeEventListener("abort",s)}),e=null)};e.forEach(u=>u.addEventListener("abort",s));const{signal:l}=r;return l.unsubscribe=()=>$.asap(c),l}},Xd=Yd,Jd=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},Zd=async function*(e,t){for await(const n of Qd(e))yield*Jd(n,t)},Qd=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},ns=(e,t,n,r)=>{const o=Zd(e,t);let s=0,i,c=l=>{i||(i=!0,r&&r(l))};return new ReadableStream({async pull(l){try{const{done:u,value:d}=await o.next();if(u){c(),l.close();return}let f=d.byteLength;if(n){let v=s+=f;n(v)}l.enqueue(new Uint8Array(d))}catch(u){throw c(u),u}},cancel(l){return c(l),o.return()}},{highWaterMark:2})},In=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",pi=In&&typeof ReadableStream=="function",ef=In&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),mi=(e,...t)=>{try{return!!e(...t)}catch{return!1}},tf=pi&&mi(()=>{let e=!1;const t=new Request(ce.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),rs=64*1024,vr=pi&&mi(()=>$.isReadableStream(new Response("").body)),mn={stream:vr&&(e=>e.body)};In&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!mn[t]&&(mn[t]=$.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new B(`Response type '${t}' is not supported`,B.ERR_NOT_SUPPORT,r)})})})(new Response);const nf=async e=>{if(e==null)return 0;if($.isBlob(e))return e.size;if($.isSpecCompliantForm(e))return(await new Request(ce.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if($.isArrayBufferView(e)||$.isArrayBuffer(e))return e.byteLength;if($.isURLSearchParams(e)&&(e=e+""),$.isString(e))return(await ef(e)).byteLength},rf=async(e,t)=>{const n=$.toFiniteNumber(e.getContentLength());return n??nf(t)},of=In&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:c,onUploadProgress:l,responseType:u,headers:d,withCredentials:f="same-origin",fetchOptions:v}=fi(e);u=u?(u+"").toLowerCase():"text";let g=Xd([o,s&&s.toAbortSignal()],i),h;const p=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let b;try{if(l&&tf&&n!=="get"&&n!=="head"&&(b=await rf(d,r))!==0){let E=new Request(t,{method:"POST",body:r,duplex:"half"}),P;if($.isFormData(r)&&(P=E.headers.get("content-type"))&&d.setContentType(P),E.body){const[S,R]=Qo(b,pn(es(l)));r=ns(E.body,rs,S,R)}}$.isString(f)||(f=f?"include":"omit");const x="credentials"in Request.prototype;h=new Request(t,{...v,signal:g,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:x?f:void 0});let y=await fetch(h,v);const w=vr&&(u==="stream"||u==="response");if(vr&&(c||w&&p)){const E={};["status","statusText","headers"].forEach(N=>{E[N]=y[N]});const P=$.toFiniteNumber(y.headers.get("content-length")),[S,R]=c&&Qo(P,pn(es(c),!0))||[];y=new Response(ns(y.body,rs,S,()=>{R&&R(),p&&p()}),E)}u=u||"text";let C=await mn[$.findKey(mn,u)||"text"](y,e);return!w&&p&&p(),await new Promise((E,P)=>{ui(E,P,{data:C,headers:Ee.from(y.headers),status:y.status,statusText:y.statusText,config:e,request:h})})}catch(x){throw p&&p(),x&&x.name==="TypeError"&&/Load failed|fetch/i.test(x.message)?Object.assign(new B("Network Error",B.ERR_NETWORK,e,h),{cause:x.cause||x}):B.from(x,x&&x.code,e,h)}}),gr={http:bd,xhr:qd,fetch:of};$.forEach(gr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const os=e=>`- ${e}`,sf=e=>$.isFunction(e)||e===null||e===!1,hi={getAdapter:e=>{e=$.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){n=e[s];let i;if(r=n,!sf(n)&&(r=gr[(i=String(n)).toLowerCase()],r===void 0))throw new B(`Unknown adapter '${i}'`);if(r)break;o[i||"#"+s]=r}if(!r){const s=Object.entries(o).map(([c,l])=>`adapter ${c} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(os).join(`
`):" "+os(s[0]):"as no adapter specified";throw new B("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:gr};function Qn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new vt(null,e)}function ss(e){return Qn(e),e.headers=Ee.from(e.headers),e.data=Zn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),hi.getAdapter(e.adapter||so.adapter)(e).then(function(r){return Qn(e),r.data=Zn.call(e,e.transformResponse,r),r.headers=Ee.from(r.headers),r},function(r){return li(r)||(Qn(e),r&&r.response&&(r.response.data=Zn.call(e,e.transformResponse,r.response),r.response.headers=Ee.from(r.response.headers))),Promise.reject(r)})}const vi="1.11.0",Dn={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Dn[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const is={};Dn.transitional=function(t,n,r){function o(s,i){return"[Axios v"+vi+"] Transitional option '"+s+"'"+i+(r?". "+r:"")}return(s,i,c)=>{if(t===!1)throw new B(o(i," has been removed"+(n?" in "+n:"")),B.ERR_DEPRECATED);return n&&!is[i]&&(is[i]=!0,console.warn(o(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,i,c):!0}};Dn.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function af(e,t,n){if(typeof e!="object")throw new B("options must be an object",B.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const c=e[s],l=c===void 0||i(c,s,e);if(l!==!0)throw new B("option "+s+" must be "+l,B.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new B("Unknown option "+s,B.ERR_BAD_OPTION)}}const cn={assertOptions:af,validators:Dn},Pe=cn.validators;class hn{constructor(t){this.defaults=t||{},this.interceptors={request:new Jo,response:new Jo}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Xe(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:s}=n;r!==void 0&&cn.assertOptions(r,{silentJSONParsing:Pe.transitional(Pe.boolean),forcedJSONParsing:Pe.transitional(Pe.boolean),clarifyTimeoutError:Pe.transitional(Pe.boolean)},!1),o!=null&&($.isFunction(o)?n.paramsSerializer={serialize:o}:cn.assertOptions(o,{encode:Pe.function,serialize:Pe.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),cn.assertOptions(n,{baseUrl:Pe.spelling("baseURL"),withXsrfToken:Pe.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=s&&$.merge(s.common,s[n.method]);s&&$.forEach(["delete","get","head","post","put","patch","common"],h=>{delete s[h]}),n.headers=Ee.concat(i,s);const c=[];let l=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(n)===!1||(l=l&&p.synchronous,c.unshift(p.fulfilled,p.rejected))});const u=[];this.interceptors.response.forEach(function(p){u.push(p.fulfilled,p.rejected)});let d,f=0,v;if(!l){const h=[ss.bind(this),void 0];for(h.unshift(...c),h.push(...u),v=h.length,d=Promise.resolve(n);f<v;)d=d.then(h[f++],h[f++]);return d}v=c.length;let g=n;for(f=0;f<v;){const h=c[f++],p=c[f++];try{g=h(g)}catch(b){p.call(this,b);break}}try{d=ss.call(this,g)}catch(h){return Promise.reject(h)}for(f=0,v=u.length;f<v;)d=d.then(u[f++],u[f++]);return d}getUri(t){t=Xe(this.defaults,t);const n=di(t.baseURL,t.url,t.allowAbsoluteUrls);return ii(n,t.params,t.paramsSerializer)}}$.forEach(["delete","get","head","options"],function(t){hn.prototype[t]=function(n,r){return this.request(Xe(r||{},{method:t,url:n,data:(r||{}).data}))}});$.forEach(["post","put","patch"],function(t){function n(r){return function(s,i,c){return this.request(Xe(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}hn.prototype[t]=n(),hn.prototype[t+"Form"]=n(!0)});const ln=hn;class io{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(o=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](o);r._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(c=>{r.subscribe(c),s=c}).then(o);return i.cancel=function(){r.unsubscribe(s)},i},t(function(s,i,c){r.reason||(r.reason=new vt(s,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new io(function(o){t=o}),cancel:t}}}const cf=io;function lf(e){return function(n){return e.apply(null,n)}}function uf(e){return $.isObject(e)&&e.isAxiosError===!0}const br={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(br).forEach(([e,t])=>{br[t]=e});const df=br;function gi(e){const t=new ln(e),n=qs(ln.prototype.request,t);return $.extend(n,ln.prototype,t,{allOwnKeys:!0}),$.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return gi(Xe(e,o))},n}const ee=gi(so);ee.Axios=ln;ee.CanceledError=vt;ee.CancelToken=cf;ee.isCancel=li;ee.VERSION=vi;ee.toFormData=_n;ee.AxiosError=B;ee.Cancel=ee.CanceledError;ee.all=function(t){return Promise.all(t)};ee.spread=lf;ee.isAxiosError=uf;ee.mergeConfig=Xe;ee.AxiosHeaders=Ee;ee.formToJSON=e=>ci($.isHTMLForm(e)?new FormData(e):e);ee.getAdapter=hi.getAdapter;ee.HttpStatusCode=df;ee.default=ee;const bi=a.forwardRef(({className:e,type:t,...n},r)=>m.jsx("input",{type:t,className:K("flex h-10 w-full rounded-md border-2 border-[#7b7b7b7b] bg-[var(--input-background)] px-3 py-2 text-sm text-[var(--input-text)] placeholder-[var(--paragraph)] ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus:border-[var(--input-border-color)] focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));bi.displayName="Input";function vn(e,[t,n]){return Math.min(n,Math.max(t,e))}function ne(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function ao(e,t=[]){let n=[];function r(s,i){const c=a.createContext(i),l=n.length;n=[...n,i];function u(f){const{scope:v,children:g,...h}=f,p=(v==null?void 0:v[e][l])||c,b=a.useMemo(()=>h,Object.values(h));return m.jsx(p.Provider,{value:b,children:g})}function d(f,v){const g=(v==null?void 0:v[e][l])||c,h=a.useContext(g);if(h)return h;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(i=>a.createContext(i));return function(c){const l=(c==null?void 0:c[e])||s;return a.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,ff(o,...t)]}function ff(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((c,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...c,...f}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function pf(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function xi(...e){return t=>e.forEach(n=>pf(n,t))}function oe(...e){return a.useCallback(xi(...e),e)}var Mt=a.forwardRef((e,t)=>{const{children:n,...r}=e,o=a.Children.toArray(n),s=o.find(hf);if(s){const i=s.props.children,c=o.map(l=>l===s?a.Children.count(i)>1?a.Children.only(null):a.isValidElement(i)?i.props.children:null:l);return m.jsx(xr,{...r,ref:t,children:a.isValidElement(i)?a.cloneElement(i,void 0,c):null})}return m.jsx(xr,{...r,ref:t,children:n})});Mt.displayName="Slot";var xr=a.forwardRef((e,t)=>{const{children:n,...r}=e;if(a.isValidElement(n)){const o=gf(n);return a.cloneElement(n,{...vf(r,n.props),ref:t?xi(t,o):o})}return a.Children.count(n)>1?a.Children.only(null):null});xr.displayName="SlotClone";var mf=({children:e})=>m.jsx(m.Fragment,{children:e});function hf(e){return a.isValidElement(e)&&e.type===mf}function vf(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...c)=>{s(...c),o(...c)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function gf(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function bf(e){const t=e+"CollectionProvider",[n,r]=ao(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=g=>{const{scope:h,children:p}=g,b=W.useRef(null),x=W.useRef(new Map).current;return m.jsx(o,{scope:h,itemMap:x,collectionRef:b,children:p})};i.displayName=t;const c=e+"CollectionSlot",l=W.forwardRef((g,h)=>{const{scope:p,children:b}=g,x=s(c,p),y=oe(h,x.collectionRef);return m.jsx(Mt,{ref:y,children:b})});l.displayName=c;const u=e+"CollectionItemSlot",d="data-radix-collection-item",f=W.forwardRef((g,h)=>{const{scope:p,children:b,...x}=g,y=W.useRef(null),w=oe(h,y),C=s(u,p);return W.useEffect(()=>(C.itemMap.set(y,{ref:y,...x}),()=>void C.itemMap.delete(y))),m.jsx(Mt,{[d]:"",ref:w,children:b})});f.displayName=u;function v(g){const h=s(e+"CollectionConsumer",g);return W.useCallback(()=>{const b=h.collectionRef.current;if(!b)return[];const x=Array.from(b.querySelectorAll(`[${d}]`));return Array.from(h.itemMap.values()).sort((C,E)=>x.indexOf(C.ref.current)-x.indexOf(E.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:i,Slot:l,ItemSlot:f},v,r]}var xf=a.createContext(void 0);function yf(e){const t=a.useContext(xf);return e||t||"ltr"}var wf=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],te=wf.reduce((e,t)=>{const n=a.forwardRef((r,o)=>{const{asChild:s,...i}=r,c=s?Mt:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),m.jsx(c,{...i,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Cf(e,t){e&&Fe.flushSync(()=>e.dispatchEvent(t))}function De(e){const t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Ef(e,t=globalThis==null?void 0:globalThis.document){const n=De(e);a.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Sf="DismissableLayer",yr="dismissableLayer.update",$f="dismissableLayer.pointerDownOutside",Rf="dismissableLayer.focusOutside",as,yi=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),wi=a.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:c,...l}=e,u=a.useContext(yi),[d,f]=a.useState(null),v=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,g]=a.useState({}),h=oe(t,S=>f(S)),p=Array.from(u.layers),[b]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=p.indexOf(b),y=d?p.indexOf(d):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,C=y>=x,E=Nf(S=>{const R=S.target,N=[...u.branches].some(M=>M.contains(R));!C||N||(o==null||o(S),i==null||i(S),S.defaultPrevented||c==null||c())},v),P=Af(S=>{const R=S.target;[...u.branches].some(M=>M.contains(R))||(s==null||s(S),i==null||i(S),S.defaultPrevented||c==null||c())},v);return Ef(S=>{y===u.layers.size-1&&(r==null||r(S),!S.defaultPrevented&&c&&(S.preventDefault(),c()))},v),a.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(as=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),cs(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=as)}},[d,v,n,u]),a.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),cs())},[d,u]),a.useEffect(()=>{const S=()=>g({});return document.addEventListener(yr,S),()=>document.removeEventListener(yr,S)},[]),m.jsx(te.div,{...l,ref:h,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:ne(e.onFocusCapture,P.onFocusCapture),onBlurCapture:ne(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:ne(e.onPointerDownCapture,E.onPointerDownCapture)})});wi.displayName=Sf;var Pf="DismissableLayerBranch",Tf=a.forwardRef((e,t)=>{const n=a.useContext(yi),r=a.useRef(null),o=oe(t,r);return a.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),m.jsx(te.div,{...e,ref:o})});Tf.displayName=Pf;function Nf(e,t=globalThis==null?void 0:globalThis.document){const n=De(e),r=a.useRef(!1),o=a.useRef(()=>{});return a.useEffect(()=>{const s=c=>{if(c.target&&!r.current){let l=function(){Ci($f,n,u,{discrete:!0})};const u={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Af(e,t=globalThis==null?void 0:globalThis.document){const n=De(e),r=a.useRef(!1);return a.useEffect(()=>{const o=s=>{s.target&&!r.current&&Ci(Rf,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function cs(){const e=new CustomEvent(yr);document.dispatchEvent(e)}function Ci(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Cf(o,s):o.dispatchEvent(s)}var er=0;function Of(){a.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ls()),document.body.insertAdjacentElement("beforeend",e[1]??ls()),er++,()=>{er===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),er--}},[])}function ls(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var tr="focusScope.autoFocusOnMount",nr="focusScope.autoFocusOnUnmount",us={bubbles:!1,cancelable:!0},_f="FocusScope",Ei=a.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[c,l]=a.useState(null),u=De(o),d=De(s),f=a.useRef(null),v=oe(t,p=>l(p)),g=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let p=function(w){if(g.paused||!c)return;const C=w.target;c.contains(C)?f.current=C:Ue(f.current,{select:!0})},b=function(w){if(g.paused||!c)return;const C=w.relatedTarget;C!==null&&(c.contains(C)||Ue(f.current,{select:!0}))},x=function(w){if(document.activeElement===document.body)for(const E of w)E.removedNodes.length>0&&Ue(c)};document.addEventListener("focusin",p),document.addEventListener("focusout",b);const y=new MutationObserver(x);return c&&y.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",b),y.disconnect()}}},[r,c,g.paused]),a.useEffect(()=>{if(c){fs.add(g);const p=document.activeElement;if(!c.contains(p)){const x=new CustomEvent(tr,us);c.addEventListener(tr,u),c.dispatchEvent(x),x.defaultPrevented||(Mf(Lf(Si(c)),{select:!0}),document.activeElement===p&&Ue(c))}return()=>{c.removeEventListener(tr,u),setTimeout(()=>{const x=new CustomEvent(nr,us);c.addEventListener(nr,d),c.dispatchEvent(x),x.defaultPrevented||Ue(p??document.body,{select:!0}),c.removeEventListener(nr,d),fs.remove(g)},0)}}},[c,u,d,g]);const h=a.useCallback(p=>{if(!n&&!r||g.paused)return;const b=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,x=document.activeElement;if(b&&x){const y=p.currentTarget,[w,C]=If(y);w&&C?!p.shiftKey&&x===C?(p.preventDefault(),n&&Ue(w,{select:!0})):p.shiftKey&&x===w&&(p.preventDefault(),n&&Ue(C,{select:!0})):x===y&&p.preventDefault()}},[n,r,g.paused]);return m.jsx(te.div,{tabIndex:-1,...i,ref:v,onKeyDown:h})});Ei.displayName=_f;function Mf(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Ue(r,{select:t}),document.activeElement!==n)return}function If(e){const t=Si(e),n=ds(t,e),r=ds(t.reverse(),e);return[n,r]}function Si(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ds(e,t){for(const n of e)if(!Df(n,{upTo:t}))return n}function Df(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function jf(e){return e instanceof HTMLInputElement&&"select"in e}function Ue(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&jf(e)&&t&&e.select()}}var fs=kf();function kf(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=ps(e,t),e.unshift(t)},remove(t){var n;e=ps(e,t),(n=e[0])==null||n.resume()}}}function ps(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Lf(e){return e.filter(t=>t.tagName!=="A")}var ge=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{},Ff=Tn.useId||(()=>{}),Bf=0;function co(e){const[t,n]=a.useState(Ff());return ge(()=>{e||n(r=>r??String(Bf++))},[e]),e||(t?`radix-${t}`:"")}const Uf=["top","right","bottom","left"],Ve=Math.min,pe=Math.max,gn=Math.round,qt=Math.floor,ze=e=>({x:e,y:e}),Wf={left:"right",right:"left",bottom:"top",top:"bottom"},Hf={start:"end",end:"start"};function wr(e,t,n){return pe(e,Ve(t,n))}function je(e,t){return typeof e=="function"?e(t):e}function ke(e){return e.split("-")[0]}function gt(e){return e.split("-")[1]}function lo(e){return e==="x"?"y":"x"}function uo(e){return e==="y"?"height":"width"}function bt(e){return["top","bottom"].includes(ke(e))?"y":"x"}function fo(e){return lo(bt(e))}function Vf(e,t,n){n===void 0&&(n=!1);const r=gt(e),o=fo(e),s=uo(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=bn(i)),[i,bn(i)]}function zf(e){const t=bn(e);return[Cr(e),t,Cr(t)]}function Cr(e){return e.replace(/start|end/g,t=>Hf[t])}function Kf(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function Gf(e,t,n,r){const o=gt(e);let s=Kf(ke(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(Cr)))),s}function bn(e){return e.replace(/left|right|bottom|top/g,t=>Wf[t])}function qf(e){return{top:0,right:0,bottom:0,left:0,...e}}function $i(e){return typeof e!="number"?qf(e):{top:e,right:e,bottom:e,left:e}}function xn(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function ms(e,t,n){let{reference:r,floating:o}=e;const s=bt(t),i=fo(t),c=uo(i),l=ke(t),u=s==="y",d=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,v=r[c]/2-o[c]/2;let g;switch(l){case"top":g={x:d,y:r.y-o.height};break;case"bottom":g={x:d,y:r.y+r.height};break;case"right":g={x:r.x+r.width,y:f};break;case"left":g={x:r.x-o.width,y:f};break;default:g={x:r.x,y:r.y}}switch(gt(t)){case"start":g[i]-=v*(n&&u?-1:1);break;case"end":g[i]+=v*(n&&u?-1:1);break}return g}const Yf=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,c=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:f}=ms(u,r,l),v=r,g={},h=0;for(let p=0;p<c.length;p++){const{name:b,fn:x}=c[p],{x:y,y:w,data:C,reset:E}=await x({x:d,y:f,initialPlacement:r,placement:v,strategy:o,middlewareData:g,rects:u,platform:i,elements:{reference:e,floating:t}});d=y??d,f=w??f,g={...g,[b]:{...g[b],...C}},E&&h<=50&&(h++,typeof E=="object"&&(E.placement&&(v=E.placement),E.rects&&(u=E.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:d,y:f}=ms(u,v,l)),p=-1)}return{x:d,y:f,placement:v,strategy:o,middlewareData:g}};async function It(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:c,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:v=!1,padding:g=0}=je(t,e),h=$i(g),b=c[v?f==="floating"?"reference":"floating":f],x=xn(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(b)))==null||n?b:b.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(c.floating)),boundary:u,rootBoundary:d,strategy:l})),y=f==="floating"?{...i.floating,x:r,y:o}:i.reference,w=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c.floating)),C=await(s.isElement==null?void 0:s.isElement(w))?await(s.getScale==null?void 0:s.getScale(w))||{x:1,y:1}:{x:1,y:1},E=xn(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:y,offsetParent:w,strategy:l}):y);return{top:(x.top-E.top+h.top)/C.y,bottom:(E.bottom-x.bottom+h.bottom)/C.y,left:(x.left-E.left+h.left)/C.x,right:(E.right-x.right+h.right)/C.x}}const Xf=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:c,middlewareData:l}=t,{element:u,padding:d=0}=je(e,t)||{};if(u==null)return{};const f=$i(d),v={x:n,y:r},g=fo(o),h=uo(g),p=await i.getDimensions(u),b=g==="y",x=b?"top":"left",y=b?"bottom":"right",w=b?"clientHeight":"clientWidth",C=s.reference[h]+s.reference[g]-v[g]-s.floating[h],E=v[g]-s.reference[g],P=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let S=P?P[w]:0;(!S||!await(i.isElement==null?void 0:i.isElement(P)))&&(S=c.floating[w]||s.floating[h]);const R=C/2-E/2,N=S/2-p[h]/2-1,M=Ve(f[x],N),D=Ve(f[y],N),I=M,F=S-p[h]-D,k=S/2-p[h]/2+R,A=wr(I,k,F),j=!l.arrow&&gt(o)!=null&&k!==A&&s.reference[h]/2-(k<I?M:D)-p[h]/2<0,O=j?k<I?k-I:k-F:0;return{[g]:v[g]+O,data:{[g]:A,centerOffset:k-A-O,...j&&{alignmentOffset:O}},reset:j}}}),Jf=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:c,platform:l,elements:u}=t,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:v,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:p=!0,...b}=je(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const x=ke(o),y=ke(c)===c,w=await(l.isRTL==null?void 0:l.isRTL(u.floating)),C=v||(y||!p?[bn(c)]:zf(c));!v&&h!=="none"&&C.push(...Gf(c,p,h,w));const E=[c,...C],P=await It(t,b),S=[];let R=((r=s.flip)==null?void 0:r.overflows)||[];if(d&&S.push(P[x]),f){const I=Vf(o,i,w);S.push(P[I[0]],P[I[1]])}if(R=[...R,{placement:o,overflows:S}],!S.every(I=>I<=0)){var N,M;const I=(((N=s.flip)==null?void 0:N.index)||0)+1,F=E[I];if(F)return{data:{index:I,overflows:R},reset:{placement:F}};let k=(M=R.filter(A=>A.overflows[0]<=0).sort((A,j)=>A.overflows[1]-j.overflows[1])[0])==null?void 0:M.placement;if(!k)switch(g){case"bestFit":{var D;const A=(D=R.map(j=>[j.placement,j.overflows.filter(O=>O>0).reduce((O,L)=>O+L,0)]).sort((j,O)=>j[1]-O[1])[0])==null?void 0:D[0];A&&(k=A);break}case"initialPlacement":k=c;break}if(o!==k)return{reset:{placement:k}}}return{}}}};function hs(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function vs(e){return Uf.some(t=>e[t]>=0)}const Zf=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=je(e,t);switch(r){case"referenceHidden":{const s=await It(t,{...o,elementContext:"reference"}),i=hs(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:vs(i)}}}case"escaped":{const s=await It(t,{...o,altBoundary:!0}),i=hs(s,n.floating);return{data:{escapedOffsets:i,escaped:vs(i)}}}default:return{}}}}};async function Qf(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=ke(n),c=gt(n),l=bt(n)==="y",u=["left","top"].includes(i)?-1:1,d=s&&l?-1:1,f=je(t,e);let{mainAxis:v,crossAxis:g,alignmentAxis:h}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...f};return c&&typeof h=="number"&&(g=c==="end"?h*-1:h),l?{x:g*d,y:v*u}:{x:v*u,y:g*d}}const po=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:c}=t,l=await Qf(t,e);return i===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:i}}}}},ep=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:c={fn:b=>{let{x,y}=b;return{x,y}}},...l}=je(e,t),u={x:n,y:r},d=await It(t,l),f=bt(ke(o)),v=lo(f);let g=u[v],h=u[f];if(s){const b=v==="y"?"top":"left",x=v==="y"?"bottom":"right",y=g+d[b],w=g-d[x];g=wr(y,g,w)}if(i){const b=f==="y"?"top":"left",x=f==="y"?"bottom":"right",y=h+d[b],w=h-d[x];h=wr(y,h,w)}const p=c.fn({...t,[v]:g,[f]:h});return{...p,data:{x:p.x-n,y:p.y-r}}}}},tp=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:c=0,mainAxis:l=!0,crossAxis:u=!0}=je(e,t),d={x:n,y:r},f=bt(o),v=lo(f);let g=d[v],h=d[f];const p=je(c,t),b=typeof p=="number"?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(l){const w=v==="y"?"height":"width",C=s.reference[v]-s.floating[w]+b.mainAxis,E=s.reference[v]+s.reference[w]-b.mainAxis;g<C?g=C:g>E&&(g=E)}if(u){var x,y;const w=v==="y"?"width":"height",C=["top","left"].includes(ke(o)),E=s.reference[f]-s.floating[w]+(C&&((x=i.offset)==null?void 0:x[f])||0)+(C?0:b.crossAxis),P=s.reference[f]+s.reference[w]+(C?0:((y=i.offset)==null?void 0:y[f])||0)-(C?b.crossAxis:0);h<E?h=E:h>P&&(h=P)}return{[v]:g,[f]:h}}}},np=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){const{placement:n,rects:r,platform:o,elements:s}=t,{apply:i=()=>{},...c}=je(e,t),l=await It(t,c),u=ke(n),d=gt(n),f=bt(n)==="y",{width:v,height:g}=r.floating;let h,p;u==="top"||u==="bottom"?(h=u,p=d===(await(o.isRTL==null?void 0:o.isRTL(s.floating))?"start":"end")?"left":"right"):(p=u,h=d==="end"?"top":"bottom");const b=g-l[h],x=v-l[p],y=!t.middlewareData.shift;let w=b,C=x;if(f){const P=v-l.left-l.right;C=d||y?Ve(x,P):P}else{const P=g-l.top-l.bottom;w=d||y?Ve(b,P):P}if(y&&!d){const P=pe(l.left,0),S=pe(l.right,0),R=pe(l.top,0),N=pe(l.bottom,0);f?C=v-2*(P!==0||S!==0?P+S:pe(l.left,l.right)):w=g-2*(R!==0||N!==0?R+N:pe(l.top,l.bottom))}await i({...t,availableWidth:C,availableHeight:w});const E=await o.getDimensions(s.floating);return v!==E.width||g!==E.height?{reset:{rects:!0}}:{}}}};function Ke(e){return Ri(e)?(e.nodeName||"").toLowerCase():"#document"}function he(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Be(e){var t;return(t=(Ri(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Ri(e){return e instanceof Node||e instanceof he(e).Node}function Le(e){return e instanceof Element||e instanceof he(e).Element}function Ne(e){return e instanceof HTMLElement||e instanceof he(e).HTMLElement}function gs(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof he(e).ShadowRoot}function Ut(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=be(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function rp(e){return["table","td","th"].includes(Ke(e))}function mo(e){const t=ho(),n=be(e);return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function op(e){let t=dt(e);for(;Ne(t)&&!jn(t);){if(mo(t))return t;t=dt(t)}return null}function ho(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function jn(e){return["html","body","#document"].includes(Ke(e))}function be(e){return he(e).getComputedStyle(e)}function kn(e){return Le(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function dt(e){if(Ke(e)==="html")return e;const t=e.assignedSlot||e.parentNode||gs(e)&&e.host||Be(e);return gs(t)?t.host:t}function Pi(e){const t=dt(e);return jn(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ne(t)&&Ut(t)?t:Pi(t)}function Dt(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Pi(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=he(o);return s?t.concat(i,i.visualViewport||[],Ut(o)?o:[],i.frameElement&&n?Dt(i.frameElement):[]):t.concat(o,Dt(o,[],n))}function Ti(e){const t=be(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Ne(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,c=gn(n)!==s||gn(r)!==i;return c&&(n=s,r=i),{width:n,height:r,$:c}}function vo(e){return Le(e)?e:e.contextElement}function lt(e){const t=vo(e);if(!Ne(t))return ze(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=Ti(t);let i=(s?gn(n.width):n.width)/r,c=(s?gn(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const sp=ze(0);function Ni(e){const t=he(e);return!ho()||!t.visualViewport?sp:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function ip(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==he(e)?!1:t}function Je(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=vo(e);let i=ze(1);t&&(r?Le(r)&&(i=lt(r)):i=lt(e));const c=ip(s,n,r)?Ni(s):ze(0);let l=(o.left+c.x)/i.x,u=(o.top+c.y)/i.y,d=o.width/i.x,f=o.height/i.y;if(s){const v=he(s),g=r&&Le(r)?he(r):r;let h=v,p=h.frameElement;for(;p&&r&&g!==h;){const b=lt(p),x=p.getBoundingClientRect(),y=be(p),w=x.left+(p.clientLeft+parseFloat(y.paddingLeft))*b.x,C=x.top+(p.clientTop+parseFloat(y.paddingTop))*b.y;l*=b.x,u*=b.y,d*=b.x,f*=b.y,l+=w,u+=C,h=he(p),p=h.frameElement}}return xn({width:d,height:f,x:l,y:u})}const ap=[":popover-open",":modal"];function Ai(e){return ap.some(t=>{try{return e.matches(t)}catch{return!1}})}function cp(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Be(r),c=t?Ai(t.floating):!1;if(r===i||c&&s)return n;let l={scrollLeft:0,scrollTop:0},u=ze(1);const d=ze(0),f=Ne(r);if((f||!f&&!s)&&((Ke(r)!=="body"||Ut(i))&&(l=kn(r)),Ne(r))){const v=Je(r);u=lt(r),d.x=v.x+r.clientLeft,d.y=v.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+d.x,y:n.y*u.y-l.scrollTop*u.y+d.y}}function lp(e){return Array.from(e.getClientRects())}function Oi(e){return Je(Be(e)).left+kn(e).scrollLeft}function up(e){const t=Be(e),n=kn(e),r=e.ownerDocument.body,o=pe(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=pe(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Oi(e);const c=-n.scrollTop;return be(r).direction==="rtl"&&(i+=pe(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:c}}function dp(e,t){const n=he(e),r=Be(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,c=0,l=0;if(o){s=o.width,i=o.height;const u=ho();(!u||u&&t==="fixed")&&(c=o.offsetLeft,l=o.offsetTop)}return{width:s,height:i,x:c,y:l}}function fp(e,t){const n=Je(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Ne(e)?lt(e):ze(1),i=e.clientWidth*s.x,c=e.clientHeight*s.y,l=o*s.x,u=r*s.y;return{width:i,height:c,x:l,y:u}}function bs(e,t,n){let r;if(t==="viewport")r=dp(e,n);else if(t==="document")r=up(Be(e));else if(Le(t))r=fp(t,n);else{const o=Ni(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return xn(r)}function _i(e,t){const n=dt(e);return n===t||!Le(n)||jn(n)?!1:be(n).position==="fixed"||_i(n,t)}function pp(e,t){const n=t.get(e);if(n)return n;let r=Dt(e,[],!1).filter(c=>Le(c)&&Ke(c)!=="body"),o=null;const s=be(e).position==="fixed";let i=s?dt(e):e;for(;Le(i)&&!jn(i);){const c=be(i),l=mo(i);!l&&c.position==="fixed"&&(o=null),(s?!l&&!o:!l&&c.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Ut(i)&&!l&&_i(e,i))?r=r.filter(d=>d!==i):o=c,i=dt(i)}return t.set(e,r),r}function mp(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?pp(t,this._c):[].concat(n),r],c=i[0],l=i.reduce((u,d)=>{const f=bs(t,d,o);return u.top=pe(f.top,u.top),u.right=Ve(f.right,u.right),u.bottom=Ve(f.bottom,u.bottom),u.left=pe(f.left,u.left),u},bs(t,c,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function hp(e){const{width:t,height:n}=Ti(e);return{width:t,height:n}}function vp(e,t,n){const r=Ne(t),o=Be(t),s=n==="fixed",i=Je(e,!0,s,t);let c={scrollLeft:0,scrollTop:0};const l=ze(0);if(r||!r&&!s)if((Ke(t)!=="body"||Ut(o))&&(c=kn(t)),r){const f=Je(t,!0,s,t);l.x=f.x+t.clientLeft,l.y=f.y+t.clientTop}else o&&(l.x=Oi(o));const u=i.left+c.scrollLeft-l.x,d=i.top+c.scrollTop-l.y;return{x:u,y:d,width:i.width,height:i.height}}function xs(e,t){return!Ne(e)||be(e).position==="fixed"?null:t?t(e):e.offsetParent}function Mi(e,t){const n=he(e);if(!Ne(e)||Ai(e))return n;let r=xs(e,t);for(;r&&rp(r)&&be(r).position==="static";)r=xs(r,t);return r&&(Ke(r)==="html"||Ke(r)==="body"&&be(r).position==="static"&&!mo(r))?n:r||op(e)||n}const gp=async function(e){const t=this.getOffsetParent||Mi,n=this.getDimensions;return{reference:vp(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,...await n(e.floating)}}};function bp(e){return be(e).direction==="rtl"}const xp={convertOffsetParentRelativeRectToViewportRelativeRect:cp,getDocumentElement:Be,getClippingRect:mp,getOffsetParent:Mi,getElementRects:gp,getClientRects:lp,getDimensions:hp,getScale:lt,isElement:Le,isRTL:bp};function yp(e,t){let n=null,r;const o=Be(e);function s(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function i(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),s();const{left:u,top:d,width:f,height:v}=e.getBoundingClientRect();if(c||t(),!f||!v)return;const g=qt(d),h=qt(o.clientWidth-(u+f)),p=qt(o.clientHeight-(d+v)),b=qt(u),y={rootMargin:-g+"px "+-h+"px "+-p+"px "+-b+"px",threshold:pe(0,Ve(1,l))||1};let w=!0;function C(E){const P=E[0].intersectionRatio;if(P!==l){if(!w)return i();P?i(!1,P):r=setTimeout(()=>{i(!1,1e-7)},100)}w=!1}try{n=new IntersectionObserver(C,{...y,root:o.ownerDocument})}catch{n=new IntersectionObserver(C,y)}n.observe(e)}return i(!0),s}function go(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=vo(e),d=o||s?[...u?Dt(u):[],...Dt(t)]:[];d.forEach(x=>{o&&x.addEventListener("scroll",n,{passive:!0}),s&&x.addEventListener("resize",n)});const f=u&&c?yp(u,n):null;let v=-1,g=null;i&&(g=new ResizeObserver(x=>{let[y]=x;y&&y.target===u&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var w;(w=g)==null||w.observe(t)})),n()}),u&&!l&&g.observe(u),g.observe(t));let h,p=l?Je(e):null;l&&b();function b(){const x=Je(e);p&&(x.x!==p.x||x.y!==p.y||x.width!==p.width||x.height!==p.height)&&n(),p=x,h=requestAnimationFrame(b)}return n(),()=>{var x;d.forEach(y=>{o&&y.removeEventListener("scroll",n),s&&y.removeEventListener("resize",n)}),f==null||f(),(x=g)==null||x.disconnect(),g=null,l&&cancelAnimationFrame(h)}}const bo=ep,xo=Jf,yo=np,wo=Zf,ys=Xf,Co=tp,wp=(e,t,n)=>{const r=new Map,o={platform:xp,...n},s={...o.platform,_c:r};return Yf(e,t,{...o,platform:s})},Eo=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?ys({element:r.current,padding:o}).fn(n):{}:r?ys({element:r,padding:o}).fn(n):{}}}};var un=typeof document<"u"?a.useLayoutEffect:a.useEffect;function yn(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!yn(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!yn(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Ii(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ws(e,t){const n=Ii(e);return Math.round(t*n)/n}function Cs(e){const t=a.useRef(e);return un(()=>{t.current=e}),t}function So(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:c=!0,whileElementsMounted:l,open:u}=e,[d,f]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,g]=a.useState(r);yn(v,r)||g(r);const[h,p]=a.useState(null),[b,x]=a.useState(null),y=a.useCallback(O=>{O!==P.current&&(P.current=O,p(O))},[]),w=a.useCallback(O=>{O!==S.current&&(S.current=O,x(O))},[]),C=s||h,E=i||b,P=a.useRef(null),S=a.useRef(null),R=a.useRef(d),N=l!=null,M=Cs(l),D=Cs(o),I=a.useCallback(()=>{if(!P.current||!S.current)return;const O={placement:t,strategy:n,middleware:v};D.current&&(O.platform=D.current),wp(P.current,S.current,O).then(L=>{const T={...L,isPositioned:!0};F.current&&!yn(R.current,T)&&(R.current=T,Fe.flushSync(()=>{f(T)}))})},[v,t,n,D]);un(()=>{u===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,f(O=>({...O,isPositioned:!1})))},[u]);const F=a.useRef(!1);un(()=>(F.current=!0,()=>{F.current=!1}),[]),un(()=>{if(C&&(P.current=C),E&&(S.current=E),C&&E){if(M.current)return M.current(C,E,I);I()}},[C,E,I,M,N]);const k=a.useMemo(()=>({reference:P,floating:S,setReference:y,setFloating:w}),[y,w]),A=a.useMemo(()=>({reference:C,floating:E}),[C,E]),j=a.useMemo(()=>{const O={position:n,left:0,top:0};if(!A.floating)return O;const L=ws(A.floating,d.x),T=ws(A.floating,d.y);return c?{...O,transform:"translate("+L+"px, "+T+"px)",...Ii(A.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:L,top:T}},[n,c,A.floating,d.x,d.y]);return a.useMemo(()=>({...d,update:I,refs:k,elements:A,floatingStyles:j}),[d,I,k,A,j])}var Cp="Arrow",Di=a.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return m.jsx(te.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:m.jsx("polygon",{points:"0,0 30,0 15,10"})})});Di.displayName=Cp;var Ep=Di;function Sp(e){const[t,n]=a.useState(void 0);return ge(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;i=u.inlineSize,c=u.blockSize}else i=e.offsetWidth,c=e.offsetHeight;n({width:i,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var $o="Popper",[ji,ki]=ao($o),[$p,Li]=ji($o),Fi=e=>{const{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return m.jsx($p,{scope:t,anchor:r,onAnchorChange:o,children:n})};Fi.displayName=$o;var Bi="PopperAnchor",Ui=a.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=Li(Bi,n),i=a.useRef(null),c=oe(t,i);return a.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:m.jsx(te.div,{...o,ref:c})});Ui.displayName=Bi;var Ro="PopperContent",[Rp,Pp]=ji(Ro),Wi=a.forwardRef((e,t)=>{var _,G,H,V,q,Y;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:f="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:h,...p}=e,b=Li(Ro,n),[x,y]=a.useState(null),w=oe(t,Q=>y(Q)),[C,E]=a.useState(null),P=Sp(C),S=(P==null?void 0:P.width)??0,R=(P==null?void 0:P.height)??0,N=r+(s!=="center"?"-"+s:""),M=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},D=Array.isArray(u)?u:[u],I=D.length>0,F={padding:M,boundary:D.filter(Np),altBoundary:I},{refs:k,floatingStyles:A,placement:j,isPositioned:O,middlewareData:L}=So({strategy:"fixed",placement:N,whileElementsMounted:(...Q)=>go(...Q,{animationFrame:g==="always"}),elements:{reference:b.anchor},middleware:[po({mainAxis:o+R,alignmentAxis:i}),l&&bo({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?Co():void 0,...F}),l&&xo({...F}),yo({...F,apply:({elements:Q,rects:ue,availableWidth:fe,availableHeight:Re})=>{const{width:Oe,height:St}=ue.reference,Ce=Q.floating.style;Ce.setProperty("--radix-popper-available-width",`${fe}px`),Ce.setProperty("--radix-popper-available-height",`${Re}px`),Ce.setProperty("--radix-popper-anchor-width",`${Oe}px`),Ce.setProperty("--radix-popper-anchor-height",`${St}px`)}}),C&&Eo({element:C,padding:c}),Ap({arrowWidth:S,arrowHeight:R}),v&&wo({strategy:"referenceHidden",...F})]}),[T,J]=zi(j),X=De(h);ge(()=>{O&&(X==null||X())},[O,X]);const re=(_=L.arrow)==null?void 0:_.x,se=(G=L.arrow)==null?void 0:G.y,ie=((H=L.arrow)==null?void 0:H.centerOffset)!==0,[ae,Z]=a.useState();return ge(()=>{x&&Z(window.getComputedStyle(x).zIndex)},[x]),m.jsx("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...A,transform:O?A.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ae,"--radix-popper-transform-origin":[(V=L.transformOrigin)==null?void 0:V.x,(q=L.transformOrigin)==null?void 0:q.y].join(" "),...((Y=L.hide)==null?void 0:Y.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:m.jsx(Rp,{scope:n,placedSide:T,onArrowChange:E,arrowX:re,arrowY:se,shouldHideArrow:ie,children:m.jsx(te.div,{"data-side":T,"data-align":J,...p,ref:w,style:{...p.style,animation:O?void 0:"none"}})})})});Wi.displayName=Ro;var Hi="PopperArrow",Tp={top:"bottom",right:"left",bottom:"top",left:"right"},Vi=a.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=Pp(Hi,r),i=Tp[s.placedSide];return m.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:m.jsx(Ep,{...o,ref:n,style:{...o.style,display:"block"}})})});Vi.displayName=Hi;function Np(e){return e!==null}var Ap=e=>({name:"transformOrigin",options:e,fn(t){var b,x,y;const{placement:n,rects:r,middlewareData:o}=t,i=((b=o.arrow)==null?void 0:b.centerOffset)!==0,c=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,d]=zi(n),f={start:"0%",center:"50%",end:"100%"}[d],v=(((x=o.arrow)==null?void 0:x.x)??0)+c/2,g=(((y=o.arrow)==null?void 0:y.y)??0)+l/2;let h="",p="";return u==="bottom"?(h=i?f:`${v}px`,p=`${-l}px`):u==="top"?(h=i?f:`${v}px`,p=`${r.floating.height+l}px`):u==="right"?(h=`${-l}px`,p=i?f:`${g}px`):u==="left"&&(h=`${r.floating.width+l}px`,p=i?f:`${g}px`),{data:{x:h,y:p}}}});function zi(e){const[t,n="center"]=e.split("-");return[t,n]}var Op=Fi,_p=Ui,Mp=Wi,Ip=Vi,Dp="Portal",Ki=a.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[o,s]=a.useState(!1);ge(()=>s(!0),[]);const i=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return i?Vs.createPortal(m.jsx(te.div,{...r,ref:t}),i):null});Ki.displayName=Dp;function Es({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=jp({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,c=De(n),l=a.useCallback(u=>{if(s){const f=typeof u=="function"?u(e):u;f!==e&&c(f)}else o(u)},[s,e,o,c]);return[i,l]}function jp({defaultProp:e,onChange:t}){const n=a.useState(e),[r]=n,o=a.useRef(r),s=De(t);return a.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}function Gi(e){const t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var kp=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},rt=new WeakMap,Yt=new WeakMap,Xt={},rr=0,qi=function(e){return e&&(e.host||qi(e.parentNode))},Lp=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=qi(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Fp=function(e,t,n,r){var o=Lp(t,Array.isArray(e)?e:[e]);Xt[n]||(Xt[n]=new WeakMap);var s=Xt[n],i=[],c=new Set,l=new Set(o),u=function(f){!f||c.has(f)||(c.add(f),u(f.parentNode))};o.forEach(u);var d=function(f){!f||l.has(f)||Array.prototype.forEach.call(f.children,function(v){if(c.has(v))d(v);else{var g=v.getAttribute(r),h=g!==null&&g!=="false",p=(rt.get(v)||0)+1,b=(s.get(v)||0)+1;rt.set(v,p),s.set(v,b),i.push(v),p===1&&h&&Yt.set(v,!0),b===1&&v.setAttribute(n,"true"),h||v.setAttribute(r,"true")}})};return d(t),c.clear(),rr++,function(){i.forEach(function(f){var v=rt.get(f)-1,g=s.get(f)-1;rt.set(f,v),s.set(f,g),v||(Yt.has(f)||f.removeAttribute(r),Yt.delete(f)),g||f.removeAttribute(n)}),rr--,rr||(rt=new WeakMap,rt=new WeakMap,Yt=new WeakMap,Xt={})}},Yi=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||kp(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Fp(r,o,n,"aria-hidden")):function(){return null}},le=function(){return le=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},le.apply(this,arguments)};function Po(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Xi(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var Pt="right-scroll-bar-position",Tt="width-before-scroll-bar",Bp="with-scroll-bars-hidden",Up="--removed-body-scroll-bar-size";function or(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Wp(e,t){var n=a.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Hp=typeof window<"u"?a.useLayoutEffect:a.useEffect,Ss=new WeakMap;function Ji(e,t){var n=Wp(t||null,function(r){return e.forEach(function(o){return or(o,r)})});return Hp(function(){var r=Ss.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(c){s.has(c)||or(c,null)}),s.forEach(function(c){o.has(c)||or(c,i)})}Ss.set(n,e)},[e]),n}function Vp(e){return e}function zp(e,t){t===void 0&&(t=Vp);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(c){return c!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(c){return s(c)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var c=n;n=[],c.forEach(s),i=n}var l=function(){var d=i;i=[],d.forEach(s)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(d){i.push(d),u()},filter:function(d){return i=i.filter(d),n}}}};return o}function Zi(e){e===void 0&&(e={});var t=zp(null);return t.options=le({async:!0,ssr:!1},e),t}var Qi=function(e){var t=e.sideCar,n=Po(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return a.createElement(r,le({},n))};Qi.isSideCarExport=!0;function ea(e,t){return e.useMedium(t),Qi}var ta=Zi(),sr=function(){},Ln=a.forwardRef(function(e,t){var n=a.useRef(null),r=a.useState({onScrollCapture:sr,onWheelCapture:sr,onTouchMoveCapture:sr}),o=r[0],s=r[1],i=e.forwardProps,c=e.children,l=e.className,u=e.removeScrollBar,d=e.enabled,f=e.shards,v=e.sideCar,g=e.noIsolation,h=e.inert,p=e.allowPinchZoom,b=e.as,x=b===void 0?"div":b,y=e.gapMode,w=Po(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=v,E=Ji([n,t]),P=le(le({},w),o);return a.createElement(a.Fragment,null,d&&a.createElement(C,{sideCar:ta,removeScrollBar:u,shards:f,noIsolation:g,inert:h,setCallbacks:s,allowPinchZoom:!!p,lockRef:n,gapMode:y}),i?a.cloneElement(a.Children.only(c),le(le({},P),{ref:E})):a.createElement(x,le({},P,{className:l,ref:E}),c))});Ln.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ln.classNames={fullWidth:Tt,zeroRight:Pt};var Kp=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Gp(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Kp();return t&&e.setAttribute("nonce",t),e}function qp(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Yp(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Xp=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Gp())&&(qp(t,n),Yp(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Jp=function(){var e=Xp();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},To=function(){var e=Jp(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Zp={left:0,top:0,right:0,gap:0},ir=function(e){return parseInt(e||"",10)||0},Qp=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[ir(n),ir(r),ir(o)]},em=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Zp;var t=Qp(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tm=To(),ut="data-scroll-locked",nm=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Bp,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(ut,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Pt,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Tt,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Pt," .").concat(Pt,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Tt," .").concat(Tt,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(ut,`] {
    `).concat(Up,": ").concat(c,`px;
  }
`)},$s=function(){var e=parseInt(document.body.getAttribute(ut)||"0",10);return isFinite(e)?e:0},rm=function(){a.useEffect(function(){return document.body.setAttribute(ut,($s()+1).toString()),function(){var e=$s()-1;e<=0?document.body.removeAttribute(ut):document.body.setAttribute(ut,e.toString())}},[])},na=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;rm();var s=a.useMemo(function(){return em(o)},[o]);return a.createElement(tm,{styles:nm(s,!t,o,n?"":"!important")})},Er=!1;if(typeof window<"u")try{var Jt=Object.defineProperty({},"passive",{get:function(){return Er=!0,!0}});window.addEventListener("test",Jt,Jt),window.removeEventListener("test",Jt,Jt)}catch{Er=!1}var ot=Er?{passive:!1}:!1,om=function(e){return e.tagName==="TEXTAREA"},ra=function(e,t){var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!om(e)&&n[t]==="visible")},sm=function(e){return ra(e,"overflowY")},im=function(e){return ra(e,"overflowX")},Rs=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=oa(e,r);if(o){var s=sa(e,r),i=s[1],c=s[2];if(i>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},am=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},cm=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},oa=function(e,t){return e==="v"?sm(t):im(t)},sa=function(e,t){return e==="v"?am(t):cm(t)},lm=function(e,t){return e==="h"&&t==="rtl"?-1:1},um=function(e,t,n,r,o){var s=lm(e,window.getComputedStyle(t).direction),i=s*r,c=n.target,l=t.contains(c),u=!1,d=i>0,f=0,v=0;do{var g=sa(e,c),h=g[0],p=g[1],b=g[2],x=p-b-s*h;(h||x)&&oa(e,c)&&(f+=x,v+=h),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return(d&&(o&&Math.abs(f)<1||!o&&i>f)||!d&&(o&&Math.abs(v)<1||!o&&-i>v))&&(u=!0),u},Zt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Ps=function(e){return[e.deltaX,e.deltaY]},Ts=function(e){return e&&"current"in e?e.current:e},dm=function(e,t){return e[0]===t[0]&&e[1]===t[1]},fm=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},pm=0,st=[];function mm(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(pm++)[0],s=a.useState(To)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var p=Xi([e.lockRef.current],(e.shards||[]).map(Ts),!0).filter(Boolean);return p.forEach(function(b){return b.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),p.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=a.useCallback(function(p,b){if("touches"in p&&p.touches.length===2)return!i.current.allowPinchZoom;var x=Zt(p),y=n.current,w="deltaX"in p?p.deltaX:y[0]-x[0],C="deltaY"in p?p.deltaY:y[1]-x[1],E,P=p.target,S=Math.abs(w)>Math.abs(C)?"h":"v";if("touches"in p&&S==="h"&&P.type==="range")return!1;var R=Rs(S,P);if(!R)return!0;if(R?E=S:(E=S==="v"?"h":"v",R=Rs(S,P)),!R)return!1;if(!r.current&&"changedTouches"in p&&(w||C)&&(r.current=E),!E)return!0;var N=r.current||E;return um(N,b,p,N==="h"?w:C,!0)},[]),l=a.useCallback(function(p){var b=p;if(!(!st.length||st[st.length-1]!==s)){var x="deltaY"in b?Ps(b):Zt(b),y=t.current.filter(function(E){return E.name===b.type&&(E.target===b.target||b.target===E.shadowParent)&&dm(E.delta,x)})[0];if(y&&y.should){b.cancelable&&b.preventDefault();return}if(!y){var w=(i.current.shards||[]).map(Ts).filter(Boolean).filter(function(E){return E.contains(b.target)}),C=w.length>0?c(b,w[0]):!i.current.noIsolation;C&&b.cancelable&&b.preventDefault()}}},[]),u=a.useCallback(function(p,b,x,y){var w={name:p,delta:b,target:x,should:y,shadowParent:hm(x)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(C){return C!==w})},1)},[]),d=a.useCallback(function(p){n.current=Zt(p),r.current=void 0},[]),f=a.useCallback(function(p){u(p.type,Ps(p),p.target,c(p,e.lockRef.current))},[]),v=a.useCallback(function(p){u(p.type,Zt(p),p.target,c(p,e.lockRef.current))},[]);a.useEffect(function(){return st.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",l,ot),document.addEventListener("touchmove",l,ot),document.addEventListener("touchstart",d,ot),function(){st=st.filter(function(p){return p!==s}),document.removeEventListener("wheel",l,ot),document.removeEventListener("touchmove",l,ot),document.removeEventListener("touchstart",d,ot)}},[]);var g=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(s,{styles:fm(o)}):null,g?a.createElement(na,{gapMode:e.gapMode}):null)}function hm(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const vm=ea(ta,mm);var ia=a.forwardRef(function(e,t){return a.createElement(Ln,le({},e,{ref:t,sideCar:vm}))});ia.classNames=Ln.classNames;const gm=ia;var bm=[" ","Enter","ArrowUp","ArrowDown"],xm=[" ","Enter"],Wt="Select",[Fn,Bn,ym]=bf(Wt),[xt,dy]=ao(Wt,[ym,ki]),Un=ki(),[wm,Ge]=xt(Wt),[Cm,Em]=xt(Wt),aa=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:s,value:i,defaultValue:c,onValueChange:l,dir:u,name:d,autoComplete:f,disabled:v,required:g}=e,h=Un(t),[p,b]=a.useState(null),[x,y]=a.useState(null),[w,C]=a.useState(!1),E=yf(u),[P=!1,S]=Es({prop:r,defaultProp:o,onChange:s}),[R,N]=Es({prop:i,defaultProp:c,onChange:l}),M=a.useRef(null),D=p?!!p.closest("form"):!0,[I,F]=a.useState(new Set),k=Array.from(I).map(A=>A.props.value).join(";");return m.jsx(Op,{...h,children:m.jsxs(wm,{required:g,scope:t,trigger:p,onTriggerChange:b,valueNode:x,onValueNodeChange:y,valueNodeHasChildren:w,onValueNodeHasChildrenChange:C,contentId:co(),value:R,onValueChange:N,open:P,onOpenChange:S,dir:E,triggerPointerDownPosRef:M,disabled:v,children:[m.jsx(Fn.Provider,{scope:t,children:m.jsx(Cm,{scope:e.__scopeSelect,onNativeOptionAdd:a.useCallback(A=>{F(j=>new Set(j).add(A))},[]),onNativeOptionRemove:a.useCallback(A=>{F(j=>{const O=new Set(j);return O.delete(A),O})},[]),children:n})}),D?m.jsxs(_a,{"aria-hidden":!0,required:g,tabIndex:-1,name:d,autoComplete:f,value:R,onChange:A=>N(A.target.value),disabled:v,children:[R===void 0?m.jsx("option",{value:""}):null,Array.from(I)]},k):null]})})};aa.displayName=Wt;var ca="SelectTrigger",la=a.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,s=Un(n),i=Ge(ca,n),c=i.disabled||r,l=oe(t,i.onTriggerChange),u=Bn(n),[d,f,v]=Ma(h=>{const p=u().filter(y=>!y.disabled),b=p.find(y=>y.value===i.value),x=Ia(p,h,b);x!==void 0&&i.onValueChange(x.value)}),g=()=>{c||(i.onOpenChange(!0),v())};return m.jsx(_p,{asChild:!0,...s,children:m.jsx(te.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":Oa(i.value)?"":void 0,...o,ref:l,onClick:ne(o.onClick,h=>{h.currentTarget.focus()}),onPointerDown:ne(o.onPointerDown,h=>{const p=h.target;p.hasPointerCapture(h.pointerId)&&p.releasePointerCapture(h.pointerId),h.button===0&&h.ctrlKey===!1&&(g(),i.triggerPointerDownPosRef.current={x:Math.round(h.pageX),y:Math.round(h.pageY)},h.preventDefault())}),onKeyDown:ne(o.onKeyDown,h=>{const p=d.current!=="";!(h.ctrlKey||h.altKey||h.metaKey)&&h.key.length===1&&f(h.key),!(p&&h.key===" ")&&bm.includes(h.key)&&(g(),h.preventDefault())})})})});la.displayName=ca;var ua="SelectValue",da=a.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:s,placeholder:i="",...c}=e,l=Ge(ua,n),{onValueNodeHasChildrenChange:u}=l,d=s!==void 0,f=oe(t,l.onValueNodeChange);return ge(()=>{u(d)},[u,d]),m.jsx(te.span,{...c,ref:f,style:{pointerEvents:"none"},children:Oa(l.value)?m.jsx(m.Fragment,{children:i}):s})});da.displayName=ua;var Sm="SelectIcon",fa=a.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return m.jsx(te.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});fa.displayName=Sm;var $m="SelectPortal",pa=e=>m.jsx(Ki,{asChild:!0,...e});pa.displayName=$m;var Ze="SelectContent",ma=a.forwardRef((e,t)=>{const n=Ge(Ze,e.__scopeSelect),[r,o]=a.useState();if(ge(()=>{o(new DocumentFragment)},[]),!n.open){const s=r;return s?Fe.createPortal(m.jsx(ha,{scope:e.__scopeSelect,children:m.jsx(Fn.Slot,{scope:e.__scopeSelect,children:m.jsx("div",{children:e.children})})}),s):null}return m.jsx(va,{...e,ref:t})});ma.displayName=Ze;var _e=10,[ha,qe]=xt(Ze),Rm="SelectContentImpl",va=a.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:s,onPointerDownOutside:i,side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:v,collisionPadding:g,sticky:h,hideWhenDetached:p,avoidCollisions:b,...x}=e,y=Ge(Ze,n),[w,C]=a.useState(null),[E,P]=a.useState(null),S=oe(t,_=>C(_)),[R,N]=a.useState(null),[M,D]=a.useState(null),I=Bn(n),[F,k]=a.useState(!1),A=a.useRef(!1);a.useEffect(()=>{if(w)return Yi(w)},[w]),Of();const j=a.useCallback(_=>{const[G,...H]=I().map(Y=>Y.ref.current),[V]=H.slice(-1),q=document.activeElement;for(const Y of _)if(Y===q||(Y==null||Y.scrollIntoView({block:"nearest"}),Y===G&&E&&(E.scrollTop=0),Y===V&&E&&(E.scrollTop=E.scrollHeight),Y==null||Y.focus(),document.activeElement!==q))return},[I,E]),O=a.useCallback(()=>j([R,w]),[j,R,w]);a.useEffect(()=>{F&&O()},[F,O]);const{onOpenChange:L,triggerPointerDownPosRef:T}=y;a.useEffect(()=>{if(w){let _={x:0,y:0};const G=V=>{var q,Y;_={x:Math.abs(Math.round(V.pageX)-(((q=T.current)==null?void 0:q.x)??0)),y:Math.abs(Math.round(V.pageY)-(((Y=T.current)==null?void 0:Y.y)??0))}},H=V=>{_.x<=10&&_.y<=10?V.preventDefault():w.contains(V.target)||L(!1),document.removeEventListener("pointermove",G),T.current=null};return T.current!==null&&(document.addEventListener("pointermove",G),document.addEventListener("pointerup",H,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",G),document.removeEventListener("pointerup",H,{capture:!0})}}},[w,L,T]),a.useEffect(()=>{const _=()=>L(!1);return window.addEventListener("blur",_),window.addEventListener("resize",_),()=>{window.removeEventListener("blur",_),window.removeEventListener("resize",_)}},[L]);const[J,X]=Ma(_=>{const G=I().filter(q=>!q.disabled),H=G.find(q=>q.ref.current===document.activeElement),V=Ia(G,_,H);V&&setTimeout(()=>V.ref.current.focus())}),re=a.useCallback((_,G,H)=>{const V=!A.current&&!H;(y.value!==void 0&&y.value===G||V)&&(N(_),V&&(A.current=!0))},[y.value]),se=a.useCallback(()=>w==null?void 0:w.focus(),[w]),ie=a.useCallback((_,G,H)=>{const V=!A.current&&!H;(y.value!==void 0&&y.value===G||V)&&D(_)},[y.value]),ae=r==="popper"?Sr:ga,Z=ae===Sr?{side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:v,collisionPadding:g,sticky:h,hideWhenDetached:p,avoidCollisions:b}:{};return m.jsx(ha,{scope:n,content:w,viewport:E,onViewportChange:P,itemRefCallback:re,selectedItem:R,onItemLeave:se,itemTextRefCallback:ie,focusSelectedItem:O,selectedItemText:M,position:r,isPositioned:F,searchRef:J,children:m.jsx(gm,{as:Mt,allowPinchZoom:!0,children:m.jsx(Ei,{asChild:!0,trapped:y.open,onMountAutoFocus:_=>{_.preventDefault()},onUnmountAutoFocus:ne(o,_=>{var G;(G=y.trigger)==null||G.focus({preventScroll:!0}),_.preventDefault()}),children:m.jsx(wi,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:_=>_.preventDefault(),onDismiss:()=>y.onOpenChange(!1),children:m.jsx(ae,{role:"listbox",id:y.contentId,"data-state":y.open?"open":"closed",dir:y.dir,onContextMenu:_=>_.preventDefault(),...x,...Z,onPlaced:()=>k(!0),ref:S,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:ne(x.onKeyDown,_=>{const G=_.ctrlKey||_.altKey||_.metaKey;if(_.key==="Tab"&&_.preventDefault(),!G&&_.key.length===1&&X(_.key),["ArrowUp","ArrowDown","Home","End"].includes(_.key)){let V=I().filter(q=>!q.disabled).map(q=>q.ref.current);if(["ArrowUp","End"].includes(_.key)&&(V=V.slice().reverse()),["ArrowUp","ArrowDown"].includes(_.key)){const q=_.target,Y=V.indexOf(q);V=V.slice(Y+1)}setTimeout(()=>j(V)),_.preventDefault()}})})})})})})});va.displayName=Rm;var Pm="SelectItemAlignedPosition",ga=a.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,s=Ge(Ze,n),i=qe(Ze,n),[c,l]=a.useState(null),[u,d]=a.useState(null),f=oe(t,S=>d(S)),v=Bn(n),g=a.useRef(!1),h=a.useRef(!0),{viewport:p,selectedItem:b,selectedItemText:x,focusSelectedItem:y}=i,w=a.useCallback(()=>{if(s.trigger&&s.valueNode&&c&&u&&p&&b&&x){const S=s.trigger.getBoundingClientRect(),R=u.getBoundingClientRect(),N=s.valueNode.getBoundingClientRect(),M=x.getBoundingClientRect();if(s.dir!=="rtl"){const q=M.left-R.left,Y=N.left-q,Q=S.left-Y,ue=S.width+Q,fe=Math.max(ue,R.width),Re=window.innerWidth-_e,Oe=vn(Y,[_e,Re-fe]);c.style.minWidth=ue+"px",c.style.left=Oe+"px"}else{const q=R.right-M.right,Y=window.innerWidth-N.right-q,Q=window.innerWidth-S.right-Y,ue=S.width+Q,fe=Math.max(ue,R.width),Re=window.innerWidth-_e,Oe=vn(Y,[_e,Re-fe]);c.style.minWidth=ue+"px",c.style.right=Oe+"px"}const D=v(),I=window.innerHeight-_e*2,F=p.scrollHeight,k=window.getComputedStyle(u),A=parseInt(k.borderTopWidth,10),j=parseInt(k.paddingTop,10),O=parseInt(k.borderBottomWidth,10),L=parseInt(k.paddingBottom,10),T=A+j+F+L+O,J=Math.min(b.offsetHeight*5,T),X=window.getComputedStyle(p),re=parseInt(X.paddingTop,10),se=parseInt(X.paddingBottom,10),ie=S.top+S.height/2-_e,ae=I-ie,Z=b.offsetHeight/2,_=b.offsetTop+Z,G=A+j+_,H=T-G;if(G<=ie){const q=b===D[D.length-1].ref.current;c.style.bottom="0px";const Y=u.clientHeight-p.offsetTop-p.offsetHeight,Q=Math.max(ae,Z+(q?se:0)+Y+O),ue=G+Q;c.style.height=ue+"px"}else{const q=b===D[0].ref.current;c.style.top="0px";const Q=Math.max(ie,A+p.offsetTop+(q?re:0)+Z)+H;c.style.height=Q+"px",p.scrollTop=G-ie+p.offsetTop}c.style.margin=`${_e}px 0`,c.style.minHeight=J+"px",c.style.maxHeight=I+"px",r==null||r(),requestAnimationFrame(()=>g.current=!0)}},[v,s.trigger,s.valueNode,c,u,p,b,x,s.dir,r]);ge(()=>w(),[w]);const[C,E]=a.useState();ge(()=>{u&&E(window.getComputedStyle(u).zIndex)},[u]);const P=a.useCallback(S=>{S&&h.current===!0&&(w(),y==null||y(),h.current=!1)},[w,y]);return m.jsx(Nm,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:g,onScrollButtonChange:P,children:m.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:m.jsx(te.div,{...o,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});ga.displayName=Pm;var Tm="SelectPopperPosition",Sr=a.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=_e,...s}=e,i=Un(n);return m.jsx(Mp,{...i,...s,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Sr.displayName=Tm;var[Nm,No]=xt(Ze,{}),$r="SelectViewport",ba=a.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...o}=e,s=qe($r,n),i=No($r,n),c=oe(t,s.onViewportChange),l=a.useRef(0);return m.jsxs(m.Fragment,{children:[m.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),m.jsx(Fn.Slot,{scope:n,children:m.jsx(te.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:c,style:{position:"relative",flex:1,overflow:"auto",...o.style},onScroll:ne(o.onScroll,u=>{const d=u.currentTarget,{contentWrapper:f,shouldExpandOnScrollRef:v}=i;if(v!=null&&v.current&&f){const g=Math.abs(l.current-d.scrollTop);if(g>0){const h=window.innerHeight-_e*2,p=parseFloat(f.style.minHeight),b=parseFloat(f.style.height),x=Math.max(p,b);if(x<h){const y=x+g,w=Math.min(h,y),C=y-w;f.style.height=w+"px",f.style.bottom==="0px"&&(d.scrollTop=C>0?C:0,f.style.justifyContent="flex-end")}}}l.current=d.scrollTop})})})]})});ba.displayName=$r;var xa="SelectGroup",[Am,Om]=xt(xa),_m=a.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=co();return m.jsx(Am,{scope:n,id:o,children:m.jsx(te.div,{role:"group","aria-labelledby":o,...r,ref:t})})});_m.displayName=xa;var ya="SelectLabel",wa=a.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Om(ya,n);return m.jsx(te.div,{id:o.id,...r,ref:t})});wa.displayName=ya;var wn="SelectItem",[Mm,Ca]=xt(wn),Ea=a.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:s,...i}=e,c=Ge(wn,n),l=qe(wn,n),u=c.value===r,[d,f]=a.useState(s??""),[v,g]=a.useState(!1),h=oe(t,x=>{var y;return(y=l.itemRefCallback)==null?void 0:y.call(l,x,r,o)}),p=co(),b=()=>{o||(c.onValueChange(r),c.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return m.jsx(Mm,{scope:n,value:r,disabled:o,textId:p,isSelected:u,onItemTextChange:a.useCallback(x=>{f(y=>y||((x==null?void 0:x.textContent)??"").trim())},[]),children:m.jsx(Fn.ItemSlot,{scope:n,value:r,disabled:o,textValue:d,children:m.jsx(te.div,{role:"option","aria-labelledby":p,"data-highlighted":v?"":void 0,"aria-selected":u&&v,"data-state":u?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...i,ref:h,onFocus:ne(i.onFocus,()=>g(!0)),onBlur:ne(i.onBlur,()=>g(!1)),onPointerUp:ne(i.onPointerUp,b),onPointerMove:ne(i.onPointerMove,x=>{var y;o?(y=l.onItemLeave)==null||y.call(l):x.currentTarget.focus({preventScroll:!0})}),onPointerLeave:ne(i.onPointerLeave,x=>{var y;x.currentTarget===document.activeElement&&((y=l.onItemLeave)==null||y.call(l))}),onKeyDown:ne(i.onKeyDown,x=>{var w;((w=l.searchRef)==null?void 0:w.current)!==""&&x.key===" "||(xm.includes(x.key)&&b(),x.key===" "&&x.preventDefault())})})})})});Ea.displayName=wn;var Rt="SelectItemText",Sa=a.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...s}=e,i=Ge(Rt,n),c=qe(Rt,n),l=Ca(Rt,n),u=Em(Rt,n),[d,f]=a.useState(null),v=oe(t,x=>f(x),l.onItemTextChange,x=>{var y;return(y=c.itemTextRefCallback)==null?void 0:y.call(c,x,l.value,l.disabled)}),g=d==null?void 0:d.textContent,h=a.useMemo(()=>m.jsx("option",{value:l.value,disabled:l.disabled,children:g},l.value),[l.disabled,l.value,g]),{onNativeOptionAdd:p,onNativeOptionRemove:b}=u;return ge(()=>(p(h),()=>b(h)),[p,b,h]),m.jsxs(m.Fragment,{children:[m.jsx(te.span,{id:l.textId,...s,ref:v}),l.isSelected&&i.valueNode&&!i.valueNodeHasChildren?Fe.createPortal(s.children,i.valueNode):null]})});Sa.displayName=Rt;var $a="SelectItemIndicator",Ra=a.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return Ca($a,n).isSelected?m.jsx(te.span,{"aria-hidden":!0,...r,ref:t}):null});Ra.displayName=$a;var Rr="SelectScrollUpButton",Pa=a.forwardRef((e,t)=>{const n=qe(Rr,e.__scopeSelect),r=No(Rr,e.__scopeSelect),[o,s]=a.useState(!1),i=oe(t,r.onScrollButtonChange);return ge(()=>{if(n.viewport&&n.isPositioned){let c=function(){const u=l.scrollTop>0;s(u)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),o?m.jsx(Na,{...e,ref:i,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop-l.offsetHeight)}}):null});Pa.displayName=Rr;var Pr="SelectScrollDownButton",Ta=a.forwardRef((e,t)=>{const n=qe(Pr,e.__scopeSelect),r=No(Pr,e.__scopeSelect),[o,s]=a.useState(!1),i=oe(t,r.onScrollButtonChange);return ge(()=>{if(n.viewport&&n.isPositioned){let c=function(){const u=l.scrollHeight-l.clientHeight,d=Math.ceil(l.scrollTop)<u;s(d)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),o?m.jsx(Na,{...e,ref:i,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop+l.offsetHeight)}}):null});Ta.displayName=Pr;var Na=a.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,s=qe("SelectScrollButton",n),i=a.useRef(null),c=Bn(n),l=a.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return a.useEffect(()=>()=>l(),[l]),ge(()=>{var d;const u=c().find(f=>f.ref.current===document.activeElement);(d=u==null?void 0:u.ref.current)==null||d.scrollIntoView({block:"nearest"})},[c]),m.jsx(te.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:ne(o.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(r,50))}),onPointerMove:ne(o.onPointerMove,()=>{var u;(u=s.onItemLeave)==null||u.call(s),i.current===null&&(i.current=window.setInterval(r,50))}),onPointerLeave:ne(o.onPointerLeave,()=>{l()})})}),Im="SelectSeparator",Aa=a.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return m.jsx(te.div,{"aria-hidden":!0,...r,ref:t})});Aa.displayName=Im;var Tr="SelectArrow",Dm=a.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Un(n),s=Ge(Tr,n),i=qe(Tr,n);return s.open&&i.position==="popper"?m.jsx(Ip,{...o,...r,ref:t}):null});Dm.displayName=Tr;function Oa(e){return e===""||e===void 0}var _a=a.forwardRef((e,t)=>{const{value:n,...r}=e,o=a.useRef(null),s=oe(t,o),i=Gi(n);return a.useEffect(()=>{const c=o.current,l=window.HTMLSelectElement.prototype,d=Object.getOwnPropertyDescriptor(l,"value").set;if(i!==n&&d){const f=new Event("change",{bubbles:!0});d.call(c,n),c.dispatchEvent(f)}},[i,n]),m.jsx(uu,{asChild:!0,children:m.jsx("select",{...r,ref:s,defaultValue:n})})});_a.displayName="BubbleSelect";function Ma(e){const t=De(e),n=a.useRef(""),r=a.useRef(0),o=a.useCallback(i=>{const c=n.current+i;t(c),function l(u){n.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>l(""),1e3))}(c)},[t]),s=a.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return a.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,s]}function Ia(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=jm(e,Math.max(s,0));o.length===1&&(i=i.filter(u=>u!==n));const l=i.find(u=>u.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function jm(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var km=aa,Da=la,Lm=da,Fm=fa,Bm=pa,ja=ma,Um=ba,ka=wa,La=Ea,Wm=Sa,Hm=Ra,Fa=Pa,Ba=Ta,Ua=Aa;const Vm=km,zm=Lm,Wa=a.forwardRef(({className:e,children:t,...n},r)=>m.jsxs(Da,{ref:r,className:K("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,m.jsx(Fm,{asChild:!0,children:m.jsx(Ot,{className:"h-4 w-4 opacity-50"})})]}));Wa.displayName=Da.displayName;const Ha=a.forwardRef(({className:e,...t},n)=>m.jsx(Fa,{ref:n,className:K("flex cursor-default items-center justify-center py-1",e),...t,children:m.jsx(Ru,{className:"h-4 w-4"})}));Ha.displayName=Fa.displayName;const Va=a.forwardRef(({className:e,...t},n)=>m.jsx(Ba,{ref:n,className:K("flex cursor-default items-center justify-center py-1",e),...t,children:m.jsx(Ot,{className:"h-4 w-4"})}));Va.displayName=Ba.displayName;const za=a.forwardRef(({className:e,children:t,position:n="popper",...r},o)=>m.jsx(Bm,{children:m.jsxs(ja,{ref:o,className:K("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-muted text-muted-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[m.jsx(Ha,{}),m.jsx(Um,{className:K("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),m.jsx(Va,{})]})}));za.displayName=ja.displayName;const Km=a.forwardRef(({className:e,...t},n)=>m.jsx(ka,{ref:n,className:K("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));Km.displayName=ka.displayName;const Nr=a.forwardRef(({className:e,children:t,...n},r)=>m.jsxs(La,{ref:r,className:K("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[m.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:m.jsx(Hm,{children:m.jsx(eo,{className:"h-4 w-4"})})}),m.jsx(Wm,{children:t})]}));Nr.displayName=La.displayName;const Gm=a.forwardRef(({className:e,...t},n)=>m.jsx(Ua,{ref:n,className:K("-mx-1 my-1 h-px bg-muted",e),...t}));Gm.displayName=Ua.displayName;function Me(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function qm(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Ka(...e){return t=>e.forEach(n=>qm(n,t))}function et(...e){return a.useCallback(Ka(...e),e)}function Ga(e,t=[]){let n=[];function r(s,i){const c=a.createContext(i),l=n.length;n=[...n,i];function u(f){const{scope:v,children:g,...h}=f,p=(v==null?void 0:v[e][l])||c,b=a.useMemo(()=>h,Object.values(h));return m.jsx(p.Provider,{value:b,children:g})}function d(f,v){const g=(v==null?void 0:v[e][l])||c,h=a.useContext(g);if(h)return h;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(i=>a.createContext(i));return function(c){const l=(c==null?void 0:c[e])||s;return a.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,Ym(o,...t)]}function Ym(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((c,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...c,...f}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var qa=a.forwardRef((e,t)=>{const{children:n,...r}=e,o=a.Children.toArray(n),s=o.find(Xm);if(s){const i=s.props.children,c=o.map(l=>l===s?a.Children.count(i)>1?a.Children.only(null):a.isValidElement(i)?i.props.children:null:l);return m.jsx(Ar,{...r,ref:t,children:a.isValidElement(i)?a.cloneElement(i,void 0,c):null})}return m.jsx(Ar,{...r,ref:t,children:n})});qa.displayName="Slot";var Ar=a.forwardRef((e,t)=>{const{children:n,...r}=e;if(a.isValidElement(n)){const o=Zm(n);return a.cloneElement(n,{...Jm(r,n.props),ref:t?Ka(t,o):o})}return a.Children.count(n)>1?a.Children.only(null):null});Ar.displayName="SlotClone";var Ya=({children:e})=>m.jsx(m.Fragment,{children:e});function Xm(e){return a.isValidElement(e)&&e.type===Ya}function Jm(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...c)=>{s(...c),o(...c)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Zm(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Qm=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],yt=Qm.reduce((e,t)=>{const n=a.forwardRef((r,o)=>{const{asChild:s,...i}=r,c=s?qa:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),m.jsx(c,{...i,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function eh(e,t){e&&Fe.flushSync(()=>e.dispatchEvent(t))}function wt(e){const t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function th(e,t=globalThis==null?void 0:globalThis.document){const n=wt(e);a.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var nh="DismissableLayer",Or="dismissableLayer.update",rh="dismissableLayer.pointerDownOutside",oh="dismissableLayer.focusOutside",Ns,Xa=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ja=a.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:c,...l}=e,u=a.useContext(Xa),[d,f]=a.useState(null),v=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,g]=a.useState({}),h=et(t,S=>f(S)),p=Array.from(u.layers),[b]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=p.indexOf(b),y=d?p.indexOf(d):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,C=y>=x,E=ah(S=>{const R=S.target,N=[...u.branches].some(M=>M.contains(R));!C||N||(o==null||o(S),i==null||i(S),S.defaultPrevented||c==null||c())},v),P=ch(S=>{const R=S.target;[...u.branches].some(M=>M.contains(R))||(s==null||s(S),i==null||i(S),S.defaultPrevented||c==null||c())},v);return th(S=>{y===u.layers.size-1&&(r==null||r(S),!S.defaultPrevented&&c&&(S.preventDefault(),c()))},v),a.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Ns=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),As(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=Ns)}},[d,v,n,u]),a.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),As())},[d,u]),a.useEffect(()=>{const S=()=>g({});return document.addEventListener(Or,S),()=>document.removeEventListener(Or,S)},[]),m.jsx(yt.div,{...l,ref:h,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:Me(e.onFocusCapture,P.onFocusCapture),onBlurCapture:Me(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:Me(e.onPointerDownCapture,E.onPointerDownCapture)})});Ja.displayName=nh;var sh="DismissableLayerBranch",ih=a.forwardRef((e,t)=>{const n=a.useContext(Xa),r=a.useRef(null),o=et(t,r);return a.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),m.jsx(yt.div,{...e,ref:o})});ih.displayName=sh;function ah(e,t=globalThis==null?void 0:globalThis.document){const n=wt(e),r=a.useRef(!1),o=a.useRef(()=>{});return a.useEffect(()=>{const s=c=>{if(c.target&&!r.current){let l=function(){Za(rh,n,u,{discrete:!0})};const u={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function ch(e,t=globalThis==null?void 0:globalThis.document){const n=wt(e),r=a.useRef(!1);return a.useEffect(()=>{const o=s=>{s.target&&!r.current&&Za(oh,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function As(){const e=new CustomEvent(Or);document.dispatchEvent(e)}function Za(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?eh(o,s):o.dispatchEvent(s)}var ft=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{},lh=Tn.useId||(()=>{}),uh=0;function dh(e){const[t,n]=a.useState(lh());return ft(()=>{e||n(r=>r??String(uh++))},[e]),e||(t?`radix-${t}`:"")}var fh="Arrow",Qa=a.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return m.jsx(yt.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:m.jsx("polygon",{points:"0,0 30,0 15,10"})})});Qa.displayName=fh;var ph=Qa;function mh(e){const[t,n]=a.useState(void 0);return ft(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;i=u.inlineSize,c=u.blockSize}else i=e.offsetWidth,c=e.offsetHeight;n({width:i,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var Ao="Popper",[ec,tc]=Ga(Ao),[hh,nc]=ec(Ao),rc=e=>{const{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return m.jsx(hh,{scope:t,anchor:r,onAnchorChange:o,children:n})};rc.displayName=Ao;var oc="PopperAnchor",sc=a.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=nc(oc,n),i=a.useRef(null),c=et(t,i);return a.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:m.jsx(yt.div,{...o,ref:c})});sc.displayName=oc;var Oo="PopperContent",[vh,gh]=ec(Oo),ic=a.forwardRef((e,t)=>{var _,G,H,V,q,Y;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:f="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:h,...p}=e,b=nc(Oo,n),[x,y]=a.useState(null),w=et(t,Q=>y(Q)),[C,E]=a.useState(null),P=mh(C),S=(P==null?void 0:P.width)??0,R=(P==null?void 0:P.height)??0,N=r+(s!=="center"?"-"+s:""),M=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},D=Array.isArray(u)?u:[u],I=D.length>0,F={padding:M,boundary:D.filter(xh),altBoundary:I},{refs:k,floatingStyles:A,placement:j,isPositioned:O,middlewareData:L}=So({strategy:"fixed",placement:N,whileElementsMounted:(...Q)=>go(...Q,{animationFrame:g==="always"}),elements:{reference:b.anchor},middleware:[po({mainAxis:o+R,alignmentAxis:i}),l&&bo({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?Co():void 0,...F}),l&&xo({...F}),yo({...F,apply:({elements:Q,rects:ue,availableWidth:fe,availableHeight:Re})=>{const{width:Oe,height:St}=ue.reference,Ce=Q.floating.style;Ce.setProperty("--radix-popper-available-width",`${fe}px`),Ce.setProperty("--radix-popper-available-height",`${Re}px`),Ce.setProperty("--radix-popper-anchor-width",`${Oe}px`),Ce.setProperty("--radix-popper-anchor-height",`${St}px`)}}),C&&Eo({element:C,padding:c}),yh({arrowWidth:S,arrowHeight:R}),v&&wo({strategy:"referenceHidden",...F})]}),[T,J]=lc(j),X=wt(h);ft(()=>{O&&(X==null||X())},[O,X]);const re=(_=L.arrow)==null?void 0:_.x,se=(G=L.arrow)==null?void 0:G.y,ie=((H=L.arrow)==null?void 0:H.centerOffset)!==0,[ae,Z]=a.useState();return ft(()=>{x&&Z(window.getComputedStyle(x).zIndex)},[x]),m.jsx("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...A,transform:O?A.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ae,"--radix-popper-transform-origin":[(V=L.transformOrigin)==null?void 0:V.x,(q=L.transformOrigin)==null?void 0:q.y].join(" "),...((Y=L.hide)==null?void 0:Y.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:m.jsx(vh,{scope:n,placedSide:T,onArrowChange:E,arrowX:re,arrowY:se,shouldHideArrow:ie,children:m.jsx(yt.div,{"data-side":T,"data-align":J,...p,ref:w,style:{...p.style,animation:O?void 0:"none"}})})})});ic.displayName=Oo;var ac="PopperArrow",bh={top:"bottom",right:"left",bottom:"top",left:"right"},cc=a.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=gh(ac,r),i=bh[s.placedSide];return m.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:m.jsx(ph,{...o,ref:n,style:{...o.style,display:"block"}})})});cc.displayName=ac;function xh(e){return e!==null}var yh=e=>({name:"transformOrigin",options:e,fn(t){var b,x,y;const{placement:n,rects:r,middlewareData:o}=t,i=((b=o.arrow)==null?void 0:b.centerOffset)!==0,c=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,d]=lc(n),f={start:"0%",center:"50%",end:"100%"}[d],v=(((x=o.arrow)==null?void 0:x.x)??0)+c/2,g=(((y=o.arrow)==null?void 0:y.y)??0)+l/2;let h="",p="";return u==="bottom"?(h=i?f:`${v}px`,p=`${-l}px`):u==="top"?(h=i?f:`${v}px`,p=`${r.floating.height+l}px`):u==="right"?(h=`${-l}px`,p=i?f:`${g}px`):u==="left"&&(h=`${r.floating.width+l}px`,p=i?f:`${g}px`),{data:{x:h,y:p}}}});function lc(e){const[t,n="center"]=e.split("-");return[t,n]}var wh=rc,Ch=sc,Eh=ic,Sh=cc;function $h(e,t){return a.useReducer((n,r)=>t[n][r]??n,e)}var uc=e=>{const{present:t,children:n}=e,r=Rh(t),o=typeof n=="function"?n({present:r.isPresent}):a.Children.only(n),s=et(r.ref,Ph(o));return typeof n=="function"||r.isPresent?a.cloneElement(o,{ref:s}):null};uc.displayName="Presence";function Rh(e){const[t,n]=a.useState(),r=a.useRef({}),o=a.useRef(e),s=a.useRef("none"),i=e?"mounted":"unmounted",[c,l]=$h(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return a.useEffect(()=>{const u=Qt(r.current);s.current=c==="mounted"?u:"none"},[c]),ft(()=>{const u=r.current,d=o.current;if(d!==e){const v=s.current,g=Qt(u);e?l("MOUNT"):g==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&v!==g?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),ft(()=>{if(t){const u=f=>{const g=Qt(r.current).includes(f.animationName);f.target===t&&g&&Fe.flushSync(()=>l("ANIMATION_END"))},d=f=>{f.target===t&&(s.current=Qt(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:a.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Qt(e){return(e==null?void 0:e.animationName)||"none"}function Ph(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Th({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=Nh({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,c=wt(n),l=a.useCallback(u=>{if(s){const f=typeof u=="function"?u(e):u;f!==e&&c(f)}else o(u)},[s,e,o,c]);return[i,l]}function Nh({defaultProp:e,onChange:t}){const n=a.useState(e),[r]=n,o=a.useRef(r),s=wt(t);return a.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var[Wn,fy]=Ga("Tooltip",[tc]),Hn=tc(),dc="TooltipProvider",Ah=700,_r="tooltip.open",[Oh,_o]=Wn(dc),fc=e=>{const{__scopeTooltip:t,delayDuration:n=Ah,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,[i,c]=a.useState(!0),l=a.useRef(!1),u=a.useRef(0);return a.useEffect(()=>{const d=u.current;return()=>window.clearTimeout(d)},[]),m.jsx(Oh,{scope:t,isOpenDelayed:i,delayDuration:n,onOpen:a.useCallback(()=>{window.clearTimeout(u.current),c(!1)},[]),onClose:a.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>c(!0),r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:a.useCallback(d=>{l.current=d},[]),disableHoverableContent:o,children:s})};fc.displayName=dc;var Vn="Tooltip",[_h,zn]=Wn(Vn),pc=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:s,disableHoverableContent:i,delayDuration:c}=e,l=_o(Vn,e.__scopeTooltip),u=Hn(t),[d,f]=a.useState(null),v=dh(),g=a.useRef(0),h=i??l.disableHoverableContent,p=c??l.delayDuration,b=a.useRef(!1),[x=!1,y]=Th({prop:r,defaultProp:o,onChange:S=>{S?(l.onOpen(),document.dispatchEvent(new CustomEvent(_r))):l.onClose(),s==null||s(S)}}),w=a.useMemo(()=>x?b.current?"delayed-open":"instant-open":"closed",[x]),C=a.useCallback(()=>{window.clearTimeout(g.current),b.current=!1,y(!0)},[y]),E=a.useCallback(()=>{window.clearTimeout(g.current),y(!1)},[y]),P=a.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{b.current=!0,y(!0)},p)},[p,y]);return a.useEffect(()=>()=>window.clearTimeout(g.current),[]),m.jsx(wh,{...u,children:m.jsx(_h,{scope:t,contentId:v,open:x,stateAttribute:w,trigger:d,onTriggerChange:f,onTriggerEnter:a.useCallback(()=>{l.isOpenDelayed?P():C()},[l.isOpenDelayed,P,C]),onTriggerLeave:a.useCallback(()=>{h?E():window.clearTimeout(g.current)},[E,h]),onOpen:C,onClose:E,disableHoverableContent:h,children:n})})};pc.displayName=Vn;var Mr="TooltipTrigger",mc=a.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=zn(Mr,n),s=_o(Mr,n),i=Hn(n),c=a.useRef(null),l=et(t,c,o.onTriggerChange),u=a.useRef(!1),d=a.useRef(!1),f=a.useCallback(()=>u.current=!1,[]);return a.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),m.jsx(Ch,{asChild:!0,...i,children:m.jsx(yt.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:Me(e.onPointerMove,v=>{v.pointerType!=="touch"&&!d.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:Me(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:Me(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:Me(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:Me(e.onBlur,o.onClose),onClick:Me(e.onClick,o.onClose)})})});mc.displayName=Mr;var Mh="TooltipPortal",[py,Ih]=Wn(Mh,{forceMount:void 0}),pt="TooltipContent",hc=a.forwardRef((e,t)=>{const n=Ih(pt,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=zn(pt,e.__scopeTooltip);return m.jsx(uc,{present:r||i.open,children:i.disableHoverableContent?m.jsx(vc,{side:o,...s,ref:t}):m.jsx(Dh,{side:o,...s,ref:t})})}),Dh=a.forwardRef((e,t)=>{const n=zn(pt,e.__scopeTooltip),r=_o(pt,e.__scopeTooltip),o=a.useRef(null),s=et(t,o),[i,c]=a.useState(null),{trigger:l,onClose:u}=n,d=o.current,{onPointerInTransitChange:f}=r,v=a.useCallback(()=>{c(null),f(!1)},[f]),g=a.useCallback((h,p)=>{const b=h.currentTarget,x={x:h.clientX,y:h.clientY},y=Fh(x,b.getBoundingClientRect()),w=Bh(x,y),C=Uh(p.getBoundingClientRect()),E=Hh([...w,...C]);c(E),f(!0)},[f]);return a.useEffect(()=>()=>v(),[v]),a.useEffect(()=>{if(l&&d){const h=b=>g(b,d),p=b=>g(b,l);return l.addEventListener("pointerleave",h),d.addEventListener("pointerleave",p),()=>{l.removeEventListener("pointerleave",h),d.removeEventListener("pointerleave",p)}}},[l,d,g,v]),a.useEffect(()=>{if(i){const h=p=>{const b=p.target,x={x:p.clientX,y:p.clientY},y=(l==null?void 0:l.contains(b))||(d==null?void 0:d.contains(b)),w=!Wh(x,i);y?v():w&&(v(),u())};return document.addEventListener("pointermove",h),()=>document.removeEventListener("pointermove",h)}},[l,d,i,u,v]),m.jsx(vc,{...e,ref:s})}),[jh,kh]=Wn(Vn,{isInside:!1}),vc=a.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...c}=e,l=zn(pt,n),u=Hn(n),{onClose:d}=l;return a.useEffect(()=>(document.addEventListener(_r,d),()=>document.removeEventListener(_r,d)),[d]),a.useEffect(()=>{if(l.trigger){const f=v=>{const g=v.target;g!=null&&g.contains(l.trigger)&&d()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[l.trigger,d]),m.jsx(Ja,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:f=>f.preventDefault(),onDismiss:d,children:m.jsxs(Eh,{"data-state":l.stateAttribute,...u,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[m.jsx(Ya,{children:r}),m.jsx(jh,{scope:n,isInside:!0,children:m.jsx(du,{id:l.contentId,role:"tooltip",children:o||r})})]})})});hc.displayName=pt;var gc="TooltipArrow",Lh=a.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Hn(n);return kh(gc,n).isInside?null:m.jsx(Sh,{...o,...r,ref:t})});Lh.displayName=gc;function Fh(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Bh(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Uh(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Wh(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const c=t[s].x,l=t[s].y,u=t[i].x,d=t[i].y;l>r!=d>r&&n<(u-c)*(r-l)/(d-l)+c&&(o=!o)}return o}function Hh(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Vh(t)}function Vh(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var zh=fc,Kh=pc,Gh=mc,bc=hc;const qh=zh,xc=Kh,yc=Gh,Mo=a.forwardRef(({className:e,sideOffset:t=4,...n},r)=>m.jsx(bc,{ref:r,sideOffset:t,className:K("z-50 overflow-hidden rounded-md border border-[#ffffff36] bg-muted px-3 py-1.5 text-sm text-muted-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));Mo.displayName=bc.displayName;const Yh=a.createContext(void 0),Xh={setTheme:e=>{},themes:[]},Jh=()=>{var e;return(e=a.useContext(Yh))!==null&&e!==void 0?e:Xh};function z(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Ht(e,t=[]){let n=[];function r(s,i){const c=a.createContext(i),l=n.length;n=[...n,i];function u(f){const{scope:v,children:g,...h}=f,p=(v==null?void 0:v[e][l])||c,b=a.useMemo(()=>h,Object.values(h));return a.createElement(p.Provider,{value:b},g)}function d(f,v){const g=(v==null?void 0:v[e][l])||c,h=a.useContext(g);if(h)return h;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(i=>a.createContext(i));return function(c){const l=(c==null?void 0:c[e])||s;return a.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,Zh(o,...t)]}function Zh(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((c,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...c,...f}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function Ae(e){const t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...n)=>{var r;return(r=t.current)===null||r===void 0?void 0:r.call(t,...n)},[])}function wc({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=Qh({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,c=Ae(n),l=a.useCallback(u=>{if(s){const f=typeof u=="function"?u(e):u;f!==e&&c(f)}else o(u)},[s,e,o,c]);return[i,l]}function Qh({defaultProp:e,onChange:t}){const n=a.useState(e),[r]=n,o=a.useRef(r),s=Ae(t);return a.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}const ev=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ye=ev.reduce((e,t)=>{const n=a.forwardRef((r,o)=>{const{asChild:s,...i}=r,c=s?fn:t;return a.useEffect(()=>{window[Symbol.for("radix-ui")]=!0},[]),a.createElement(c,U({},i,{ref:o}))});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Cc(e,t){e&&Fe.flushSync(()=>e.dispatchEvent(t))}function Ec(e){const t=e+"CollectionProvider",[n,r]=Ht(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=g=>{const{scope:h,children:p}=g,b=W.useRef(null),x=W.useRef(new Map).current;return W.createElement(o,{scope:h,itemMap:x,collectionRef:b},p)},c=e+"CollectionSlot",l=W.forwardRef((g,h)=>{const{scope:p,children:b}=g,x=s(c,p),y=ve(h,x.collectionRef);return W.createElement(fn,{ref:y},b)}),u=e+"CollectionItemSlot",d="data-radix-collection-item",f=W.forwardRef((g,h)=>{const{scope:p,children:b,...x}=g,y=W.useRef(null),w=ve(h,y),C=s(u,p);return W.useEffect(()=>(C.itemMap.set(y,{ref:y,...x}),()=>void C.itemMap.delete(y))),W.createElement(fn,{[d]:"",ref:w},b)});function v(g){const h=s(e+"CollectionConsumer",g);return W.useCallback(()=>{const b=h.collectionRef.current;if(!b)return[];const x=Array.from(b.querySelectorAll(`[${d}]`));return Array.from(h.itemMap.values()).sort((C,E)=>x.indexOf(C.ref.current)-x.indexOf(E.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:i,Slot:l,ItemSlot:f},v,r]}const tv=a.createContext(void 0);function Sc(e){const t=a.useContext(tv);return e||t||"ltr"}function nv(e,t=globalThis==null?void 0:globalThis.document){const n=Ae(e);a.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r),()=>t.removeEventListener("keydown",r)},[n,t])}const Ir="dismissableLayer.update",rv="dismissableLayer.pointerDownOutside",ov="dismissableLayer.focusOutside";let Os;const sv=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),iv=a.forwardRef((e,t)=>{var n;const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:s,onFocusOutside:i,onInteractOutside:c,onDismiss:l,...u}=e,d=a.useContext(sv),[f,v]=a.useState(null),g=(n=f==null?void 0:f.ownerDocument)!==null&&n!==void 0?n:globalThis==null?void 0:globalThis.document,[,h]=a.useState({}),p=ve(t,R=>v(R)),b=Array.from(d.layers),[x]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),y=b.indexOf(x),w=f?b.indexOf(f):-1,C=d.layersWithOutsidePointerEventsDisabled.size>0,E=w>=y,P=av(R=>{const N=R.target,M=[...d.branches].some(D=>D.contains(N));!E||M||(s==null||s(R),c==null||c(R),R.defaultPrevented||l==null||l())},g),S=cv(R=>{const N=R.target;[...d.branches].some(D=>D.contains(N))||(i==null||i(R),c==null||c(R),R.defaultPrevented||l==null||l())},g);return nv(R=>{w===d.layers.size-1&&(o==null||o(R),!R.defaultPrevented&&l&&(R.preventDefault(),l()))},g),a.useEffect(()=>{if(f)return r&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(Os=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(f)),d.layers.add(f),_s(),()=>{r&&d.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=Os)}},[f,g,r,d]),a.useEffect(()=>()=>{f&&(d.layers.delete(f),d.layersWithOutsidePointerEventsDisabled.delete(f),_s())},[f,d]),a.useEffect(()=>{const R=()=>h({});return document.addEventListener(Ir,R),()=>document.removeEventListener(Ir,R)},[]),a.createElement(ye.div,U({},u,{ref:p,style:{pointerEvents:C?E?"auto":"none":void 0,...e.style},onFocusCapture:z(e.onFocusCapture,S.onFocusCapture),onBlurCapture:z(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:z(e.onPointerDownCapture,P.onPointerDownCapture)}))});function av(e,t=globalThis==null?void 0:globalThis.document){const n=Ae(e),r=a.useRef(!1),o=a.useRef(()=>{});return a.useEffect(()=>{const s=c=>{if(c.target&&!r.current){let u=function(){$c(rv,n,l,{discrete:!0})};const l={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function cv(e,t=globalThis==null?void 0:globalThis.document){const n=Ae(e),r=a.useRef(!1);return a.useEffect(()=>{const o=s=>{s.target&&!r.current&&$c(ov,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function _s(){const e=new CustomEvent(Ir);document.dispatchEvent(e)}function $c(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Cc(o,s):o.dispatchEvent(s)}let ar=0;function lv(){a.useEffect(()=>{var e,t;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(e=n[0])!==null&&e!==void 0?e:Ms()),document.body.insertAdjacentElement("beforeend",(t=n[1])!==null&&t!==void 0?t:Ms()),ar++,()=>{ar===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),ar--}},[])}function Ms(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}const cr="focusScope.autoFocusOnMount",lr="focusScope.autoFocusOnUnmount",Is={bubbles:!1,cancelable:!0},uv=a.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[c,l]=a.useState(null),u=Ae(o),d=Ae(s),f=a.useRef(null),v=ve(t,p=>l(p)),g=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let p=function(w){if(g.paused||!c)return;const C=w.target;c.contains(C)?f.current=C:We(f.current,{select:!0})},b=function(w){if(g.paused||!c)return;const C=w.relatedTarget;C!==null&&(c.contains(C)||We(f.current,{select:!0}))},x=function(w){if(document.activeElement===document.body)for(const E of w)E.removedNodes.length>0&&We(c)};document.addEventListener("focusin",p),document.addEventListener("focusout",b);const y=new MutationObserver(x);return c&&y.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",b),y.disconnect()}}},[r,c,g.paused]),a.useEffect(()=>{if(c){js.add(g);const p=document.activeElement;if(!c.contains(p)){const x=new CustomEvent(cr,Is);c.addEventListener(cr,u),c.dispatchEvent(x),x.defaultPrevented||(dv(vv(Rc(c)),{select:!0}),document.activeElement===p&&We(c))}return()=>{c.removeEventListener(cr,u),setTimeout(()=>{const x=new CustomEvent(lr,Is);c.addEventListener(lr,d),c.dispatchEvent(x),x.defaultPrevented||We(p??document.body,{select:!0}),c.removeEventListener(lr,d),js.remove(g)},0)}}},[c,u,d,g]);const h=a.useCallback(p=>{if(!n&&!r||g.paused)return;const b=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,x=document.activeElement;if(b&&x){const y=p.currentTarget,[w,C]=fv(y);w&&C?!p.shiftKey&&x===C?(p.preventDefault(),n&&We(w,{select:!0})):p.shiftKey&&x===w&&(p.preventDefault(),n&&We(C,{select:!0})):x===y&&p.preventDefault()}},[n,r,g.paused]);return a.createElement(ye.div,U({tabIndex:-1},i,{ref:v,onKeyDown:h}))});function dv(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(We(r,{select:t}),document.activeElement!==n)return}function fv(e){const t=Rc(e),n=Ds(t,e),r=Ds(t.reverse(),e);return[n,r]}function Rc(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ds(e,t){for(const n of e)if(!pv(n,{upTo:t}))return n}function pv(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function mv(e){return e instanceof HTMLInputElement&&"select"in e}function We(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&mv(e)&&t&&e.select()}}const js=hv();function hv(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=ks(e,t),e.unshift(t)},remove(t){var n;e=ks(e,t),(n=e[0])===null||n===void 0||n.resume()}}}function ks(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function vv(e){return e.filter(t=>t.tagName!=="A")}const mt=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{},gv=Tn.useId||(()=>{});let bv=0;function Dr(e){const[t,n]=a.useState(gv());return mt(()=>{e||n(r=>r??String(bv++))},[e]),e||(t?`radix-${t}`:"")}function xv(e){const[t,n]=a.useState(void 0);return mt(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;i=u.inlineSize,c=u.blockSize}else i=e.offsetWidth,c=e.offsetHeight;n({width:i,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}const Pc="Popper",[Tc,Nc]=Ht(Pc),[yv,Ac]=Tc(Pc),wv=e=>{const{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return a.createElement(yv,{scope:t,anchor:r,onAnchorChange:o},n)},Cv="PopperAnchor",Ev=a.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=Ac(Cv,n),i=a.useRef(null),c=ve(t,i);return a.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:a.createElement(ye.div,U({},o,{ref:c}))}),Oc="PopperContent",[Sv,my]=Tc(Oc),$v=a.forwardRef((e,t)=>{var n,r,o,s,i,c,l,u;const{__scopePopper:d,side:f="bottom",sideOffset:v=0,align:g="center",alignOffset:h=0,arrowPadding:p=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:y=0,sticky:w="partial",hideWhenDetached:C=!1,updatePositionStrategy:E="optimized",onPlaced:P,...S}=e,R=Ac(Oc,d),[N,M]=a.useState(null),D=ve(t,fe=>M(fe)),[I,F]=a.useState(null),k=xv(I),A=(n=k==null?void 0:k.width)!==null&&n!==void 0?n:0,j=(r=k==null?void 0:k.height)!==null&&r!==void 0?r:0,O=f+(g!=="center"?"-"+g:""),L=typeof y=="number"?y:{top:0,right:0,bottom:0,left:0,...y},T=Array.isArray(x)?x:[x],J=T.length>0,X={padding:L,boundary:T.filter(Rv),altBoundary:J},{refs:re,floatingStyles:se,placement:ie,isPositioned:ae,middlewareData:Z}=So({strategy:"fixed",placement:O,whileElementsMounted:(...fe)=>go(...fe,{animationFrame:E==="always"}),elements:{reference:R.anchor},middleware:[po({mainAxis:v+j,alignmentAxis:h}),b&&bo({mainAxis:!0,crossAxis:!1,limiter:w==="partial"?Co():void 0,...X}),b&&xo({...X}),yo({...X,apply:({elements:fe,rects:Re,availableWidth:Oe,availableHeight:St})=>{const{width:Ce,height:nu}=Re.reference,Kt=fe.floating.style;Kt.setProperty("--radix-popper-available-width",`${Oe}px`),Kt.setProperty("--radix-popper-available-height",`${St}px`),Kt.setProperty("--radix-popper-anchor-width",`${Ce}px`),Kt.setProperty("--radix-popper-anchor-height",`${nu}px`)}}),I&&Eo({element:I,padding:p}),Pv({arrowWidth:A,arrowHeight:j}),C&&wo({strategy:"referenceHidden",...X})]}),[_,G]=_c(ie),H=Ae(P);mt(()=>{ae&&(H==null||H())},[ae,H]);const V=(o=Z.arrow)===null||o===void 0?void 0:o.x,q=(s=Z.arrow)===null||s===void 0?void 0:s.y,Y=((i=Z.arrow)===null||i===void 0?void 0:i.centerOffset)!==0,[Q,ue]=a.useState();return mt(()=>{N&&ue(window.getComputedStyle(N).zIndex)},[N]),a.createElement("div",{ref:re.setFloating,"data-radix-popper-content-wrapper":"",style:{...se,transform:ae?se.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Q,"--radix-popper-transform-origin":[(c=Z.transformOrigin)===null||c===void 0?void 0:c.x,(l=Z.transformOrigin)===null||l===void 0?void 0:l.y].join(" ")},dir:e.dir},a.createElement(Sv,{scope:d,placedSide:_,onArrowChange:F,arrowX:V,arrowY:q,shouldHideArrow:Y},a.createElement(ye.div,U({"data-side":_,"data-align":G},S,{ref:D,style:{...S.style,animation:ae?void 0:"none",opacity:(u=Z.hide)!==null&&u!==void 0&&u.referenceHidden?0:void 0}}))))});function Rv(e){return e!==null}const Pv=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,s,i;const{placement:c,rects:l,middlewareData:u}=t,f=((n=u.arrow)===null||n===void 0?void 0:n.centerOffset)!==0,v=f?0:e.arrowWidth,g=f?0:e.arrowHeight,[h,p]=_c(c),b={start:"0%",center:"50%",end:"100%"}[p],x=((r=(o=u.arrow)===null||o===void 0?void 0:o.x)!==null&&r!==void 0?r:0)+v/2,y=((s=(i=u.arrow)===null||i===void 0?void 0:i.y)!==null&&s!==void 0?s:0)+g/2;let w="",C="";return h==="bottom"?(w=f?b:`${x}px`,C=`${-g}px`):h==="top"?(w=f?b:`${x}px`,C=`${l.floating.height+g}px`):h==="right"?(w=`${-g}px`,C=f?b:`${y}px`):h==="left"&&(w=`${l.floating.width+g}px`,C=f?b:`${y}px`),{data:{x:w,y:C}}}});function _c(e){const[t,n="center"]=e.split("-");return[t,n]}const Tv=wv,Nv=Ev,Av=$v,Ov=a.forwardRef((e,t)=>{var n;const{container:r=globalThis==null||(n=globalThis.document)===null||n===void 0?void 0:n.body,...o}=e;return r?Vs.createPortal(a.createElement(ye.div,U({},o,{ref:t})),r):null});function _v(e,t){return a.useReducer((n,r)=>{const o=t[n][r];return o??n},e)}const Vt=e=>{const{present:t,children:n}=e,r=Mv(t),o=typeof n=="function"?n({present:r.isPresent}):a.Children.only(n),s=ve(r.ref,o.ref);return typeof n=="function"||r.isPresent?a.cloneElement(o,{ref:s}):null};Vt.displayName="Presence";function Mv(e){const[t,n]=a.useState(),r=a.useRef({}),o=a.useRef(e),s=a.useRef("none"),i=e?"mounted":"unmounted",[c,l]=_v(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return a.useEffect(()=>{const u=en(r.current);s.current=c==="mounted"?u:"none"},[c]),mt(()=>{const u=r.current,d=o.current;if(d!==e){const v=s.current,g=en(u);e?l("MOUNT"):g==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&v!==g?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),mt(()=>{if(t){const u=f=>{const g=en(r.current).includes(f.animationName);f.target===t&&g&&Fe.flushSync(()=>l("ANIMATION_END"))},d=f=>{f.target===t&&(s.current=en(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:a.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function en(e){return(e==null?void 0:e.animationName)||"none"}const ur="rovingFocusGroup.onEntryFocus",Iv={bubbles:!1,cancelable:!0},Io="RovingFocusGroup",[jr,Mc,Dv]=Ec(Io),[jv,Ic]=Ht(Io,[Dv]),[kv,Lv]=jv(Io),Fv=a.forwardRef((e,t)=>a.createElement(jr.Provider,{scope:e.__scopeRovingFocusGroup},a.createElement(jr.Slot,{scope:e.__scopeRovingFocusGroup},a.createElement(Bv,U({},e,{ref:t}))))),Bv=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:i,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:l,onEntryFocus:u,...d}=e,f=a.useRef(null),v=ve(t,f),g=Sc(s),[h=null,p]=wc({prop:i,defaultProp:c,onChange:l}),[b,x]=a.useState(!1),y=Ae(u),w=Mc(n),C=a.useRef(!1),[E,P]=a.useState(0);return a.useEffect(()=>{const S=f.current;if(S)return S.addEventListener(ur,y),()=>S.removeEventListener(ur,y)},[y]),a.createElement(kv,{scope:n,orientation:r,dir:g,loop:o,currentTabStopId:h,onItemFocus:a.useCallback(S=>p(S),[p]),onItemShiftTab:a.useCallback(()=>x(!0),[]),onFocusableItemAdd:a.useCallback(()=>P(S=>S+1),[]),onFocusableItemRemove:a.useCallback(()=>P(S=>S-1),[])},a.createElement(ye.div,U({tabIndex:b||E===0?-1:0,"data-orientation":r},d,{ref:v,style:{outline:"none",...e.style},onMouseDown:z(e.onMouseDown,()=>{C.current=!0}),onFocus:z(e.onFocus,S=>{const R=!C.current;if(S.target===S.currentTarget&&R&&!b){const N=new CustomEvent(ur,Iv);if(S.currentTarget.dispatchEvent(N),!N.defaultPrevented){const M=w().filter(A=>A.focusable),D=M.find(A=>A.active),I=M.find(A=>A.id===h),k=[D,I,...M].filter(Boolean).map(A=>A.ref.current);Dc(k)}}C.current=!1}),onBlur:z(e.onBlur,()=>x(!1))})))}),Uv="RovingFocusGroupItem",Wv=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,...i}=e,c=Dr(),l=s||c,u=Lv(Uv,n),d=u.currentTabStopId===l,f=Mc(n),{onFocusableItemAdd:v,onFocusableItemRemove:g}=u;return a.useEffect(()=>{if(r)return v(),()=>g()},[r,v,g]),a.createElement(jr.ItemSlot,{scope:n,id:l,focusable:r,active:o},a.createElement(ye.span,U({tabIndex:d?0:-1,"data-orientation":u.orientation},i,{ref:t,onMouseDown:z(e.onMouseDown,h=>{r?u.onItemFocus(l):h.preventDefault()}),onFocus:z(e.onFocus,()=>u.onItemFocus(l)),onKeyDown:z(e.onKeyDown,h=>{if(h.key==="Tab"&&h.shiftKey){u.onItemShiftTab();return}if(h.target!==h.currentTarget)return;const p=zv(h,u.orientation,u.dir);if(p!==void 0){h.preventDefault();let x=f().filter(y=>y.focusable).map(y=>y.ref.current);if(p==="last")x.reverse();else if(p==="prev"||p==="next"){p==="prev"&&x.reverse();const y=x.indexOf(h.currentTarget);x=u.loop?Kv(x,y+1):x.slice(y+1)}setTimeout(()=>Dc(x))}})})))}),Hv={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Vv(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function zv(e,t,n){const r=Vv(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Hv[r]}function Dc(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function Kv(e,t){return e.map((n,r)=>e[(t+r)%e.length])}const Gv=Fv,qv=Wv;var jc=Zi(),dr=function(){},Kn=a.forwardRef(function(e,t){var n=a.useRef(null),r=a.useState({onScrollCapture:dr,onWheelCapture:dr,onTouchMoveCapture:dr}),o=r[0],s=r[1],i=e.forwardProps,c=e.children,l=e.className,u=e.removeScrollBar,d=e.enabled,f=e.shards,v=e.sideCar,g=e.noIsolation,h=e.inert,p=e.allowPinchZoom,b=e.as,x=b===void 0?"div":b,y=Po(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),w=v,C=Ji([n,t]),E=le(le({},y),o);return a.createElement(a.Fragment,null,d&&a.createElement(w,{sideCar:jc,removeScrollBar:u,shards:f,noIsolation:g,inert:h,setCallbacks:s,allowPinchZoom:!!p,lockRef:n}),i?a.cloneElement(a.Children.only(c),le(le({},E),{ref:C})):a.createElement(x,le({},E,{className:l,ref:C}),c))});Kn.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Kn.classNames={fullWidth:Tt,zeroRight:Pt};var kr=!1;if(typeof window<"u")try{var tn=Object.defineProperty({},"passive",{get:function(){return kr=!0,!0}});window.addEventListener("test",tn,tn),window.removeEventListener("test",tn,tn)}catch{kr=!1}var it=kr?{passive:!1}:!1,Yv=function(e){return e.tagName==="TEXTAREA"},kc=function(e,t){var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Yv(e)&&n[t]==="visible")},Xv=function(e){return kc(e,"overflowY")},Jv=function(e){return kc(e,"overflowX")},Ls=function(e,t){var n=t;do{typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&(n=n.host);var r=Lc(e,n);if(r){var o=Fc(e,n),s=o[1],i=o[2];if(s>i)return!0}n=n.parentNode}while(n&&n!==document.body);return!1},Zv=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Qv=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Lc=function(e,t){return e==="v"?Xv(t):Jv(t)},Fc=function(e,t){return e==="v"?Zv(t):Qv(t)},eg=function(e,t){return e==="h"&&t==="rtl"?-1:1},tg=function(e,t,n,r,o){var s=eg(e,window.getComputedStyle(t).direction),i=s*r,c=n.target,l=t.contains(c),u=!1,d=i>0,f=0,v=0;do{var g=Fc(e,c),h=g[0],p=g[1],b=g[2],x=p-b-s*h;(h||x)&&Lc(e,c)&&(f+=x,v+=h),c=c.parentNode}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return(d&&(o&&f===0||!o&&i>f)||!d&&(o&&v===0||!o&&-i>v))&&(u=!0),u},nn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Fs=function(e){return[e.deltaX,e.deltaY]},Bs=function(e){return e&&"current"in e?e.current:e},ng=function(e,t){return e[0]===t[0]&&e[1]===t[1]},rg=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},og=0,at=[];function sg(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(og++)[0],s=a.useState(function(){return To()})[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var p=Xi([e.lockRef.current],(e.shards||[]).map(Bs),!0).filter(Boolean);return p.forEach(function(b){return b.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),p.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=a.useCallback(function(p,b){if("touches"in p&&p.touches.length===2)return!i.current.allowPinchZoom;var x=nn(p),y=n.current,w="deltaX"in p?p.deltaX:y[0]-x[0],C="deltaY"in p?p.deltaY:y[1]-x[1],E,P=p.target,S=Math.abs(w)>Math.abs(C)?"h":"v";if("touches"in p&&S==="h"&&P.type==="range")return!1;var R=Ls(S,P);if(!R)return!0;if(R?E=S:(E=S==="v"?"h":"v",R=Ls(S,P)),!R)return!1;if(!r.current&&"changedTouches"in p&&(w||C)&&(r.current=E),!E)return!0;var N=r.current||E;return tg(N,b,p,N==="h"?w:C,!0)},[]),l=a.useCallback(function(p){var b=p;if(!(!at.length||at[at.length-1]!==s)){var x="deltaY"in b?Fs(b):nn(b),y=t.current.filter(function(E){return E.name===b.type&&E.target===b.target&&ng(E.delta,x)})[0];if(y&&y.should){b.cancelable&&b.preventDefault();return}if(!y){var w=(i.current.shards||[]).map(Bs).filter(Boolean).filter(function(E){return E.contains(b.target)}),C=w.length>0?c(b,w[0]):!i.current.noIsolation;C&&b.cancelable&&b.preventDefault()}}},[]),u=a.useCallback(function(p,b,x,y){var w={name:p,delta:b,target:x,should:y};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(C){return C!==w})},1)},[]),d=a.useCallback(function(p){n.current=nn(p),r.current=void 0},[]),f=a.useCallback(function(p){u(p.type,Fs(p),p.target,c(p,e.lockRef.current))},[]),v=a.useCallback(function(p){u(p.type,nn(p),p.target,c(p,e.lockRef.current))},[]);a.useEffect(function(){return at.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",l,it),document.addEventListener("touchmove",l,it),document.addEventListener("touchstart",d,it),function(){at=at.filter(function(p){return p!==s}),document.removeEventListener("wheel",l,it),document.removeEventListener("touchmove",l,it),document.removeEventListener("touchstart",d,it)}},[]);var g=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(s,{styles:rg(o)}):null,g?a.createElement(na,{gapMode:"margin"}):null)}const ig=ea(jc,sg);var Bc=a.forwardRef(function(e,t){return a.createElement(Kn,le({},e,{ref:t,sideCar:ig}))});Bc.classNames=Kn.classNames;const ag=Bc,Lr=["Enter"," "],cg=["ArrowDown","PageUp","Home"],Uc=["ArrowUp","PageDown","End"],lg=[...cg,...Uc],ug={ltr:[...Lr,"ArrowRight"],rtl:[...Lr,"ArrowLeft"]},dg={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Gn="Menu",[jt,fg,pg]=Ec(Gn),[tt,Wc]=Ht(Gn,[pg,Nc,Ic]),Do=Nc(),Hc=Ic(),[mg,nt]=tt(Gn),[hg,zt]=tt(Gn),vg=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:s,modal:i=!0}=e,c=Do(t),[l,u]=a.useState(null),d=a.useRef(!1),f=Ae(s),v=Sc(o);return a.useEffect(()=>{const g=()=>{d.current=!0,document.addEventListener("pointerdown",h,{capture:!0,once:!0}),document.addEventListener("pointermove",h,{capture:!0,once:!0})},h=()=>d.current=!1;return document.addEventListener("keydown",g,{capture:!0}),()=>{document.removeEventListener("keydown",g,{capture:!0}),document.removeEventListener("pointerdown",h,{capture:!0}),document.removeEventListener("pointermove",h,{capture:!0})}},[]),a.createElement(Tv,c,a.createElement(mg,{scope:t,open:n,onOpenChange:f,content:l,onContentChange:u},a.createElement(hg,{scope:t,onClose:a.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:d,dir:v,modal:i},r)))},Vc=a.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Do(n);return a.createElement(Nv,U({},o,r,{ref:t}))}),zc="MenuPortal",[gg,Kc]=tt(zc,{forceMount:void 0}),bg=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,s=nt(zc,t);return a.createElement(gg,{scope:t,forceMount:n},a.createElement(Vt,{present:n||s.open},a.createElement(Ov,{asChild:!0,container:o},r)))},Se="MenuContent",[xg,jo]=tt(Se),yg=a.forwardRef((e,t)=>{const n=Kc(Se,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=nt(Se,e.__scopeMenu),i=zt(Se,e.__scopeMenu);return a.createElement(jt.Provider,{scope:e.__scopeMenu},a.createElement(Vt,{present:r||s.open},a.createElement(jt.Slot,{scope:e.__scopeMenu},i.modal?a.createElement(wg,U({},o,{ref:t})):a.createElement(Cg,U({},o,{ref:t})))))}),wg=a.forwardRef((e,t)=>{const n=nt(Se,e.__scopeMenu),r=a.useRef(null),o=ve(t,r);return a.useEffect(()=>{const s=r.current;if(s)return Yi(s)},[]),a.createElement(ko,U({},e,{ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:z(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)}))}),Cg=a.forwardRef((e,t)=>{const n=nt(Se,e.__scopeMenu);return a.createElement(ko,U({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)}))}),ko=a.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:i,disableOutsidePointerEvents:c,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:v,onDismiss:g,disableOutsideScroll:h,...p}=e,b=nt(Se,n),x=zt(Se,n),y=Do(n),w=Hc(n),C=fg(n),[E,P]=a.useState(null),S=a.useRef(null),R=ve(t,S,b.onContentChange),N=a.useRef(0),M=a.useRef(""),D=a.useRef(0),I=a.useRef(null),F=a.useRef("right"),k=a.useRef(0),A=h?ag:a.Fragment,j=h?{as:fn,allowPinchZoom:!0}:void 0,O=T=>{var J,X;const re=M.current+T,se=C().filter(H=>!H.disabled),ie=document.activeElement,ae=(J=se.find(H=>H.ref.current===ie))===null||J===void 0?void 0:J.textValue,Z=se.map(H=>H.textValue),_=Lg(Z,re,ae),G=(X=se.find(H=>H.textValue===_))===null||X===void 0?void 0:X.ref.current;(function H(V){M.current=V,window.clearTimeout(N.current),V!==""&&(N.current=window.setTimeout(()=>H(""),1e3))})(re),G&&setTimeout(()=>G.focus())};a.useEffect(()=>()=>window.clearTimeout(N.current),[]),lv();const L=a.useCallback(T=>{var J,X;return F.current===((J=I.current)===null||J===void 0?void 0:J.side)&&Bg(T,(X=I.current)===null||X===void 0?void 0:X.area)},[]);return a.createElement(xg,{scope:n,searchRef:M,onItemEnter:a.useCallback(T=>{L(T)&&T.preventDefault()},[L]),onItemLeave:a.useCallback(T=>{var J;L(T)||((J=S.current)===null||J===void 0||J.focus(),P(null))},[L]),onTriggerLeave:a.useCallback(T=>{L(T)&&T.preventDefault()},[L]),pointerGraceTimerRef:D,onPointerGraceIntentChange:a.useCallback(T=>{I.current=T},[])},a.createElement(A,j,a.createElement(uv,{asChild:!0,trapped:o,onMountAutoFocus:z(s,T=>{var J;T.preventDefault(),(J=S.current)===null||J===void 0||J.focus()}),onUnmountAutoFocus:i},a.createElement(iv,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:v,onDismiss:g},a.createElement(Gv,U({asChild:!0},w,{dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:E,onCurrentTabStopIdChange:P,onEntryFocus:z(l,T=>{x.isUsingKeyboardRef.current||T.preventDefault()})}),a.createElement(Av,U({role:"menu","aria-orientation":"vertical","data-state":Jc(b.open),"data-radix-menu-content":"",dir:x.dir},y,p,{ref:R,style:{outline:"none",...p.style},onKeyDown:z(p.onKeyDown,T=>{const X=T.target.closest("[data-radix-menu-content]")===T.currentTarget,re=T.ctrlKey||T.altKey||T.metaKey,se=T.key.length===1;X&&(T.key==="Tab"&&T.preventDefault(),!re&&se&&O(T.key));const ie=S.current;if(T.target!==ie||!lg.includes(T.key))return;T.preventDefault();const Z=C().filter(_=>!_.disabled).map(_=>_.ref.current);Uc.includes(T.key)&&Z.reverse(),jg(Z)}),onBlur:z(e.onBlur,T=>{T.currentTarget.contains(T.target)||(window.clearTimeout(N.current),M.current="")}),onPointerMove:z(e.onPointerMove,kt(T=>{const J=T.target,X=k.current!==T.clientX;if(T.currentTarget.contains(J)&&X){const re=T.clientX>k.current?"right":"left";F.current=re,k.current=T.clientX}}))})))))))}),Eg=a.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return a.createElement(ye.div,U({},r,{ref:t}))}),Fr="MenuItem",Us="menu.itemSelect",Lo=a.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,s=a.useRef(null),i=zt(Fr,e.__scopeMenu),c=jo(Fr,e.__scopeMenu),l=ve(t,s),u=a.useRef(!1),d=()=>{const f=s.current;if(!n&&f){const v=new CustomEvent(Us,{bubbles:!0,cancelable:!0});f.addEventListener(Us,g=>r==null?void 0:r(g),{once:!0}),Cc(f,v),v.defaultPrevented?u.current=!1:i.onClose()}};return a.createElement(Gc,U({},o,{ref:l,disabled:n,onClick:z(e.onClick,d),onPointerDown:f=>{var v;(v=e.onPointerDown)===null||v===void 0||v.call(e,f),u.current=!0},onPointerUp:z(e.onPointerUp,f=>{var v;u.current||(v=f.currentTarget)===null||v===void 0||v.click()}),onKeyDown:z(e.onKeyDown,f=>{const v=c.searchRef.current!=="";n||v&&f.key===" "||Lr.includes(f.key)&&(f.currentTarget.click(),f.preventDefault())})}))}),Gc=a.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...s}=e,i=jo(Fr,n),c=Hc(n),l=a.useRef(null),u=ve(t,l),[d,f]=a.useState(!1),[v,g]=a.useState("");return a.useEffect(()=>{const h=l.current;if(h){var p;g(((p=h.textContent)!==null&&p!==void 0?p:"").trim())}},[s.children]),a.createElement(jt.ItemSlot,{scope:n,disabled:r,textValue:o??v},a.createElement(qv,U({asChild:!0},c,{focusable:!r}),a.createElement(ye.div,U({role:"menuitem","data-highlighted":d?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0},s,{ref:u,onPointerMove:z(e.onPointerMove,kt(h=>{r?i.onItemLeave(h):(i.onItemEnter(h),h.defaultPrevented||h.currentTarget.focus())})),onPointerLeave:z(e.onPointerLeave,kt(h=>i.onItemLeave(h))),onFocus:z(e.onFocus,()=>f(!0)),onBlur:z(e.onBlur,()=>f(!1))}))))}),Sg=a.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return a.createElement(Yc,{scope:e.__scopeMenu,checked:n},a.createElement(Lo,U({role:"menuitemcheckbox","aria-checked":Cn(n)?"mixed":n},o,{ref:t,"data-state":Fo(n),onSelect:z(o.onSelect,()=>r==null?void 0:r(Cn(n)?!0:!n),{checkForDefaultPrevented:!1})})))}),$g="MenuRadioGroup",[hy,Rg]=tt($g,{value:void 0,onValueChange:()=>{}}),Pg="MenuRadioItem",Tg=a.forwardRef((e,t)=>{const{value:n,...r}=e,o=Rg(Pg,e.__scopeMenu),s=n===o.value;return a.createElement(Yc,{scope:e.__scopeMenu,checked:s},a.createElement(Lo,U({role:"menuitemradio","aria-checked":s},r,{ref:t,"data-state":Fo(s),onSelect:z(r.onSelect,()=>{var i;return(i=o.onValueChange)===null||i===void 0?void 0:i.call(o,n)},{checkForDefaultPrevented:!1})})))}),qc="MenuItemIndicator",[Yc,Ng]=tt(qc,{checked:!1}),Ag=a.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,s=Ng(qc,n);return a.createElement(Vt,{present:r||Cn(s.checked)||s.checked===!0},a.createElement(ye.span,U({},o,{ref:t,"data-state":Fo(s.checked)})))}),Og=a.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return a.createElement(ye.div,U({role:"separator","aria-orientation":"horizontal"},r,{ref:t}))}),_g="MenuSub",[vy,Xc]=tt(_g),rn="MenuSubTrigger",Mg=a.forwardRef((e,t)=>{const n=nt(rn,e.__scopeMenu),r=zt(rn,e.__scopeMenu),o=Xc(rn,e.__scopeMenu),s=jo(rn,e.__scopeMenu),i=a.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:l}=s,u={__scopeMenu:e.__scopeMenu},d=a.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return a.useEffect(()=>d,[d]),a.useEffect(()=>{const f=c.current;return()=>{window.clearTimeout(f),l(null)}},[c,l]),a.createElement(Vc,U({asChild:!0},u),a.createElement(Gc,U({id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":Jc(n.open)},e,{ref:zs(t,o.onTriggerChange),onClick:f=>{var v;(v=e.onClick)===null||v===void 0||v.call(e,f),!(e.disabled||f.defaultPrevented)&&(f.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:z(e.onPointerMove,kt(f=>{s.onItemEnter(f),!f.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(s.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),d()},100))})),onPointerLeave:z(e.onPointerLeave,kt(f=>{var v;d();const g=(v=n.content)===null||v===void 0?void 0:v.getBoundingClientRect();if(g){var h;const p=(h=n.content)===null||h===void 0?void 0:h.dataset.side,b=p==="right",x=b?-5:5,y=g[b?"left":"right"],w=g[b?"right":"left"];s.onPointerGraceIntentChange({area:[{x:f.clientX+x,y:f.clientY},{x:y,y:g.top},{x:w,y:g.top},{x:w,y:g.bottom},{x:y,y:g.bottom}],side:p}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(f),f.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:z(e.onKeyDown,f=>{const v=s.searchRef.current!=="";if(!(e.disabled||v&&f.key===" ")&&ug[r.dir].includes(f.key)){var g;n.onOpenChange(!0),(g=n.content)===null||g===void 0||g.focus(),f.preventDefault()}})})))}),Ig="MenuSubContent",Dg=a.forwardRef((e,t)=>{const n=Kc(Se,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=nt(Se,e.__scopeMenu),i=zt(Se,e.__scopeMenu),c=Xc(Ig,e.__scopeMenu),l=a.useRef(null),u=ve(t,l);return a.createElement(jt.Provider,{scope:e.__scopeMenu},a.createElement(Vt,{present:r||s.open},a.createElement(jt.Slot,{scope:e.__scopeMenu},a.createElement(ko,U({id:c.contentId,"aria-labelledby":c.triggerId},o,{ref:u,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:d=>{var f;i.isUsingKeyboardRef.current&&((f=l.current)===null||f===void 0||f.focus()),d.preventDefault()},onCloseAutoFocus:d=>d.preventDefault(),onFocusOutside:z(e.onFocusOutside,d=>{d.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:z(e.onEscapeKeyDown,d=>{i.onClose(),d.preventDefault()}),onKeyDown:z(e.onKeyDown,d=>{const f=d.currentTarget.contains(d.target),v=dg[i.dir].includes(d.key);if(f&&v){var g;s.onOpenChange(!1),(g=c.trigger)===null||g===void 0||g.focus(),d.preventDefault()}})})))))});function Jc(e){return e?"open":"closed"}function Cn(e){return e==="indeterminate"}function Fo(e){return Cn(e)?"indeterminate":e?"checked":"unchecked"}function jg(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function kg(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function Lg(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=kg(e,Math.max(s,0));o.length===1&&(i=i.filter(u=>u!==n));const l=i.find(u=>u.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function Fg(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const c=t[s].x,l=t[s].y,u=t[i].x,d=t[i].y;l>r!=d>r&&n<(u-c)*(r-l)/(d-l)+c&&(o=!o)}return o}function Bg(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return Fg(n,t)}function kt(e){return t=>t.pointerType==="mouse"?e(t):void 0}const Ug=vg,Wg=Vc,Hg=bg,Vg=yg,zg=Eg,Kg=Lo,Gg=Sg,qg=Tg,Yg=Ag,Xg=Og,Jg=Mg,Zg=Dg,Zc="DropdownMenu",[Qg,gy]=Ht(Zc,[Wc]),we=Wc(),[eb,Qc]=Qg(Zc),tb=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:s,onOpenChange:i,modal:c=!0}=e,l=we(t),u=a.useRef(null),[d=!1,f]=wc({prop:o,defaultProp:s,onChange:i});return a.createElement(eb,{scope:t,triggerId:Dr(),triggerRef:u,contentId:Dr(),open:d,onOpenChange:f,onOpenToggle:a.useCallback(()=>f(v=>!v),[f]),modal:c},a.createElement(Ug,U({},l,{open:d,onOpenChange:f,dir:r,modal:c}),n))},nb="DropdownMenuTrigger",rb=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,s=Qc(nb,n),i=we(n);return a.createElement(Wg,U({asChild:!0},i),a.createElement(ye.button,U({type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":r?"":void 0,disabled:r},o,{ref:zs(t,s.triggerRef),onPointerDown:z(e.onPointerDown,c=>{!r&&c.button===0&&c.ctrlKey===!1&&(s.onOpenToggle(),s.open||c.preventDefault())}),onKeyDown:z(e.onKeyDown,c=>{r||(["Enter"," "].includes(c.key)&&s.onOpenToggle(),c.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})))}),ob=e=>{const{__scopeDropdownMenu:t,...n}=e,r=we(t);return a.createElement(Hg,U({},r,n))},sb="DropdownMenuContent",ib=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Qc(sb,n),s=we(n),i=a.useRef(!1);return a.createElement(Vg,U({id:o.contentId,"aria-labelledby":o.triggerId},s,r,{ref:t,onCloseAutoFocus:z(e.onCloseAutoFocus,c=>{var l;i.current||(l=o.triggerRef.current)===null||l===void 0||l.focus(),i.current=!1,c.preventDefault()}),onInteractOutside:z(e.onInteractOutside,c=>{const l=c.detail.originalEvent,u=l.button===0&&l.ctrlKey===!0,d=l.button===2||u;(!o.modal||d)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}}))}),ab=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=we(n);return a.createElement(zg,U({},o,r,{ref:t}))}),cb=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=we(n);return a.createElement(Kg,U({},o,r,{ref:t}))}),lb=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=we(n);return a.createElement(Gg,U({},o,r,{ref:t}))}),ub=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=we(n);return a.createElement(qg,U({},o,r,{ref:t}))}),db=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=we(n);return a.createElement(Yg,U({},o,r,{ref:t}))}),fb=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=we(n);return a.createElement(Xg,U({},o,r,{ref:t}))}),pb=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=we(n);return a.createElement(Jg,U({},o,r,{ref:t}))}),mb=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=we(n);return a.createElement(Zg,U({},o,r,{ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}}))}),hb=tb,vb=rb,gb=ob,el=ib,tl=ab,nl=cb,rl=lb,ol=ub,sl=db,il=fb,al=pb,cl=mb,Br=hb,Ur=vb,bb=a.forwardRef(({className:e,inset:t,children:n,...r},o)=>m.jsxs(al,{ref:o,className:K("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-card data-[state=open]:bg-card",t&&"pl-8",e),...r,children:[n,m.jsx(Gs,{className:"ml-auto h-4 w-4"})]}));bb.displayName=al.displayName;const xb=a.forwardRef(({className:e,...t},n)=>m.jsx(cl,{ref:n,className:K("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-muted p-1 text-muted-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));xb.displayName=cl.displayName;const En=a.forwardRef(({className:e,sideOffset:t=4,...n},r)=>m.jsx(gb,{children:m.jsx(el,{ref:r,sideOffset:t,className:K("z-50 min-w-[8rem] overflow-hidden rounded-md bg-[var(--card-background)] p-1 text-[var(--headline)] shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));En.displayName=el.displayName;const Sn=a.forwardRef(({className:e,inset:t,...n},r)=>m.jsx(nl,{ref:r,className:K("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-card focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n}));Sn.displayName=nl.displayName;const yb=a.forwardRef(({className:e,children:t,checked:n,...r},o)=>m.jsxs(rl,{ref:o,className:K("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-card focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[m.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:m.jsx(sl,{children:m.jsx(eo,{className:"h-4 w-4"})})}),t]}));yb.displayName=rl.displayName;const wb=a.forwardRef(({className:e,children:t,...n},r)=>m.jsxs(ol,{ref:r,className:K("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-card focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[m.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:m.jsx(sl,{children:m.jsx(Pu,{className:"h-2 w-2 fill-current"})})}),t]}));wb.displayName=ol.displayName;const Cb=a.forwardRef(({className:e,inset:t,...n},r)=>m.jsx(tl,{ref:r,className:K("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));Cb.displayName=tl.displayName;const Eb=a.forwardRef(({className:e,...t},n)=>m.jsx(il,{ref:n,className:K("-mx-1 my-1 h-px bg-muted",e),...t}));Eb.displayName=il.displayName;function Sb({children:e,className:t,gradientSize:n=200,gradientColor:r="border",gradientOpacity:o=.8}){const s=a.useRef(null),i=At(-n),c=At(-n),l=a.useCallback(f=>{if(s.current){const{left:v,top:g}=s.current.getBoundingClientRect(),h=f.clientX,p=f.clientY;i.set(h-v),c.set(p-g)}},[i,c]),u=a.useCallback(f=>{f.relatedTarget||(document.removeEventListener("mousemove",l),i.set(-n),c.set(-n))},[l,i,n,c]),d=a.useCallback(()=>{document.addEventListener("mousemove",l),i.set(-n),c.set(-n)},[l,i,n,c]);return a.useEffect(()=>(document.addEventListener("mousemove",l),document.addEventListener("mouseout",u),document.addEventListener("mouseenter",d),()=>{document.removeEventListener("mousemove",l),document.removeEventListener("mouseout",u),document.removeEventListener("mouseenter",d)}),[d,l,u]),a.useEffect(()=>{i.set(-n),c.set(-n)},[n,i,c]),m.jsxs("div",{"data-aos":"fade-up",ref:s,className:K("group relative flex size-full overflow-hidden rounded-xl border border-border bg-card text-black dark:bg-neutral-900 dark:text-white",t),children:[m.jsx("div",{className:"relative z-10 w-full",children:e}),m.jsx(me.div,{className:"pointer-events-none absolute -inset-px rounded-xl opacity-0 transition-opacity duration-300 group-hover:opacity-100",style:{background:bu`
            radial-gradient(${n}px circle at ${i}px ${c}px, ${r}, transparent 100%)
          `,opacity:o}})]})}const $b=({style:e,gradientType:t,gradient:n})=>{const r=n.colors[0];return m.jsxs("header",{className:"relative w-full",children:[m.jsx("div",{className:"absolute inset-0 opacity-50 blur-[42px]",style:{background:`radial-gradient(circle, ${r} 0%, transparent 30%)`,transform:"translate(-25%, -25%) scale(1.5)"}}),m.jsx(me.div,{className:"relative left-0 right-0 m-auto mt-6 flex h-48 w-48 items-center justify-center overflow-hidden rounded-full",style:t==="background"?{...e}:{},transition:{duration:.3},children:t==="text"?m.jsx(me.span,{className:"overflow-hidden text-nowrap p-2 text-2xl font-bold",style:e,initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:n.name}):m.jsx(me.span,{className:"text-2xl font-bold text-white",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3}})})]})},Rb=({name:e,isFavorite:t,onFavoriteToggle:n})=>m.jsxs("div",{className:"flex w-full items-center justify-between",children:[m.jsx("h3",{className:"text-lg font-semibold text-primary",children:e}),m.jsxs(xc,{children:[m.jsx(yc,{asChild:!0,children:m.jsx("div",{className:"flex items-center",children:m.jsx(me.button,{className:"rounded-full p-2 transition-colors",onClick:()=>n(e),whileHover:{scale:1.1},whileTap:{scale:.9},children:m.jsx(Ou,{className:K("h-5 w-5",t?"fill-current text-red-400":"text-gray-400")})})})}),m.jsx(Mo,{children:m.jsx("p",{children:t?"Remove from favorites":"Add to favorites"})})]})]}),Pb=({colors:e,getColorInFormat:t,copyToClipboard:n})=>m.jsx("div",{className:"flex flex-wrap gap-2",children:e.map((r,o)=>m.jsxs(xc,{children:[m.jsx(yc,{asChild:!0,children:m.jsx(me.div,{className:"hoverd h-6 w-6 cursor-pointer rounded-full border border-border",style:{backgroundColor:r},onClick:()=>n(t(r),"colors"),whileHover:{scale:1.2},whileTap:{scale:.9}})}),m.jsx(Mo,{children:m.jsxs("p",{className:"text-muted-foreground",children:["Click to copy: ",t(r)]})})]},o))});function ct(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Tb(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function ll(...e){return t=>e.forEach(n=>Tb(n,t))}function Qe(...e){return a.useCallback(ll(...e),e)}function Nb(e,t=[]){let n=[];function r(s,i){const c=a.createContext(i),l=n.length;n=[...n,i];const u=f=>{var x;const{scope:v,children:g,...h}=f,p=((x=v==null?void 0:v[e])==null?void 0:x[l])||c,b=a.useMemo(()=>h,Object.values(h));return m.jsx(p.Provider,{value:b,children:g})};u.displayName=s+"Provider";function d(f,v){var p;const g=((p=v==null?void 0:v[e])==null?void 0:p[l])||c,h=a.useContext(g);if(h)return h;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return[u,d]}const o=()=>{const s=n.map(i=>a.createContext(i));return function(c){const l=(c==null?void 0:c[e])||s;return a.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,Ab(o,...t)]}function Ab(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((c,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...c,...f}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function ul(e){const t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Ob({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=_b({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,c=ul(n),l=a.useCallback(u=>{if(s){const f=typeof u=="function"?u(e):u;f!==e&&c(f)}else o(u)},[s,e,o,c]);return[i,l]}function _b({defaultProp:e,onChange:t}){const n=a.useState(e),[r]=n,o=a.useRef(r),s=ul(t);return a.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var Mb=a.createContext(void 0);function Ib(e){const t=a.useContext(Mb);return e||t||"ltr"}var Db=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{};function jb(e){const[t,n]=a.useState(void 0);return Db(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;i=u.inlineSize,c=u.blockSize}else i=e.offsetWidth,c=e.offsetHeight;n({width:i,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var $n=a.forwardRef((e,t)=>{const{children:n,...r}=e,o=a.Children.toArray(n),s=o.find(Lb);if(s){const i=s.props.children,c=o.map(l=>l===s?a.Children.count(i)>1?a.Children.only(null):a.isValidElement(i)?i.props.children:null:l);return m.jsx(Wr,{...r,ref:t,children:a.isValidElement(i)?a.cloneElement(i,void 0,c):null})}return m.jsx(Wr,{...r,ref:t,children:n})});$n.displayName="Slot";var Wr=a.forwardRef((e,t)=>{const{children:n,...r}=e;if(a.isValidElement(n)){const o=Bb(n);return a.cloneElement(n,{...Fb(r,n.props),ref:t?ll(t,o):o})}return a.Children.count(n)>1?a.Children.only(null):null});Wr.displayName="SlotClone";var kb=({children:e})=>m.jsx(m.Fragment,{children:e});function Lb(e){return a.isValidElement(e)&&e.type===kb}function Fb(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...c)=>{s(...c),o(...c)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Bb(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Ub=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],qn=Ub.reduce((e,t)=>{const n=a.forwardRef((r,o)=>{const{asChild:s,...i}=r,c=s?$n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),m.jsx(c,{...i,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Wb(e,t=[]){let n=[];function r(s,i){const c=a.createContext(i),l=n.length;n=[...n,i];function u(f){const{scope:v,children:g,...h}=f,p=(v==null?void 0:v[e][l])||c,b=a.useMemo(()=>h,Object.values(h));return m.jsx(p.Provider,{value:b,children:g})}function d(f,v){const g=(v==null?void 0:v[e][l])||c,h=a.useContext(g);if(h)return h;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(i=>a.createContext(i));return function(c){const l=(c==null?void 0:c[e])||s;return a.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,Hb(o,...t)]}function Hb(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((c,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...c,...f}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function Vb(e){const t=e+"CollectionProvider",[n,r]=Wb(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=g=>{const{scope:h,children:p}=g,b=W.useRef(null),x=W.useRef(new Map).current;return m.jsx(o,{scope:h,itemMap:x,collectionRef:b,children:p})};i.displayName=t;const c=e+"CollectionSlot",l=W.forwardRef((g,h)=>{const{scope:p,children:b}=g,x=s(c,p),y=Qe(h,x.collectionRef);return m.jsx($n,{ref:y,children:b})});l.displayName=c;const u=e+"CollectionItemSlot",d="data-radix-collection-item",f=W.forwardRef((g,h)=>{const{scope:p,children:b,...x}=g,y=W.useRef(null),w=Qe(h,y),C=s(u,p);return W.useEffect(()=>(C.itemMap.set(y,{ref:y,...x}),()=>void C.itemMap.delete(y))),m.jsx($n,{[d]:"",ref:w,children:b})});f.displayName=u;function v(g){const h=s(e+"CollectionConsumer",g);return W.useCallback(()=>{const b=h.collectionRef.current;if(!b)return[];const x=Array.from(b.querySelectorAll(`[${d}]`));return Array.from(h.itemMap.values()).sort((C,E)=>x.indexOf(C.ref.current)-x.indexOf(E.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:i,Slot:l,ItemSlot:f},v,r]}var dl=["PageUp","PageDown"],fl=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],pl={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},Ct="Slider",[Hr,zb,Kb]=Vb(Ct),[ml,by]=Nb(Ct,[Kb]),[Gb,Yn]=ml(Ct),hl=a.forwardRef((e,t)=>{const{name:n,min:r=0,max:o=100,step:s=1,orientation:i="horizontal",disabled:c=!1,minStepsBetweenThumbs:l=0,defaultValue:u=[r],value:d,onValueChange:f=()=>{},onValueCommit:v=()=>{},inverted:g=!1,form:h,...p}=e,b=a.useRef(new Set),x=a.useRef(0),w=i==="horizontal"?qb:Yb,[C=[],E]=Ob({prop:d,defaultProp:u,onChange:D=>{var F;(F=[...b.current][x.current])==null||F.focus(),f(D)}}),P=a.useRef(C);function S(D){const I=ex(C,D);M(D,I)}function R(D){M(D,x.current)}function N(){const D=P.current[x.current];C[x.current]!==D&&v(C)}function M(D,I,{commit:F}={commit:!1}){const k=ox(s),A=sx(Math.round((D-r)/s)*s+r,k),j=vn(A,[r,o]);E((O=[])=>{const L=Zb(O,j,I);if(rx(L,l*s)){x.current=L.indexOf(j);const T=String(L)!==String(O);return T&&F&&v(L),T?L:O}else return O})}return m.jsx(Gb,{scope:e.__scopeSlider,name:n,disabled:c,min:r,max:o,valueIndexToChangeRef:x,thumbs:b.current,values:C,orientation:i,form:h,children:m.jsx(Hr.Provider,{scope:e.__scopeSlider,children:m.jsx(Hr.Slot,{scope:e.__scopeSlider,children:m.jsx(w,{"aria-disabled":c,"data-disabled":c?"":void 0,...p,ref:t,onPointerDown:ct(p.onPointerDown,()=>{c||(P.current=C)}),min:r,max:o,inverted:g,onSlideStart:c?void 0:S,onSlideMove:c?void 0:R,onSlideEnd:c?void 0:N,onHomeKeyDown:()=>!c&&M(r,0,{commit:!0}),onEndKeyDown:()=>!c&&M(o,C.length-1,{commit:!0}),onStepKeyDown:({event:D,direction:I})=>{if(!c){const A=dl.includes(D.key)||D.shiftKey&&fl.includes(D.key)?10:1,j=x.current,O=C[j],L=s*A*I;M(O+L,j,{commit:!0})}}})})})})});hl.displayName=Ct;var[vl,gl]=ml(Ct,{startEdge:"left",endEdge:"right",size:"width",direction:1}),qb=a.forwardRef((e,t)=>{const{min:n,max:r,dir:o,inverted:s,onSlideStart:i,onSlideMove:c,onSlideEnd:l,onStepKeyDown:u,...d}=e,[f,v]=a.useState(null),g=Qe(t,w=>v(w)),h=a.useRef(),p=Ib(o),b=p==="ltr",x=b&&!s||!b&&s;function y(w){const C=h.current||f.getBoundingClientRect(),E=[0,C.width],S=Bo(E,x?[n,r]:[r,n]);return h.current=C,S(w-C.left)}return m.jsx(vl,{scope:e.__scopeSlider,startEdge:x?"left":"right",endEdge:x?"right":"left",direction:x?1:-1,size:"width",children:m.jsx(bl,{dir:p,"data-orientation":"horizontal",...d,ref:g,style:{...d.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:w=>{const C=y(w.clientX);i==null||i(C)},onSlideMove:w=>{const C=y(w.clientX);c==null||c(C)},onSlideEnd:()=>{h.current=void 0,l==null||l()},onStepKeyDown:w=>{const E=pl[x?"from-left":"from-right"].includes(w.key);u==null||u({event:w,direction:E?-1:1})}})})}),Yb=a.forwardRef((e,t)=>{const{min:n,max:r,inverted:o,onSlideStart:s,onSlideMove:i,onSlideEnd:c,onStepKeyDown:l,...u}=e,d=a.useRef(null),f=Qe(t,d),v=a.useRef(),g=!o;function h(p){const b=v.current||d.current.getBoundingClientRect(),x=[0,b.height],w=Bo(x,g?[r,n]:[n,r]);return v.current=b,w(p-b.top)}return m.jsx(vl,{scope:e.__scopeSlider,startEdge:g?"bottom":"top",endEdge:g?"top":"bottom",size:"height",direction:g?1:-1,children:m.jsx(bl,{"data-orientation":"vertical",...u,ref:f,style:{...u.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:p=>{const b=h(p.clientY);s==null||s(b)},onSlideMove:p=>{const b=h(p.clientY);i==null||i(b)},onSlideEnd:()=>{v.current=void 0,c==null||c()},onStepKeyDown:p=>{const x=pl[g?"from-bottom":"from-top"].includes(p.key);l==null||l({event:p,direction:x?-1:1})}})})}),bl=a.forwardRef((e,t)=>{const{__scopeSlider:n,onSlideStart:r,onSlideMove:o,onSlideEnd:s,onHomeKeyDown:i,onEndKeyDown:c,onStepKeyDown:l,...u}=e,d=Yn(Ct,n);return m.jsx(qn.span,{...u,ref:t,onKeyDown:ct(e.onKeyDown,f=>{f.key==="Home"?(i(f),f.preventDefault()):f.key==="End"?(c(f),f.preventDefault()):dl.concat(fl).includes(f.key)&&(l(f),f.preventDefault())}),onPointerDown:ct(e.onPointerDown,f=>{const v=f.target;v.setPointerCapture(f.pointerId),f.preventDefault(),d.thumbs.has(v)?v.focus():r(f)}),onPointerMove:ct(e.onPointerMove,f=>{f.target.hasPointerCapture(f.pointerId)&&o(f)}),onPointerUp:ct(e.onPointerUp,f=>{const v=f.target;v.hasPointerCapture(f.pointerId)&&(v.releasePointerCapture(f.pointerId),s(f))})})}),xl="SliderTrack",yl=a.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=Yn(xl,n);return m.jsx(qn.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...r,ref:t})});yl.displayName=xl;var Vr="SliderRange",wl=a.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=Yn(Vr,n),s=gl(Vr,n),i=a.useRef(null),c=Qe(t,i),l=o.values.length,u=o.values.map(v=>El(v,o.min,o.max)),d=l>1?Math.min(...u):0,f=100-Math.max(...u);return m.jsx(qn.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...r,ref:c,style:{...e.style,[s.startEdge]:d+"%",[s.endEdge]:f+"%"}})});wl.displayName=Vr;var zr="SliderThumb",Cl=a.forwardRef((e,t)=>{const n=zb(e.__scopeSlider),[r,o]=a.useState(null),s=Qe(t,c=>o(c)),i=a.useMemo(()=>r?n().findIndex(c=>c.ref.current===r):-1,[n,r]);return m.jsx(Xb,{...e,ref:s,index:i})}),Xb=a.forwardRef((e,t)=>{const{__scopeSlider:n,index:r,name:o,...s}=e,i=Yn(zr,n),c=gl(zr,n),[l,u]=a.useState(null),d=Qe(t,y=>u(y)),f=l?i.form||!!l.closest("form"):!0,v=jb(l),g=i.values[r],h=g===void 0?0:El(g,i.min,i.max),p=Qb(r,i.values.length),b=v==null?void 0:v[c.size],x=b?tx(b,h,c.direction):0;return a.useEffect(()=>{if(l)return i.thumbs.add(l),()=>{i.thumbs.delete(l)}},[l,i.thumbs]),m.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:`calc(${h}% + ${x}px)`},children:[m.jsx(Hr.ItemSlot,{scope:e.__scopeSlider,children:m.jsx(qn.span,{role:"slider","aria-label":e["aria-label"]||p,"aria-valuemin":i.min,"aria-valuenow":g,"aria-valuemax":i.max,"aria-orientation":i.orientation,"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,tabIndex:i.disabled?void 0:0,...s,ref:d,style:g===void 0?{display:"none"}:e.style,onFocus:ct(e.onFocus,()=>{i.valueIndexToChangeRef.current=r})})}),f&&m.jsx(Jb,{name:o??(i.name?i.name+(i.values.length>1?"[]":""):void 0),form:i.form,value:g},r)]})});Cl.displayName=zr;var Jb=e=>{const{value:t,...n}=e,r=a.useRef(null),o=Gi(t);return a.useEffect(()=>{const s=r.current,i=window.HTMLInputElement.prototype,l=Object.getOwnPropertyDescriptor(i,"value").set;if(o!==t&&l){const u=new Event("input",{bubbles:!0});l.call(s,t),s.dispatchEvent(u)}},[o,t]),m.jsx("input",{style:{display:"none"},...n,ref:r,defaultValue:t})};function Zb(e=[],t,n){const r=[...e];return r[n]=t,r.sort((o,s)=>o-s)}function El(e,t,n){const s=100/(n-t)*(e-t);return vn(s,[0,100])}function Qb(e,t){return t>2?`Value ${e+1} of ${t}`:t===2?["Minimum","Maximum"][e]:void 0}function ex(e,t){if(e.length===1)return 0;const n=e.map(o=>Math.abs(o-t)),r=Math.min(...n);return n.indexOf(r)}function tx(e,t,n){const r=e/2,s=Bo([0,50],[0,r]);return(r-s(t)*n)*n}function nx(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}function rx(e,t){if(t>0){const n=nx(e);return Math.min(...n)>=t}return!0}function Bo(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function ox(e){return(String(e).split(".")[1]||"").length}function sx(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}var Sl=hl,ix=yl,ax=wl,cx=Cl;const $l=a.forwardRef(({className:e,...t},n)=>m.jsxs(Sl,{ref:n,className:K("relative flex w-full touch-none select-none items-center",e),...t,children:[m.jsx(ix,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:m.jsx(ax,{className:"absolute h-full bg-gray-700"})}),m.jsx(cx,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));$l.displayName=Sl.displayName;var Rl={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Ws=W.createContext&&W.createContext(Rl),He=function(){return He=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},He.apply(this,arguments)},lx=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Pl(e){return e&&e.map(function(t,n){return W.createElement(t.tag,He({key:n},t.attr),Pl(t.child))})}function ux(e){return function(t){return W.createElement(dx,He({attr:He({},e.attr)},t),Pl(e.child))}}function dx(e){var t=function(n){var r=e.attr,o=e.size,s=e.title,i=lx(e,["attr","size","title"]),c=o||n.size||"1em",l;return n.className&&(l=n.className),e.className&&(l=(l?l+" ":"")+e.className),W.createElement("svg",He({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,i,{className:l,style:He(He({color:e.color||n.color},n.style),e.style),height:c,width:c,xmlns:"http://www.w3.org/2000/svg"}),s&&W.createElement("title",null,s),e.children)};return Ws!==void 0?W.createElement(Ws.Consumer,null,function(n){return t(n)}):t(Rl)}function fx(e){return ux({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M125.7 160H176c17.7 0 32 14.3 32 32s-14.3 32-32 32H48c-17.7 0-32-14.3-32-32V64c0-17.7 14.3-32 32-32s32 14.3 32 32v51.2L97.6 97.6c87.5-87.5 229.3-87.5 316.8 0s87.5 229.3 0 316.8s-229.3 87.5-316.8 0c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0c62.5 62.5 163.8 62.5 226.3 0s62.5-163.8 0-226.3s-163.8-62.5-226.3 0L125.7 160z"}}]})(e)}const px=({angle:e,setAngle:t})=>m.jsxs("div",{className:"flex w-full items-center space-x-2",children:[m.jsx($l,{value:[e],onValueChange:n=>t(n[0]),max:360,step:1,className:"flex-grow"}),m.jsxs("span",{className:"text-sm font-medium text-primary",children:[e,"°"]}),m.jsx(fx,{className:"flex h-full cursor-pointer items-center justify-center text-primary",onClick:()=>t(90)})]}),mx=({gradientType:e,setGradientType:t})=>m.jsxs(Br,{children:[m.jsx(Ur,{className:"inline-flex",children:m.jsxs("div",{className:"flex w-32 items-center justify-between rounded-md border border-border bg-secondary p-2 text-sm text-primary",children:[m.jsx("span",{children:e.charAt(0).toUpperCase()+e.slice(1)}),m.jsx(Ot,{className:"h-4 w-4"})]})}),m.jsx(En,{align:"end",className:"w-32 rounded-md border border-border bg-secondary p-1",children:["background","text"].map(n=>m.jsx(Sn,{onSelect:()=>t(n),className:"hover:bg-primary/10 cursor-pointer rounded px-2 py-1.5 text-sm text-primary",children:n.charAt(0).toUpperCase()+n.slice(1)},n))})]}),Nt={hexToRGB:e=>{const t=parseInt(e.slice(1,3),16),n=parseInt(e.slice(3,5),16),r=parseInt(e.slice(5,7),16);return{r:t,g:n,b:r}},rgbToHSL:(e,t,n)=>{e/=255,t/=255,n/=255;const r=Math.max(e,t,n),o=Math.min(e,t,n);let s=0,i=0;const c=(r+o)/2;if(r!==o){const l=r-o;switch(i=c>.5?l/(2-r-o):l/(r+o),r){case e:s=((t-n)/l+(t<n?6:0))*60;break;case t:s=((n-e)/l+2)*60;break;case n:s=((e-t)/l+4)*60;break}}return{h:Math.round(s),s:Math.round(i*100),l:Math.round(c*100)}},getUniqueColors:e=>{const t=new Set;return e.forEach(n=>{n.colorsname&&Array.isArray(n.colorsname)&&n.colorsname.forEach(r=>{r&&typeof r=="string"&&t.add(r.toLowerCase().trim())})}),Array.from(t).sort()},getColorCategories:e=>{const t={"🔴 Red":[],"🩷 Pink":[],"🟠 Orange":[],"🟡 Yellow":[],"🟢 Green":[],"🔵 Blue":[],"🟣 Purple":[],"🟤 Brown":[],"⚫ Black":[],"⚪ White":[],"🔘 Gray":[],"🎨 Other":[]};return e.forEach(n=>{const r=n.toLowerCase();r.includes("red")?t["🔴 Red"].push(n):r.includes("pink")?t["🩷 Pink"].push(n):r.includes("orange")||r.includes("peach")?t["🟠 Orange"].push(n):r.includes("yellow")?t["🟡 Yellow"].push(n):r.includes("green")||r.includes("olive")||r.includes("teal")||r.includes("cyan")?t["🟢 Green"].push(n):r.includes("blue")||r.includes("indigo")?t["🔵 Blue"].push(n):r.includes("purple")||r.includes("violet")||r.includes("magenta")?t["🟣 Purple"].push(n):r.includes("brown")||r.includes("beige")?t["🟤 Brown"].push(n):r.includes("black")?t["⚫ Black"].push(n):r.includes("white")?t["⚪ White"].push(n):r.includes("gray")||r.includes("grey")?t["🔘 Gray"].push(n):t["🎨 Other"].push(n)}),Object.keys(t).forEach(n=>{t[n].length===0&&delete t[n]}),t},getBasicColors:e=>{const t=Nt.getUniqueColors(e),n=["red","pink","orange","yellow","green","blue","purple","brown","black","white","gray","grey","teal","cyan"];return t.filter(r=>{const o=r.toLowerCase();return n.some(s=>o.includes(s))})},getBasicColorCategories:e=>{const t=e.filter(n=>{const r=n.toLowerCase();return["red","pink","orange","yellow","green","blue","purple","brown","black","white","gray","grey","teal","cyan"].some(s=>r.includes(s))});return Nt.getColorCategories(t)}},hx=()=>{const[e,t]=a.useState("HEX"),[n,r]=a.useState("background");return{selectedColorFormat:e,setSelectedColorFormat:i=>{t(i)},getColorInFormat:i=>{switch(e){case"RGB":{const{r:c,g:l,b:u}=Nt.hexToRGB(i);return`rgb(${c}, ${l}, ${u})`}case"HSL":{const{r:c,g:l,b:u}=Nt.hexToRGB(i),{h:d,s:f,l:v}=Nt.rgbToHSL(c,l,u);return`hsl(${d}, ${f}%, ${v}%)`}default:return i.toUpperCase()}},gradientType:n,setGradientType:r}},vx=()=>{const[e,t]=a.useState({tailwind:!1,css:!1,sass:!1,bootstrap:!1,colors:!1}),n=(r,o)=>{navigator.clipboard.writeText(r),t(s=>({...s,[o]:!0})),fu({title:"Copied to clipboard ✅",description:`The ${o} code has been copied to your clipboard.`})};return a.useEffect(()=>{const r=setTimeout(()=>{t({tailwind:!1,css:!1,sass:!1,bootstrap:!1,colors:!1})},2e3);return()=>clearTimeout(r)},[e]),{copiedStates:e,copyToClipboard:n}},gx=({code:e,copiedStates:t,activeTab:n,copyToClipboard:r})=>m.jsxs("div",{className:"relative mt-2 w-full",children:[m.jsx("pre",{className:"line-clamp-1 w-full overflow-hidden rounded-md border border-border bg-secondary p-2 text-xs text-muted-foreground",children:e}),m.jsx("button",{className:"absolute right-0 top-0 flex h-full w-[30px] items-center justify-center border border-border bg-secondary p-0 text-primary",onClick:()=>r(e,n),children:m.jsx(Ks,{children:t[n]?m.jsx(me.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.2},children:m.jsx(eo,{className:"text-success h-4 w-4"})}):m.jsx(me.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.2},children:m.jsx(Tu,{className:"h-4 w-4"})})})})]});function bx({gradient:e,isFavorite:t,onFavoriteToggle:n}){const[r,o]=a.useState("tailwind"),[s,i]=a.useState(90),{selectedColorFormat:c,setSelectedColorFormat:l,getColorInFormat:u,gradientType:d,setGradientType:f}=hx(),{copiedStates:v,copyToClipboard:g}=vx(),h=["HEX","RGB","HSL"],p=()=>{const w=e.colors.map(E=>u(E)),C=`linear-gradient(${s}deg, ${w.join(", ")})`;return d==="background"?{backgroundImage:C}:{color:"transparent",backgroundImage:C,WebkitBackgroundClip:"text"}},b=w=>{const C=e.colors.map(M=>u(M)),E=C[0],P=C[C.length-1],S=C.length===3,R=S?C[1]:null,N=`linear-gradient(${s}deg, ${C.join(", ")})`;switch(w){case"tailwind":return d==="background"?S?`bg-gradient-to-r from-[${E}] via-[${R}] to-[${P}]`:`bg-gradient-to-r from-[${E}] to-[${P}]`:`text-transparent bg-clip-text bg-gradient-to-r from-[${E}] ${S?`via-[${R}] `:""}to-[${P}]`;case"css":return d==="background"?`background-image: ${N};`:`color: transparent;
background-image: ${N};
-webkit-background-clip: text;
background-clip: text;`;case"sass":return`// Gradient using ${c} colors
$color-1: ${E};
${S?`$color-2: ${R};`:""}
$color-final: ${P};
$gradient-angle: ${s}deg;
${d==="background"?"background-image":"color"}: linear-gradient(
  $gradient-angle,
  $color-1,
  ${S?"$color-2,":""}
  $color-final
);
${d==="text"?`
color: transparent;
background-image: linear-gradient(
  $gradient-angle,
  $color-1,
  ${S?"$color-2,":""}
  $color-final
);
-webkit-background-clip: text;
background-clip: text;`:""}`;case"bootstrap":return`// Bootstrap gradient using ${c} colors
$gradient-degrees: ${s};
$start-color: ${E};
${S?`$middle-color: ${R};`:""}
$end-color: ${P};

.gradient {
  ${d==="background"?`
  background-image: linear-gradient(
    #{$gradient-degrees}deg,
    $start-color,
    ${S?"$middle-color,":""}
    $end-color
  );`:`
  color: transparent;
  background-image: linear-gradient(
    #{$gradient-degrees}deg,
    $start-color,
    ${S?"$middle-color,":""}
    $end-color
  );
  -webkit-background-clip: text;
  background-clip: text;`}
}`;default:return""}},x=()=>{n(e.name)},{theme:y}=Jh();return m.jsxs(Sb,{gradientColor:y==="dark"?"#262626":"#282828",className:"overflow-hidden transition-all duration-300",children:[m.jsx(Ks,{mode:"wait",children:m.jsx(me.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:m.jsx($b,{style:p(),gradientType:d,gradient:e})},d)}),m.jsxs("footer",{className:"flex flex-col items-start space-y-4 p-4",children:[m.jsx("div",{className:"flex w-full items-center justify-between",children:m.jsx(Rb,{name:e.name,isFavorite:t,onFavoriteToggle:x})}),m.jsxs("div",{className:"flex w-full items-center justify-between gap-2",children:[m.jsx(Pb,{colors:e.colors,getColorInFormat:u,copyToClipboard:(w,C)=>{g(w,C)}}),m.jsxs("div",{className:"flex gap-1",children:[m.jsxs(Br,{children:[m.jsx(Ur,{className:"nofocus inline-flex",children:m.jsxs("div",{className:"flex w-24 items-center justify-between rounded-md border border-border bg-secondary p-2 text-sm text-primary",children:[m.jsx("span",{children:c}),m.jsx(Ot,{className:"h-4 w-4"})]})}),m.jsx(En,{align:"end",className:"w-24 rounded-md border border-border bg-secondary p-1",children:h.map(w=>m.jsx(Sn,{onSelect:()=>{l(w)},className:"hover:bg-primary/10 cursor-pointer rounded px-2 py-1.5 text-sm text-primary",children:w},w))})]}),m.jsx("div",{}),m.jsx(mx,{gradientType:d,setGradientType:w=>{f(w)}})]})]}),m.jsx(px,{angle:s,setAngle:w=>{i(w)}}),m.jsxs(Br,{children:[m.jsx(Ur,{className:"nofocus relative w-full",children:m.jsxs("div",{className:"flex items-center justify-between rounded-md border border-border bg-secondary p-2 text-sm text-primary",children:[m.jsx("span",{children:r}),m.jsx(Ot,{className:"h-4 w-4"})]})}),m.jsx(En,{align:"end",className:"w-48 rounded-md border border-border bg-secondary p-1",children:["tailwind","css","sass","bootstrap"].map(w=>m.jsx(Sn,{onSelect:()=>{o(w)},className:"hover:bg-primary/10 cursor-pointer rounded px-2 py-1.5 text-sm text-primary",children:w.charAt(0).toUpperCase()+w.slice(1)},w))})]}),m.jsx(gx,{code:b(r),copiedStates:v,activeTab:r,copyToClipboard:(w,C)=>{g(w,C)}})]})]})}function Te({className:e,...t}){return m.jsx("div",{className:K("animate-pulse rounded-md bg-muted",e),...t})}const Tl=a.forwardRef(({className:e,...t},n)=>m.jsx("div",{ref:n,className:K("rounded-lg bg-[var(--card-background)]",e),...t}));Tl.displayName="Card";const xx=a.forwardRef(({className:e,...t},n)=>m.jsx("div",{ref:n,className:K("flex flex-row items-center justify-between space-y-1.5 p-6",e),...t}));xx.displayName="CardHeader";const yx=a.forwardRef(({className:e,...t},n)=>m.jsx("h3",{ref:n,className:K("text-base font-semibold leading-none tracking-tight text-[var(--headline)]",e),...t}));yx.displayName="CardTitle";const wx=a.forwardRef(({className:e,...t},n)=>m.jsx("p",{ref:n,className:K("text-sm text-[var(--paragraph)]",e),...t}));wx.displayName="CardDescription";const Nl=a.forwardRef(({className:e,...t},n)=>m.jsx("div",{ref:n,className:K("p-6 pt-0",e),...t}));Nl.displayName="CardContent";const Al=a.forwardRef(({className:e,...t},n)=>m.jsx("div",{ref:n,className:K("flex items-start gap-2 p-6 pt-0",e),...t}));Al.displayName="CardFooter";function Ie(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Uo(e,t=[]){let n=[];function r(s,i){const c=a.createContext(i),l=n.length;n=[...n,i];function u(f){const{scope:v,children:g,...h}=f,p=(v==null?void 0:v[e][l])||c,b=a.useMemo(()=>h,Object.values(h));return m.jsx(p.Provider,{value:b,children:g})}function d(f,v){const g=(v==null?void 0:v[e][l])||c,h=a.useContext(g);if(h)return h;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(i=>a.createContext(i));return function(c){const l=(c==null?void 0:c[e])||s;return a.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,Cx(o,...t)]}function Cx(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((c,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...c,...f}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function Ex(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Ol(...e){return t=>e.forEach(n=>Ex(n,t))}function Rn(...e){return a.useCallback(Ol(...e),e)}var Pn=a.forwardRef((e,t)=>{const{children:n,...r}=e,o=a.Children.toArray(n),s=o.find($x);if(s){const i=s.props.children,c=o.map(l=>l===s?a.Children.count(i)>1?a.Children.only(null):a.isValidElement(i)?i.props.children:null:l);return m.jsx(Kr,{...r,ref:t,children:a.isValidElement(i)?a.cloneElement(i,void 0,c):null})}return m.jsx(Kr,{...r,ref:t,children:n})});Pn.displayName="Slot";var Kr=a.forwardRef((e,t)=>{const{children:n,...r}=e;if(a.isValidElement(n)){const o=Px(n);return a.cloneElement(n,{...Rx(r,n.props),ref:t?Ol(t,o):o})}return a.Children.count(n)>1?a.Children.only(null):null});Kr.displayName="SlotClone";var Sx=({children:e})=>m.jsx(m.Fragment,{children:e});function $x(e){return a.isValidElement(e)&&e.type===Sx}function Rx(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...c)=>{s(...c),o(...c)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Px(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Tx(e){const t=e+"CollectionProvider",[n,r]=Uo(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=g=>{const{scope:h,children:p}=g,b=W.useRef(null),x=W.useRef(new Map).current;return m.jsx(o,{scope:h,itemMap:x,collectionRef:b,children:p})};i.displayName=t;const c=e+"CollectionSlot",l=W.forwardRef((g,h)=>{const{scope:p,children:b}=g,x=s(c,p),y=Rn(h,x.collectionRef);return m.jsx(Pn,{ref:y,children:b})});l.displayName=c;const u=e+"CollectionItemSlot",d="data-radix-collection-item",f=W.forwardRef((g,h)=>{const{scope:p,children:b,...x}=g,y=W.useRef(null),w=Rn(h,y),C=s(u,p);return W.useEffect(()=>(C.itemMap.set(y,{ref:y,...x}),()=>void C.itemMap.delete(y))),m.jsx(Pn,{[d]:"",ref:w,children:b})});f.displayName=u;function v(g){const h=s(e+"CollectionConsumer",g);return W.useCallback(()=>{const b=h.collectionRef.current;if(!b)return[];const x=Array.from(b.querySelectorAll(`[${d}]`));return Array.from(h.itemMap.values()).sort((C,E)=>x.indexOf(C.ref.current)-x.indexOf(E.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:i,Slot:l,ItemSlot:f},v,r]}var Gr=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{},Nx=Tn.useId||(()=>{}),Ax=0;function _l(e){const[t,n]=a.useState(Nx());return Gr(()=>{e||n(r=>r??String(Ax++))},[e]),e||(t?`radix-${t}`:"")}var Ox=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Et=Ox.reduce((e,t)=>{const n=a.forwardRef((r,o)=>{const{asChild:s,...i}=r,c=s?Pn:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),m.jsx(c,{...i,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Wo(e){const t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Ml({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=_x({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,c=Wo(n),l=a.useCallback(u=>{if(s){const f=typeof u=="function"?u(e):u;f!==e&&c(f)}else o(u)},[s,e,o,c]);return[i,l]}function _x({defaultProp:e,onChange:t}){const n=a.useState(e),[r]=n,o=a.useRef(r),s=Wo(t);return a.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var Mx=a.createContext(void 0);function Il(e){const t=a.useContext(Mx);return e||t||"ltr"}var fr="rovingFocusGroup.onEntryFocus",Ix={bubbles:!1,cancelable:!0},Xn="RovingFocusGroup",[qr,Dl,Dx]=Tx(Xn),[jx,jl]=Uo(Xn,[Dx]),[kx,Lx]=jx(Xn),kl=a.forwardRef((e,t)=>m.jsx(qr.Provider,{scope:e.__scopeRovingFocusGroup,children:m.jsx(qr.Slot,{scope:e.__scopeRovingFocusGroup,children:m.jsx(Fx,{...e,ref:t})})}));kl.displayName=Xn;var Fx=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:i,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:d=!1,...f}=e,v=a.useRef(null),g=Rn(t,v),h=Il(s),[p=null,b]=Ml({prop:i,defaultProp:c,onChange:l}),[x,y]=a.useState(!1),w=Wo(u),C=Dl(n),E=a.useRef(!1),[P,S]=a.useState(0);return a.useEffect(()=>{const R=v.current;if(R)return R.addEventListener(fr,w),()=>R.removeEventListener(fr,w)},[w]),m.jsx(kx,{scope:n,orientation:r,dir:h,loop:o,currentTabStopId:p,onItemFocus:a.useCallback(R=>b(R),[b]),onItemShiftTab:a.useCallback(()=>y(!0),[]),onFocusableItemAdd:a.useCallback(()=>S(R=>R+1),[]),onFocusableItemRemove:a.useCallback(()=>S(R=>R-1),[]),children:m.jsx(Et.div,{tabIndex:x||P===0?-1:0,"data-orientation":r,...f,ref:g,style:{outline:"none",...e.style},onMouseDown:Ie(e.onMouseDown,()=>{E.current=!0}),onFocus:Ie(e.onFocus,R=>{const N=!E.current;if(R.target===R.currentTarget&&N&&!x){const M=new CustomEvent(fr,Ix);if(R.currentTarget.dispatchEvent(M),!M.defaultPrevented){const D=C().filter(j=>j.focusable),I=D.find(j=>j.active),F=D.find(j=>j.id===p),A=[I,F,...D].filter(Boolean).map(j=>j.ref.current);Bl(A,d)}}E.current=!1}),onBlur:Ie(e.onBlur,()=>y(!1))})})}),Ll="RovingFocusGroupItem",Fl=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,...i}=e,c=_l(),l=s||c,u=Lx(Ll,n),d=u.currentTabStopId===l,f=Dl(n),{onFocusableItemAdd:v,onFocusableItemRemove:g}=u;return a.useEffect(()=>{if(r)return v(),()=>g()},[r,v,g]),m.jsx(qr.ItemSlot,{scope:n,id:l,focusable:r,active:o,children:m.jsx(Et.span,{tabIndex:d?0:-1,"data-orientation":u.orientation,...i,ref:t,onMouseDown:Ie(e.onMouseDown,h=>{r?u.onItemFocus(l):h.preventDefault()}),onFocus:Ie(e.onFocus,()=>u.onItemFocus(l)),onKeyDown:Ie(e.onKeyDown,h=>{if(h.key==="Tab"&&h.shiftKey){u.onItemShiftTab();return}if(h.target!==h.currentTarget)return;const p=Wx(h,u.orientation,u.dir);if(p!==void 0){if(h.metaKey||h.ctrlKey||h.altKey||h.shiftKey)return;h.preventDefault();let x=f().filter(y=>y.focusable).map(y=>y.ref.current);if(p==="last")x.reverse();else if(p==="prev"||p==="next"){p==="prev"&&x.reverse();const y=x.indexOf(h.currentTarget);x=u.loop?Hx(x,y+1):x.slice(y+1)}setTimeout(()=>Bl(x))}})})})});Fl.displayName=Ll;var Bx={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Ux(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Wx(e,t,n){const r=Ux(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Bx[r]}function Bl(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function Hx(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var Vx=kl,zx=Fl;function Kx(e,t){return a.useReducer((n,r)=>t[n][r]??n,e)}var Ul=e=>{const{present:t,children:n}=e,r=Gx(t),o=typeof n=="function"?n({present:r.isPresent}):a.Children.only(n),s=Rn(r.ref,qx(o));return typeof n=="function"||r.isPresent?a.cloneElement(o,{ref:s}):null};Ul.displayName="Presence";function Gx(e){const[t,n]=a.useState(),r=a.useRef({}),o=a.useRef(e),s=a.useRef("none"),i=e?"mounted":"unmounted",[c,l]=Kx(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return a.useEffect(()=>{const u=on(r.current);s.current=c==="mounted"?u:"none"},[c]),Gr(()=>{const u=r.current,d=o.current;if(d!==e){const v=s.current,g=on(u);e?l("MOUNT"):g==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&v!==g?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Gr(()=>{if(t){const u=f=>{const g=on(r.current).includes(f.animationName);f.target===t&&g&&Fe.flushSync(()=>l("ANIMATION_END"))},d=f=>{f.target===t&&(s.current=on(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:a.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function on(e){return(e==null?void 0:e.animationName)||"none"}function qx(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Ho="Tabs",[Yx,xy]=Uo(Ho,[jl]),Wl=jl(),[Xx,Vo]=Yx(Ho),Hl=a.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:s,orientation:i="horizontal",dir:c,activationMode:l="automatic",...u}=e,d=Il(c),[f,v]=Ml({prop:r,onChange:o,defaultProp:s});return m.jsx(Xx,{scope:n,baseId:_l(),value:f,onValueChange:v,orientation:i,dir:d,activationMode:l,children:m.jsx(Et.div,{dir:d,"data-orientation":i,...u,ref:t})})});Hl.displayName=Ho;var Vl="TabsList",zl=a.forwardRef((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,s=Vo(Vl,n),i=Wl(n);return m.jsx(Vx,{asChild:!0,...i,orientation:s.orientation,dir:s.dir,loop:r,children:m.jsx(Et.div,{role:"tablist","aria-orientation":s.orientation,...o,ref:t})})});zl.displayName=Vl;var Kl="TabsTrigger",Gl=a.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...s}=e,i=Vo(Kl,n),c=Wl(n),l=Xl(i.baseId,r),u=Jl(i.baseId,r),d=r===i.value;return m.jsx(zx,{asChild:!0,...c,focusable:!o,active:d,children:m.jsx(Et.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":u,"data-state":d?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:l,...s,ref:t,onMouseDown:Ie(e.onMouseDown,f=>{!o&&f.button===0&&f.ctrlKey===!1?i.onValueChange(r):f.preventDefault()}),onKeyDown:Ie(e.onKeyDown,f=>{[" ","Enter"].includes(f.key)&&i.onValueChange(r)}),onFocus:Ie(e.onFocus,()=>{const f=i.activationMode!=="manual";!d&&!o&&f&&i.onValueChange(r)})})})});Gl.displayName=Kl;var ql="TabsContent",Yl=a.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,forceMount:o,children:s,...i}=e,c=Vo(ql,n),l=Xl(c.baseId,r),u=Jl(c.baseId,r),d=r===c.value,f=a.useRef(d);return a.useEffect(()=>{const v=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(v)},[]),m.jsx(Ul,{present:o||d,children:({present:v})=>m.jsx(Et.div,{"data-state":d?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":l,hidden:!v,id:u,tabIndex:0,...i,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:v&&s})})});Yl.displayName=ql;function Xl(e,t){return`${e}-trigger-${t}`}function Jl(e,t){return`${e}-content-${t}`}var Jx=Hl,Zl=zl,Ql=Gl,eu=Yl;const Zx=Jx,tu=a.forwardRef(({className:e,...t},n)=>m.jsx(Zl,{ref:n,className:K("inline-flex h-10 items-center justify-center rounded-md p-1 text-[var(--paragraph)]",e),...t}));tu.displayName=Zl.displayName;const Yr=a.forwardRef(({className:e,...t},n)=>m.jsx(Ql,{ref:n,className:K("px- inline-flex items-center justify-center whitespace-nowrap rounded-sm p-2 py-1.5 text-sm font-medium text-[var(--paragraph)] ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-[var(--card-background)] data-[state=active]:text-[var(--headline)] data-[state=active]:shadow-sm",e),...t}));Yr.displayName=Ql.displayName;const Xr=a.forwardRef(({className:e,...t},n)=>m.jsx(eu,{ref:n,className:K("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));Xr.displayName=eu.displayName;function Qx(){return m.jsxs(Tl,{className:"overflow-hidden shadow-md",children:[m.jsx(Nl,{className:"p-0",children:m.jsx(Te,{className:"h-48 w-full"})}),m.jsxs(Al,{className:"flex flex-col items-start space-y-4 p-4",children:[m.jsxs("div",{className:"flex w-full items-center justify-between",children:[m.jsx(Te,{className:"h-6 w-32"}),m.jsx(Te,{className:"h-8 w-8 rounded-full"})]}),m.jsx("div",{className:"flex w-full flex-wrap gap-2",children:[...Array(5)].map((e,t)=>m.jsx(Te,{className:"h-6 w-6 rounded-full"},t))}),m.jsx(Te,{className:"h-8 w-full"}),m.jsxs(Zx,{defaultValue:"tailwind",className:"w-full",children:[m.jsxs(tu,{className:"grid w-full grid-cols-2",children:[m.jsx(Yr,{value:"tailwind",children:m.jsx(Te,{className:"h-4 w-16"})}),m.jsx(Yr,{value:"css",children:m.jsx(Te,{className:"h-4 w-8"})})]}),m.jsx(Xr,{value:"tailwind",className:"mt-2",children:m.jsx(Te,{className:"h-8 w-full"})}),m.jsx(Xr,{value:"css",className:"mt-2",children:m.jsx(Te,{className:"h-8 w-full"})})]})]})]})}function ey({gradients:e,favorites:t,toggleFavorite:n,isLoading:r}){return m.jsx("div",{className:"grid grid-cols-1 gap-6 pt-6 sm:grid-cols-2 lg:grid-cols-3",children:r?Array.from({length:6}).map((o,s)=>m.jsx(me.div,{layout:!0,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.3},children:m.jsx(Qx,{})},`skeleton-${s}`)):e.length===0?m.jsxs(me.div,{layout:!0,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.3},className:"col-span-full flex items-center justify-center gap-1 pt-16 text-center text-muted-foreground",children:[m.jsx(Au,{}),m.jsx("span",{children:"No gradients found"})]},"no-gradients"):e.map(o=>m.jsx("div",{children:m.jsx(bx,{gradient:o,isFavorite:t.includes(o.name),onFavoriteToggle:()=>n(o.name)})},o.name))})}function ty({currentPage:e,totalPages:t,onPageChange:n}){return m.jsxs(me.div,{className:"flex items-center justify-center gap-4 pt-16 max-md:py-4",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.3},children:[m.jsx(zo,{className:"h-max w-max rounded-md border-border bg-card p-2 text-primary",onClick:()=>n("prev"),disabled:e===1,children:m.jsx($u,{className:"h-4 w-4 text-primary"})}),m.jsxs(me.div,{className:"text-sm font-semibold text-muted-foreground",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.1},children:[e," / ",t]}),m.jsx(zo,{className:"h-max w-max rounded-md border-border bg-card p-2 text-primary",onClick:()=>n("next"),disabled:e===t,children:m.jsx(Gs,{className:"h-4 w-4"})})]})}const ny="https://alshaer.vercel.app/";function ry(){return m.jsxs("footer",{className:"footer container flex h-[64px] items-center justify-between max-md:flex-col max-md:gap-3 max-md:py-10 max-md:text-center",children:[m.jsxs("p",{className:"flex items-center justify-center text-sm text-primary",children:["Devlop by"," ",m.jsxs("a",{target:"_blank",className:"hoverd flex items-center justify-center ps-1 font-medium text-[var(--link)] hover:text-[var(--link-hover)]",href:ny,children:["Baraa",m.jsx(Nu,{className:"ms-1 h-3 w-3"})]})]}),m.jsx("div",{children:m.jsxs("p",{className:"text-xs text-muted-foreground max-md:pb-6",children:["© ",new Date().getFullYear()," GradientsCSS. All rights reserved."]})})]})}const oy=({children:e,className:t,shimmerWidth:n=100})=>m.jsx("p",{style:{"--shiny-width":`${n}px`},className:K("mx-auto max-w-md text-neutral-600/70 dark:text-neutral-400/70","animate-shiny-text bg-clip-text bg-no-repeat [background-position:0_0] [background-size:var(--shiny-width)_100%] [transition:background-position_1s_cubic-bezier(.6,.6,0,1)_infinite]","bg-gradient-to-r from-transparent via-black/80 via-50% to-transparent dark:via-white/80",t),children:e});function sy({value:e,direction:t="up",delay:n=0,className:r,decimalPlaces:o=0}){const s=a.useRef(null),i=At(t==="down"?e:0),c=xu(i,{damping:60,stiffness:100}),l=Eu(s,{once:!0,margin:"0px"});return a.useEffect(()=>{l&&setTimeout(()=>{i.set(t==="down"?0:e)},n*1e3)},[i,l,n,e,t]),a.useEffect(()=>c.on("change",u=>{s.current&&(s.current.textContent=Intl.NumberFormat("en-US",{minimumFractionDigits:o,maximumFractionDigits:o}).format(Number(u.toFixed(o))))}),[c,o]),m.jsx("span",{className:K("inline-block ps-1 tabular-nums tracking-wider text-[var(--headline)] dark:text-white",r),ref:s})}const iy=()=>{const e=["https://bundui-images.netlify.app/avatars/01.png","https://bundui-images.netlify.app/avatars/03.png","https://bundui-images.netlify.app/avatars/05.png","https://bundui-images.netlify.app/avatars/06.png"],t=()=>m.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"orange",stroke:"orange",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round",className:"lucide lucide-star h-5 w-5",children:m.jsx("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})}),n=({src:r})=>{const[o,s]=a.useState(!0);return m.jsxs("span",{className:"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",children:[o&&m.jsx(Te,{className:"h-10 w-10"}),m.jsx("img",{alt:"shadcn ui kit avatar",src:r,onLoad:()=>s(!1),className:o?"hidden":"block"})]})};return m.jsx("div",{children:m.jsxs("div",{className:"mt-3 flex flex-col gap-4 lg:flex-row",children:[m.jsx("div",{className:"flex justify-center -space-x-4",children:e.map((r,o)=>m.jsx(n,{src:r},o))}),m.jsxs("div",{className:"flex flex-col",children:[m.jsx("span",{className:"mt-1 flex justify-center gap-1 lg:justify-start",children:Array.from({length:5}).map((r,o)=>m.jsx(t,{},o))}),m.jsxs("span",{className:"text-sm text-muted-foreground",children:["More than",m.jsx(sy,{value:50}),"+ happy users"]})]})]})})};function ay(){const[e,t]=a.useState([]),[n,r]=a.useState(!0),[o,s]=a.useState(null);return a.useEffect(()=>{let i=!1;return(async()=>{r(!0),s(null);try{const l=await ee.get("https://gist.githubusercontent.com/balshaer/69d1f26f366d2dcf2d58d6d644f0aff4/raw/6350bd8c935e9d9f937ec95cd250f819bfc57afc/data.json");i||t(l.data)}catch(l){i||s(l instanceof Error?l.message:"Failed to fetch gradients")}finally{i||r(!1)}})(),()=>{i=!0}},[]),{gradients:e,isLoading:n,error:o}}function cy(){const[e,t]=a.useState([]);return a.useEffect(()=>{const r=localStorage.getItem("gradientFavorites");r&&t(JSON.parse(r))},[]),a.useEffect(()=>{localStorage.setItem("gradientFavorites",JSON.stringify(e))},[e]),{favorites:e,toggleFavorite:r=>{t(o=>o.includes(r)?o.filter(s=>s!==r):[...o,r])}}}function ly(e,t){const[n,r]=a.useState(e);return a.useEffect(()=>{const o=setTimeout(()=>r(e),t);return()=>clearTimeout(o)},[e,t]),n}const yy=()=>{const[e,t]=a.useState(""),n=ly(e,250),[r,o]=a.useState(1),[s,i]=a.useState("all"),[c,l]=a.useState(""),u=[],d=9,{gradients:f,isLoading:v,error:g}=ay(),{favorites:h,toggleFavorite:p}=cy(),b=a.useMemo(()=>f.length?f.filter(C=>{const E=!n||C.colorsname.some(R=>R.toLowerCase().includes(n.toLowerCase()))||C.keywords.some(R=>R.some(N=>N.toLowerCase().includes(n.toLowerCase()))),P=s==="all"||s==="favorites"&&h.includes(C.name),S=u.length===0||u.some(R=>C.colorsname.some(N=>N.toLowerCase().includes(R.toLowerCase())));return E&&P&&S}):[],[n,f,h,s,u]),x=Math.ceil(b.length/d),y=a.useMemo(()=>{const C=(r-1)*d;return b.slice(C,C+d)},[r,b,d]),w=C=>{o(E=>C==="next"?E+1:E-1)};return m.jsx(qh,{children:m.jsxs("div",{className:"container relative",style:{background:c},children:[m.jsxs("div",{className:"mx-auto max-w-6xl space-y-2 pt-12",children:[m.jsxs("header",{className:"relative mx-auto max-w-6xl space-y-2 pt-12",children:[m.jsx("a",{href:"https://github.com/balshaer/gradients-css",target:"_blank",rel:"noopener noreferrer",children:m.jsx("div",{className:"flex w-full items-center justify-center",children:m.jsx("div",{className:K("group rounded-full border border-border bg-card text-base text-[var(--muted)] transition-all ease-in hover:cursor-pointer dark:border-white/5 dark:bg-neutral-900 dark:hover:bg-neutral-800"),children:m.jsxs(oy,{className:"inline-flex items-center justify-center px-4 py-1 text-primary transition ease-out hover:duration-300 max-md:text-xs",children:[m.jsx("span",{children:"✨ Contribute to The Project"}),m.jsx(Su,{className:"ml-1 size-3 transition-transform duration-300 ease-in-out group-hover:translate-x-0.5"})]})})})}),m.jsxs("h1",{className:"pt-6 text-center text-3xl font-medium text-primary dark:text-gray-50 sm:text-6xl",children:["Collection of modern,",m.jsxs("span",{className:"relative ps-1",children:["Gradients",m.jsx("img",{className:"absolute bottom-[-10px] left-0 right-0",src:"https://uploads-ssl.webflow.com/618ce467f09b34ebf2fdf6be/62779adeac94b82ea2fe08ec_Underline%202.svg",alt:""})]})]}),m.jsx("p",{className:"m-auto mt-[-120px] max-w-2xl py-0 pb-0 pt-3 text-center text-lg leading-6 text-muted-foreground dark:text-gray-200",children:"Ready-to-use, simply copy and paste into your next project. All gradients crafted with CSS and Tailwind CSS for easy integration."}),m.jsx("div",{className:"flex w-full items-center justify-center pb-6",children:m.jsx(iy,{})})]}),m.jsxs("div",{className:"flex flex-col gap-4",children:[m.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row",children:[m.jsxs("div",{className:"relative w-full",id:"input",children:[m.jsx(bi,{placeholder:"Search by color name or keyword...",className:"hover:border-brand-500-secondary invalid:border-error-500 invalid:focus:border-error-500 text-placeholder peer block h-full w-full appearance-none overflow-hidden overflow-ellipsis text-nowrap rounded-md border border-border bg-input px-3 py-2 pr-[48px] text-sm outline-none focus:border-none focus:shadow-none focus:outline-none",id:"floating_outlined",type:"text",value:e,onChange:C=>{t(C.target.value),o(1)}}),m.jsx(_u,{className:"absolute bottom-0 right-2 top-0 m-auto h-5 w-5 text-primary"})]}),m.jsxs(Vm,{value:s,onValueChange:C=>{i(C),o(1)},children:[m.jsx(Wa,{className:"nofocus nohover w-full border-none outline-none sm:w-[180px]",children:m.jsx(zm,{placeholder:"Filter"})}),m.jsxs(za,{className:"nofocus nohover border-none outline-none",children:[m.jsx(Nr,{value:"all",children:"All Gradients"}),m.jsx(Nr,{value:"favorites",children:"Favorites"})]})]})]}),(u.length>0||e||s!=="all")&&m.jsxs("div",{className:"text-sm text-muted-foreground",children:["Showing ",b.length," gradient",b.length!==1?"s":"",u.length>0&&m.jsxs("span",{children:[" with colors: ",u.join(", ")]})]})]}),g&&m.jsxs("div",{className:"flex flex-col items-center justify-center py-12 text-center",children:[m.jsxs("p",{className:"text-red-500 mb-4",children:["Error loading gradients: ",g]}),m.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90",children:"Retry"})]}),!g&&m.jsx(ey,{gradients:y,favorites:h,toggleFavorite:p,setBackground:l,isLoading:v}),m.jsx(ty,{currentPage:r,totalPages:x,onPageChange:w})]}),m.jsx(ry,{})]})})};export{yy as default};
