function $v(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var Uv=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Fl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Zf={exports:{}},ts={},qf={exports:{}},$={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Eo=Symbol.for("react.element"),Wv=Symbol.for("react.portal"),Hv=Symbol.for("react.fragment"),Kv=Symbol.for("react.strict_mode"),Gv=Symbol.for("react.profiler"),Qv=Symbol.for("react.provider"),Yv=Symbol.for("react.context"),Xv=Symbol.for("react.forward_ref"),Zv=Symbol.for("react.suspense"),qv=Symbol.for("react.memo"),Jv=Symbol.for("react.lazy"),fc=Symbol.iterator;function ey(e){return e===null||typeof e!="object"?null:(e=fc&&e[fc]||e["@@iterator"],typeof e=="function"?e:null)}var Jf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ep=Object.assign,tp={};function xr(e,t,n){this.props=e,this.context=t,this.refs=tp,this.updater=n||Jf}xr.prototype.isReactComponent={};xr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};xr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function np(){}np.prototype=xr.prototype;function zl(e,t,n){this.props=e,this.context=t,this.refs=tp,this.updater=n||Jf}var Bl=zl.prototype=new np;Bl.constructor=zl;ep(Bl,xr.prototype);Bl.isPureReactComponent=!0;var pc=Array.isArray,rp=Object.prototype.hasOwnProperty,$l={current:null},op={key:!0,ref:!0,__self:!0,__source:!0};function ip(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)rp.call(t,r)&&!op.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:Eo,type:e,key:i,ref:s,props:o,_owner:$l.current}}function ty(e,t){return{$$typeof:Eo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ul(e){return typeof e=="object"&&e!==null&&e.$$typeof===Eo}function ny(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var hc=/\/+/g;function Ls(e,t){return typeof e=="object"&&e!==null&&e.key!=null?ny(""+e.key):t.toString(36)}function ri(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Eo:case Wv:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Ls(s,0):r,pc(o)?(n="",e!=null&&(n=e.replace(hc,"$&/")+"/"),ri(o,t,n,"",function(u){return u})):o!=null&&(Ul(o)&&(o=ty(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(hc,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",pc(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Ls(i,a);s+=ri(i,t,n,l,o)}else if(l=ey(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Ls(i,a++),s+=ri(i,t,n,l,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Do(e,t,n){if(e==null)return e;var r=[],o=0;return ri(e,r,"","",function(i){return t.call(n,i,o++)}),r}function ry(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Le={current:null},oi={transition:null},oy={ReactCurrentDispatcher:Le,ReactCurrentBatchConfig:oi,ReactCurrentOwner:$l};$.Children={map:Do,forEach:function(e,t,n){Do(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Do(e,function(){t++}),t},toArray:function(e){return Do(e,function(t){return t})||[]},only:function(e){if(!Ul(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};$.Component=xr;$.Fragment=Hv;$.Profiler=Gv;$.PureComponent=zl;$.StrictMode=Kv;$.Suspense=Zv;$.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=oy;$.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ep({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=$l.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)rp.call(t,l)&&!op.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Eo,type:e.type,key:o,ref:i,props:r,_owner:s}};$.createContext=function(e){return e={$$typeof:Yv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Qv,_context:e},e.Consumer=e};$.createElement=ip;$.createFactory=function(e){var t=ip.bind(null,e);return t.type=e,t};$.createRef=function(){return{current:null}};$.forwardRef=function(e){return{$$typeof:Xv,render:e}};$.isValidElement=Ul;$.lazy=function(e){return{$$typeof:Jv,_payload:{_status:-1,_result:e},_init:ry}};$.memo=function(e,t){return{$$typeof:qv,type:e,compare:t===void 0?null:t}};$.startTransition=function(e){var t=oi.transition;oi.transition={};try{e()}finally{oi.transition=t}};$.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};$.useCallback=function(e,t){return Le.current.useCallback(e,t)};$.useContext=function(e){return Le.current.useContext(e)};$.useDebugValue=function(){};$.useDeferredValue=function(e){return Le.current.useDeferredValue(e)};$.useEffect=function(e,t){return Le.current.useEffect(e,t)};$.useId=function(){return Le.current.useId()};$.useImperativeHandle=function(e,t,n){return Le.current.useImperativeHandle(e,t,n)};$.useInsertionEffect=function(e,t){return Le.current.useInsertionEffect(e,t)};$.useLayoutEffect=function(e,t){return Le.current.useLayoutEffect(e,t)};$.useMemo=function(e,t){return Le.current.useMemo(e,t)};$.useReducer=function(e,t,n){return Le.current.useReducer(e,t,n)};$.useRef=function(e){return Le.current.useRef(e)};$.useState=function(e){return Le.current.useState(e)};$.useSyncExternalStore=function(e,t,n){return Le.current.useSyncExternalStore(e,t,n)};$.useTransition=function(){return Le.current.useTransition()};$.version="18.2.0";qf.exports=$;var w=qf.exports;const ht=Fl(w),iy=$v({__proto__:null,default:ht},[w]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sy=w,ay=Symbol.for("react.element"),ly=Symbol.for("react.fragment"),uy=Object.prototype.hasOwnProperty,cy=sy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,dy={key:!0,ref:!0,__self:!0,__source:!0};function sp(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)uy.call(t,r)&&!dy.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:ay,type:e,key:i,ref:s,props:o,_owner:cy.current}}ts.Fragment=ly;ts.jsx=sp;ts.jsxs=sp;Zf.exports=ts;var b=Zf.exports,ap={exports:{}},Ye={},lp={exports:{}},up={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(M,O){var I=M.length;M.push(O);e:for(;0<I;){var U=I-1>>>1,W=M[U];if(0<o(W,O))M[U]=O,M[I]=W,I=U;else break e}}function n(M){return M.length===0?null:M[0]}function r(M){if(M.length===0)return null;var O=M[0],I=M.pop();if(I!==O){M[0]=I;e:for(var U=0,W=M.length,de=W>>>1;U<de;){var te=2*(U+1)-1,De=M[te],ge=te+1,ae=M[ge];if(0>o(De,I))ge<W&&0>o(ae,De)?(M[U]=ae,M[ge]=I,U=ge):(M[U]=De,M[te]=I,U=te);else if(ge<W&&0>o(ae,I))M[U]=ae,M[ge]=I,U=ge;else break e}}return O}function o(M,O){var I=M.sortIndex-O.sortIndex;return I!==0?I:M.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],d=1,c=null,f=3,m=!1,v=!1,y=!1,x=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(M){for(var O=n(u);O!==null;){if(O.callback===null)r(u);else if(O.startTime<=M)r(u),O.sortIndex=O.expirationTime,t(l,O);else break;O=n(u)}}function S(M){if(y=!1,g(M),!v)if(n(l)!==null)v=!0,B(P);else{var O=n(u);O!==null&&X(S,O.startTime-M)}}function P(M,O){v=!1,y&&(y=!1,h(T),T=-1),m=!0;var I=f;try{for(g(O),c=n(l);c!==null&&(!(c.expirationTime>O)||M&&!_());){var U=c.callback;if(typeof U=="function"){c.callback=null,f=c.priorityLevel;var W=U(c.expirationTime<=O);O=e.unstable_now(),typeof W=="function"?c.callback=W:c===n(l)&&r(l),g(O)}else r(l);c=n(l)}if(c!==null)var de=!0;else{var te=n(u);te!==null&&X(S,te.startTime-O),de=!1}return de}finally{c=null,f=I,m=!1}}var C=!1,k=null,T=-1,D=5,L=-1;function _(){return!(e.unstable_now()-L<D)}function E(){if(k!==null){var M=e.unstable_now();L=M;var O=!0;try{O=k(!0,M)}finally{O?F():(C=!1,k=null)}}else C=!1}var F;if(typeof p=="function")F=function(){p(E)};else if(typeof MessageChannel<"u"){var A=new MessageChannel,V=A.port2;A.port1.onmessage=E,F=function(){V.postMessage(null)}}else F=function(){x(E,0)};function B(M){k=M,C||(C=!0,F())}function X(M,O){T=x(function(){M(e.unstable_now())},O)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(M){M.callback=null},e.unstable_continueExecution=function(){v||m||(v=!0,B(P))},e.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<M?Math.floor(1e3/M):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(M){switch(f){case 1:case 2:case 3:var O=3;break;default:O=f}var I=f;f=O;try{return M()}finally{f=I}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(M,O){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var I=f;f=M;try{return O()}finally{f=I}},e.unstable_scheduleCallback=function(M,O,I){var U=e.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?U+I:U):I=U,M){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=I+W,M={id:d++,callback:O,priorityLevel:M,startTime:I,expirationTime:W,sortIndex:-1},I>U?(M.sortIndex=I,t(u,M),n(l)===null&&M===n(u)&&(y?(h(T),T=-1):y=!0,X(S,I-U))):(M.sortIndex=W,t(l,M),v||m||(v=!0,B(P))),M},e.unstable_shouldYield=_,e.unstable_wrapCallback=function(M){var O=f;return function(){var I=f;f=O;try{return M.apply(this,arguments)}finally{f=I}}}})(up);lp.exports=up;var fy=lp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cp=w,Ge=fy;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var dp=new Set,no={};function In(e,t){dr(e,t),dr(e+"Capture",t)}function dr(e,t){for(no[e]=t,e=0;e<t.length;e++)dp.add(t[e])}var Lt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Sa=Object.prototype.hasOwnProperty,py=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,mc={},gc={};function hy(e){return Sa.call(gc,e)?!0:Sa.call(mc,e)?!1:py.test(e)?gc[e]=!0:(mc[e]=!0,!1)}function my(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function gy(e,t,n,r){if(t===null||typeof t>"u"||my(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ne(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Pe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Pe[e]=new Ne(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Pe[t]=new Ne(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Pe[e]=new Ne(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Pe[e]=new Ne(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Pe[e]=new Ne(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Pe[e]=new Ne(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Pe[e]=new Ne(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Pe[e]=new Ne(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Pe[e]=new Ne(e,5,!1,e.toLowerCase(),null,!1,!1)});var Wl=/[\-:]([a-z])/g;function Hl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Wl,Hl);Pe[t]=new Ne(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Wl,Hl);Pe[t]=new Ne(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Wl,Hl);Pe[t]=new Ne(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Pe[e]=new Ne(e,1,!1,e.toLowerCase(),null,!1,!1)});Pe.xlinkHref=new Ne("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Pe[e]=new Ne(e,1,!1,e.toLowerCase(),null,!0,!0)});function Kl(e,t,n,r){var o=Pe.hasOwnProperty(t)?Pe[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(gy(t,n,o,r)&&(n=null),r||o===null?hy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var It=cp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Oo=Symbol.for("react.element"),Bn=Symbol.for("react.portal"),$n=Symbol.for("react.fragment"),Gl=Symbol.for("react.strict_mode"),Pa=Symbol.for("react.profiler"),fp=Symbol.for("react.provider"),pp=Symbol.for("react.context"),Ql=Symbol.for("react.forward_ref"),Ta=Symbol.for("react.suspense"),Ca=Symbol.for("react.suspense_list"),Yl=Symbol.for("react.memo"),Gt=Symbol.for("react.lazy"),hp=Symbol.for("react.offscreen"),vc=Symbol.iterator;function Er(e){return e===null||typeof e!="object"?null:(e=vc&&e[vc]||e["@@iterator"],typeof e=="function"?e:null)}var se=Object.assign,Ns;function Vr(e){if(Ns===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ns=t&&t[1]||""}return`
`+Ns+e}var Ds=!1;function Os(e,t){if(!e||Ds)return"";Ds=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{Ds=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Vr(e):""}function vy(e){switch(e.tag){case 5:return Vr(e.type);case 16:return Vr("Lazy");case 13:return Vr("Suspense");case 19:return Vr("SuspenseList");case 0:case 2:case 15:return e=Os(e.type,!1),e;case 11:return e=Os(e.type.render,!1),e;case 1:return e=Os(e.type,!0),e;default:return""}}function Ea(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case $n:return"Fragment";case Bn:return"Portal";case Pa:return"Profiler";case Gl:return"StrictMode";case Ta:return"Suspense";case Ca:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case pp:return(e.displayName||"Context")+".Consumer";case fp:return(e._context.displayName||"Context")+".Provider";case Ql:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Yl:return t=e.displayName||null,t!==null?t:Ea(e.type)||"Memo";case Gt:t=e._payload,e=e._init;try{return Ea(e(t))}catch{}}return null}function yy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ea(t);case 8:return t===Gl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function un(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function mp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function wy(e){var t=mp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Vo(e){e._valueTracker||(e._valueTracker=wy(e))}function gp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=mp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function xi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ka(e,t){var n=t.checked;return se({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function yc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=un(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function vp(e,t){t=t.checked,t!=null&&Kl(e,"checked",t,!1)}function Ra(e,t){vp(e,t);var n=un(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ba(e,t.type,n):t.hasOwnProperty("defaultValue")&&ba(e,t.type,un(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function wc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ba(e,t,n){(t!=="number"||xi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var jr=Array.isArray;function or(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+un(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Aa(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return se({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function xc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(jr(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:un(n)}}function yp(e,t){var n=un(t.value),r=un(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Sc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function wp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ma(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?wp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var jo,xp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(jo=jo||document.createElement("div"),jo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=jo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ro(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var $r={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},xy=["Webkit","ms","Moz","O"];Object.keys($r).forEach(function(e){xy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),$r[t]=$r[e]})});function Sp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||$r.hasOwnProperty(e)&&$r[e]?(""+t).trim():t+"px"}function Pp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Sp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Sy=se({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function _a(e,t){if(t){if(Sy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function La(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Na=null;function Xl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Da=null,ir=null,sr=null;function Pc(e){if(e=bo(e)){if(typeof Da!="function")throw Error(R(280));var t=e.stateNode;t&&(t=ss(t),Da(e.stateNode,e.type,t))}}function Tp(e){ir?sr?sr.push(e):sr=[e]:ir=e}function Cp(){if(ir){var e=ir,t=sr;if(sr=ir=null,Pc(e),t)for(e=0;e<t.length;e++)Pc(t[e])}}function Ep(e,t){return e(t)}function kp(){}var Vs=!1;function Rp(e,t,n){if(Vs)return e(t,n);Vs=!0;try{return Ep(e,t,n)}finally{Vs=!1,(ir!==null||sr!==null)&&(kp(),Cp())}}function oo(e,t){var n=e.stateNode;if(n===null)return null;var r=ss(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var Oa=!1;if(Lt)try{var kr={};Object.defineProperty(kr,"passive",{get:function(){Oa=!0}}),window.addEventListener("test",kr,kr),window.removeEventListener("test",kr,kr)}catch{Oa=!1}function Py(e,t,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var Ur=!1,Si=null,Pi=!1,Va=null,Ty={onError:function(e){Ur=!0,Si=e}};function Cy(e,t,n,r,o,i,s,a,l){Ur=!1,Si=null,Py.apply(Ty,arguments)}function Ey(e,t,n,r,o,i,s,a,l){if(Cy.apply(this,arguments),Ur){if(Ur){var u=Si;Ur=!1,Si=null}else throw Error(R(198));Pi||(Pi=!0,Va=u)}}function Fn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function bp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Tc(e){if(Fn(e)!==e)throw Error(R(188))}function ky(e){var t=e.alternate;if(!t){if(t=Fn(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Tc(o),e;if(i===r)return Tc(o),t;i=i.sibling}throw Error(R(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function Ap(e){return e=ky(e),e!==null?Mp(e):null}function Mp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Mp(e);if(t!==null)return t;e=e.sibling}return null}var _p=Ge.unstable_scheduleCallback,Cc=Ge.unstable_cancelCallback,Ry=Ge.unstable_shouldYield,by=Ge.unstable_requestPaint,ce=Ge.unstable_now,Ay=Ge.unstable_getCurrentPriorityLevel,Zl=Ge.unstable_ImmediatePriority,Lp=Ge.unstable_UserBlockingPriority,Ti=Ge.unstable_NormalPriority,My=Ge.unstable_LowPriority,Np=Ge.unstable_IdlePriority,ns=null,gt=null;function _y(e){if(gt&&typeof gt.onCommitFiberRoot=="function")try{gt.onCommitFiberRoot(ns,e,void 0,(e.current.flags&128)===128)}catch{}}var ut=Math.clz32?Math.clz32:Dy,Ly=Math.log,Ny=Math.LN2;function Dy(e){return e>>>=0,e===0?32:31-(Ly(e)/Ny|0)|0}var Io=64,Fo=4194304;function Ir(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ci(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=Ir(a):(i&=s,i!==0&&(r=Ir(i)))}else s=n&~o,s!==0?r=Ir(s):i!==0&&(r=Ir(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ut(t),o=1<<n,r|=e[n],t&=~o;return r}function Oy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Vy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-ut(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=Oy(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function ja(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Dp(){var e=Io;return Io<<=1,!(Io&4194240)&&(Io=64),e}function js(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ko(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ut(t),e[t]=n}function jy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-ut(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function ql(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ut(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var G=0;function Op(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Vp,Jl,jp,Ip,Fp,Ia=!1,zo=[],en=null,tn=null,nn=null,io=new Map,so=new Map,Yt=[],Iy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ec(e,t){switch(e){case"focusin":case"focusout":en=null;break;case"dragenter":case"dragleave":tn=null;break;case"mouseover":case"mouseout":nn=null;break;case"pointerover":case"pointerout":io.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":so.delete(t.pointerId)}}function Rr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=bo(t),t!==null&&Jl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Fy(e,t,n,r,o){switch(t){case"focusin":return en=Rr(en,e,t,n,r,o),!0;case"dragenter":return tn=Rr(tn,e,t,n,r,o),!0;case"mouseover":return nn=Rr(nn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return io.set(i,Rr(io.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,so.set(i,Rr(so.get(i)||null,e,t,n,r,o)),!0}return!1}function zp(e){var t=Cn(e.target);if(t!==null){var n=Fn(t);if(n!==null){if(t=n.tag,t===13){if(t=bp(n),t!==null){e.blockedOn=t,Fp(e.priority,function(){jp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ii(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Fa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Na=r,n.target.dispatchEvent(r),Na=null}else return t=bo(n),t!==null&&Jl(t),e.blockedOn=n,!1;t.shift()}return!0}function kc(e,t,n){ii(e)&&n.delete(t)}function zy(){Ia=!1,en!==null&&ii(en)&&(en=null),tn!==null&&ii(tn)&&(tn=null),nn!==null&&ii(nn)&&(nn=null),io.forEach(kc),so.forEach(kc)}function br(e,t){e.blockedOn===t&&(e.blockedOn=null,Ia||(Ia=!0,Ge.unstable_scheduleCallback(Ge.unstable_NormalPriority,zy)))}function ao(e){function t(o){return br(o,e)}if(0<zo.length){br(zo[0],e);for(var n=1;n<zo.length;n++){var r=zo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(en!==null&&br(en,e),tn!==null&&br(tn,e),nn!==null&&br(nn,e),io.forEach(t),so.forEach(t),n=0;n<Yt.length;n++)r=Yt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Yt.length&&(n=Yt[0],n.blockedOn===null);)zp(n),n.blockedOn===null&&Yt.shift()}var ar=It.ReactCurrentBatchConfig,Ei=!0;function By(e,t,n,r){var o=G,i=ar.transition;ar.transition=null;try{G=1,eu(e,t,n,r)}finally{G=o,ar.transition=i}}function $y(e,t,n,r){var o=G,i=ar.transition;ar.transition=null;try{G=4,eu(e,t,n,r)}finally{G=o,ar.transition=i}}function eu(e,t,n,r){if(Ei){var o=Fa(e,t,n,r);if(o===null)Gs(e,t,r,ki,n),Ec(e,r);else if(Fy(o,e,t,n,r))r.stopPropagation();else if(Ec(e,r),t&4&&-1<Iy.indexOf(e)){for(;o!==null;){var i=bo(o);if(i!==null&&Vp(i),i=Fa(e,t,n,r),i===null&&Gs(e,t,r,ki,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Gs(e,t,r,null,n)}}var ki=null;function Fa(e,t,n,r){if(ki=null,e=Xl(r),e=Cn(e),e!==null)if(t=Fn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=bp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ki=e,null}function Bp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ay()){case Zl:return 1;case Lp:return 4;case Ti:case My:return 16;case Np:return 536870912;default:return 16}default:return 16}}var Zt=null,tu=null,si=null;function $p(){if(si)return si;var e,t=tu,n=t.length,r,o="value"in Zt?Zt.value:Zt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return si=o.slice(e,1<r?1-r:void 0)}function ai(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Bo(){return!0}function Rc(){return!1}function Xe(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Bo:Rc,this.isPropagationStopped=Rc,this}return se(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Bo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Bo)},persist:function(){},isPersistent:Bo}),t}var Sr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},nu=Xe(Sr),Ro=se({},Sr,{view:0,detail:0}),Uy=Xe(Ro),Is,Fs,Ar,rs=se({},Ro,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ru,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ar&&(Ar&&e.type==="mousemove"?(Is=e.screenX-Ar.screenX,Fs=e.screenY-Ar.screenY):Fs=Is=0,Ar=e),Is)},movementY:function(e){return"movementY"in e?e.movementY:Fs}}),bc=Xe(rs),Wy=se({},rs,{dataTransfer:0}),Hy=Xe(Wy),Ky=se({},Ro,{relatedTarget:0}),zs=Xe(Ky),Gy=se({},Sr,{animationName:0,elapsedTime:0,pseudoElement:0}),Qy=Xe(Gy),Yy=se({},Sr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Xy=Xe(Yy),Zy=se({},Sr,{data:0}),Ac=Xe(Zy),qy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},e0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function t0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=e0[e])?!!t[e]:!1}function ru(){return t0}var n0=se({},Ro,{key:function(e){if(e.key){var t=qy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ai(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ru,charCode:function(e){return e.type==="keypress"?ai(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ai(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),r0=Xe(n0),o0=se({},rs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Mc=Xe(o0),i0=se({},Ro,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ru}),s0=Xe(i0),a0=se({},Sr,{propertyName:0,elapsedTime:0,pseudoElement:0}),l0=Xe(a0),u0=se({},rs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),c0=Xe(u0),d0=[9,13,27,32],ou=Lt&&"CompositionEvent"in window,Wr=null;Lt&&"documentMode"in document&&(Wr=document.documentMode);var f0=Lt&&"TextEvent"in window&&!Wr,Up=Lt&&(!ou||Wr&&8<Wr&&11>=Wr),_c=" ",Lc=!1;function Wp(e,t){switch(e){case"keyup":return d0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Hp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Un=!1;function p0(e,t){switch(e){case"compositionend":return Hp(t);case"keypress":return t.which!==32?null:(Lc=!0,_c);case"textInput":return e=t.data,e===_c&&Lc?null:e;default:return null}}function h0(e,t){if(Un)return e==="compositionend"||!ou&&Wp(e,t)?(e=$p(),si=tu=Zt=null,Un=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Up&&t.locale!=="ko"?null:t.data;default:return null}}var m0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Nc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!m0[e.type]:t==="textarea"}function Kp(e,t,n,r){Tp(r),t=Ri(t,"onChange"),0<t.length&&(n=new nu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Hr=null,lo=null;function g0(e){rh(e,0)}function os(e){var t=Kn(e);if(gp(t))return e}function v0(e,t){if(e==="change")return t}var Gp=!1;if(Lt){var Bs;if(Lt){var $s="oninput"in document;if(!$s){var Dc=document.createElement("div");Dc.setAttribute("oninput","return;"),$s=typeof Dc.oninput=="function"}Bs=$s}else Bs=!1;Gp=Bs&&(!document.documentMode||9<document.documentMode)}function Oc(){Hr&&(Hr.detachEvent("onpropertychange",Qp),lo=Hr=null)}function Qp(e){if(e.propertyName==="value"&&os(lo)){var t=[];Kp(t,lo,e,Xl(e)),Rp(g0,t)}}function y0(e,t,n){e==="focusin"?(Oc(),Hr=t,lo=n,Hr.attachEvent("onpropertychange",Qp)):e==="focusout"&&Oc()}function w0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return os(lo)}function x0(e,t){if(e==="click")return os(t)}function S0(e,t){if(e==="input"||e==="change")return os(t)}function P0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var dt=typeof Object.is=="function"?Object.is:P0;function uo(e,t){if(dt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Sa.call(t,o)||!dt(e[o],t[o]))return!1}return!0}function Vc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function jc(e,t){var n=Vc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Vc(n)}}function Yp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Yp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Xp(){for(var e=window,t=xi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=xi(e.document)}return t}function iu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function T0(e){var t=Xp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Yp(n.ownerDocument.documentElement,n)){if(r!==null&&iu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=jc(n,i);var s=jc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var C0=Lt&&"documentMode"in document&&11>=document.documentMode,Wn=null,za=null,Kr=null,Ba=!1;function Ic(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ba||Wn==null||Wn!==xi(r)||(r=Wn,"selectionStart"in r&&iu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Kr&&uo(Kr,r)||(Kr=r,r=Ri(za,"onSelect"),0<r.length&&(t=new nu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Wn)))}function $o(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Hn={animationend:$o("Animation","AnimationEnd"),animationiteration:$o("Animation","AnimationIteration"),animationstart:$o("Animation","AnimationStart"),transitionend:$o("Transition","TransitionEnd")},Us={},Zp={};Lt&&(Zp=document.createElement("div").style,"AnimationEvent"in window||(delete Hn.animationend.animation,delete Hn.animationiteration.animation,delete Hn.animationstart.animation),"TransitionEvent"in window||delete Hn.transitionend.transition);function is(e){if(Us[e])return Us[e];if(!Hn[e])return e;var t=Hn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Zp)return Us[e]=t[n];return e}var qp=is("animationend"),Jp=is("animationiteration"),eh=is("animationstart"),th=is("transitionend"),nh=new Map,Fc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function pn(e,t){nh.set(e,t),In(t,[e])}for(var Ws=0;Ws<Fc.length;Ws++){var Hs=Fc[Ws],E0=Hs.toLowerCase(),k0=Hs[0].toUpperCase()+Hs.slice(1);pn(E0,"on"+k0)}pn(qp,"onAnimationEnd");pn(Jp,"onAnimationIteration");pn(eh,"onAnimationStart");pn("dblclick","onDoubleClick");pn("focusin","onFocus");pn("focusout","onBlur");pn(th,"onTransitionEnd");dr("onMouseEnter",["mouseout","mouseover"]);dr("onMouseLeave",["mouseout","mouseover"]);dr("onPointerEnter",["pointerout","pointerover"]);dr("onPointerLeave",["pointerout","pointerover"]);In("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));In("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));In("onBeforeInput",["compositionend","keypress","textInput","paste"]);In("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));In("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));In("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),R0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Fr));function zc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Ey(r,t,void 0,e),e.currentTarget=null}function rh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;zc(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;zc(o,a,u),i=l}}}if(Pi)throw e=Va,Pi=!1,Va=null,e}function q(e,t){var n=t[Ka];n===void 0&&(n=t[Ka]=new Set);var r=e+"__bubble";n.has(r)||(oh(t,e,2,!1),n.add(r))}function Ks(e,t,n){var r=0;t&&(r|=4),oh(n,e,r,t)}var Uo="_reactListening"+Math.random().toString(36).slice(2);function co(e){if(!e[Uo]){e[Uo]=!0,dp.forEach(function(n){n!=="selectionchange"&&(R0.has(n)||Ks(n,!1,e),Ks(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Uo]||(t[Uo]=!0,Ks("selectionchange",!1,t))}}function oh(e,t,n,r){switch(Bp(t)){case 1:var o=By;break;case 4:o=$y;break;default:o=eu}n=o.bind(null,t,n,e),o=void 0,!Oa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Gs(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=Cn(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}Rp(function(){var u=i,d=Xl(n),c=[];e:{var f=nh.get(e);if(f!==void 0){var m=nu,v=e;switch(e){case"keypress":if(ai(n)===0)break e;case"keydown":case"keyup":m=r0;break;case"focusin":v="focus",m=zs;break;case"focusout":v="blur",m=zs;break;case"beforeblur":case"afterblur":m=zs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=bc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Hy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=s0;break;case qp:case Jp:case eh:m=Qy;break;case th:m=l0;break;case"scroll":m=Uy;break;case"wheel":m=c0;break;case"copy":case"cut":case"paste":m=Xy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Mc}var y=(t&4)!==0,x=!y&&e==="scroll",h=y?f!==null?f+"Capture":null:f;y=[];for(var p=u,g;p!==null;){g=p;var S=g.stateNode;if(g.tag===5&&S!==null&&(g=S,h!==null&&(S=oo(p,h),S!=null&&y.push(fo(p,S,g)))),x)break;p=p.return}0<y.length&&(f=new m(f,v,null,n,d),c.push({event:f,listeners:y}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",f&&n!==Na&&(v=n.relatedTarget||n.fromElement)&&(Cn(v)||v[Nt]))break e;if((m||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,m?(v=n.relatedTarget||n.toElement,m=u,v=v?Cn(v):null,v!==null&&(x=Fn(v),v!==x||v.tag!==5&&v.tag!==6)&&(v=null)):(m=null,v=u),m!==v)){if(y=bc,S="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(y=Mc,S="onPointerLeave",h="onPointerEnter",p="pointer"),x=m==null?f:Kn(m),g=v==null?f:Kn(v),f=new y(S,p+"leave",m,n,d),f.target=x,f.relatedTarget=g,S=null,Cn(d)===u&&(y=new y(h,p+"enter",v,n,d),y.target=g,y.relatedTarget=x,S=y),x=S,m&&v)t:{for(y=m,h=v,p=0,g=y;g;g=zn(g))p++;for(g=0,S=h;S;S=zn(S))g++;for(;0<p-g;)y=zn(y),p--;for(;0<g-p;)h=zn(h),g--;for(;p--;){if(y===h||h!==null&&y===h.alternate)break t;y=zn(y),h=zn(h)}y=null}else y=null;m!==null&&Bc(c,f,m,y,!1),v!==null&&x!==null&&Bc(c,x,v,y,!0)}}e:{if(f=u?Kn(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var P=v0;else if(Nc(f))if(Gp)P=S0;else{P=w0;var C=y0}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(P=x0);if(P&&(P=P(e,u))){Kp(c,P,n,d);break e}C&&C(e,f,u),e==="focusout"&&(C=f._wrapperState)&&C.controlled&&f.type==="number"&&ba(f,"number",f.value)}switch(C=u?Kn(u):window,e){case"focusin":(Nc(C)||C.contentEditable==="true")&&(Wn=C,za=u,Kr=null);break;case"focusout":Kr=za=Wn=null;break;case"mousedown":Ba=!0;break;case"contextmenu":case"mouseup":case"dragend":Ba=!1,Ic(c,n,d);break;case"selectionchange":if(C0)break;case"keydown":case"keyup":Ic(c,n,d)}var k;if(ou)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else Un?Wp(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Up&&n.locale!=="ko"&&(Un||T!=="onCompositionStart"?T==="onCompositionEnd"&&Un&&(k=$p()):(Zt=d,tu="value"in Zt?Zt.value:Zt.textContent,Un=!0)),C=Ri(u,T),0<C.length&&(T=new Ac(T,e,null,n,d),c.push({event:T,listeners:C}),k?T.data=k:(k=Hp(n),k!==null&&(T.data=k)))),(k=f0?p0(e,n):h0(e,n))&&(u=Ri(u,"onBeforeInput"),0<u.length&&(d=new Ac("onBeforeInput","beforeinput",null,n,d),c.push({event:d,listeners:u}),d.data=k))}rh(c,t)})}function fo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ri(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=oo(e,n),i!=null&&r.unshift(fo(e,i,o)),i=oo(e,t),i!=null&&r.push(fo(e,i,o))),e=e.return}return r}function zn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Bc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=oo(n,i),l!=null&&s.unshift(fo(n,l,a))):o||(l=oo(n,i),l!=null&&s.push(fo(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var b0=/\r\n?/g,A0=/\u0000|\uFFFD/g;function $c(e){return(typeof e=="string"?e:""+e).replace(b0,`
`).replace(A0,"")}function Wo(e,t,n){if(t=$c(t),$c(e)!==t&&n)throw Error(R(425))}function bi(){}var $a=null,Ua=null;function Wa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ha=typeof setTimeout=="function"?setTimeout:void 0,M0=typeof clearTimeout=="function"?clearTimeout:void 0,Uc=typeof Promise=="function"?Promise:void 0,_0=typeof queueMicrotask=="function"?queueMicrotask:typeof Uc<"u"?function(e){return Uc.resolve(null).then(e).catch(L0)}:Ha;function L0(e){setTimeout(function(){throw e})}function Qs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),ao(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);ao(t)}function rn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Wc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Pr=Math.random().toString(36).slice(2),mt="__reactFiber$"+Pr,po="__reactProps$"+Pr,Nt="__reactContainer$"+Pr,Ka="__reactEvents$"+Pr,N0="__reactListeners$"+Pr,D0="__reactHandles$"+Pr;function Cn(e){var t=e[mt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Nt]||n[mt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Wc(e);e!==null;){if(n=e[mt])return n;e=Wc(e)}return t}e=n,n=e.parentNode}return null}function bo(e){return e=e[mt]||e[Nt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Kn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function ss(e){return e[po]||null}var Ga=[],Gn=-1;function hn(e){return{current:e}}function J(e){0>Gn||(e.current=Ga[Gn],Ga[Gn]=null,Gn--)}function Y(e,t){Gn++,Ga[Gn]=e.current,e.current=t}var cn={},Ae=hn(cn),Ie=hn(!1),Ln=cn;function fr(e,t){var n=e.type.contextTypes;if(!n)return cn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Fe(e){return e=e.childContextTypes,e!=null}function Ai(){J(Ie),J(Ae)}function Hc(e,t,n){if(Ae.current!==cn)throw Error(R(168));Y(Ae,t),Y(Ie,n)}function ih(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(R(108,yy(e)||"Unknown",o));return se({},n,r)}function Mi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||cn,Ln=Ae.current,Y(Ae,e),Y(Ie,Ie.current),!0}function Kc(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=ih(e,t,Ln),r.__reactInternalMemoizedMergedChildContext=e,J(Ie),J(Ae),Y(Ae,e)):J(Ie),Y(Ie,n)}var Pt=null,as=!1,Ys=!1;function sh(e){Pt===null?Pt=[e]:Pt.push(e)}function O0(e){as=!0,sh(e)}function mn(){if(!Ys&&Pt!==null){Ys=!0;var e=0,t=G;try{var n=Pt;for(G=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Pt=null,as=!1}catch(o){throw Pt!==null&&(Pt=Pt.slice(e+1)),_p(Zl,mn),o}finally{G=t,Ys=!1}}return null}var Qn=[],Yn=0,_i=null,Li=0,Je=[],et=0,Nn=null,Tt=1,Ct="";function xn(e,t){Qn[Yn++]=Li,Qn[Yn++]=_i,_i=e,Li=t}function ah(e,t,n){Je[et++]=Tt,Je[et++]=Ct,Je[et++]=Nn,Nn=e;var r=Tt;e=Ct;var o=32-ut(r)-1;r&=~(1<<o),n+=1;var i=32-ut(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Tt=1<<32-ut(t)+o|n<<o|r,Ct=i+e}else Tt=1<<i|n<<o|r,Ct=e}function su(e){e.return!==null&&(xn(e,1),ah(e,1,0))}function au(e){for(;e===_i;)_i=Qn[--Yn],Qn[Yn]=null,Li=Qn[--Yn],Qn[Yn]=null;for(;e===Nn;)Nn=Je[--et],Je[et]=null,Ct=Je[--et],Je[et]=null,Tt=Je[--et],Je[et]=null}var Ke=null,He=null,ee=!1,lt=null;function lh(e,t){var n=tt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Gc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ke=e,He=rn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ke=e,He=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Nn!==null?{id:Tt,overflow:Ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=tt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ke=e,He=null,!0):!1;default:return!1}}function Qa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ya(e){if(ee){var t=He;if(t){var n=t;if(!Gc(e,t)){if(Qa(e))throw Error(R(418));t=rn(n.nextSibling);var r=Ke;t&&Gc(e,t)?lh(r,n):(e.flags=e.flags&-4097|2,ee=!1,Ke=e)}}else{if(Qa(e))throw Error(R(418));e.flags=e.flags&-4097|2,ee=!1,Ke=e}}}function Qc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ke=e}function Ho(e){if(e!==Ke)return!1;if(!ee)return Qc(e),ee=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Wa(e.type,e.memoizedProps)),t&&(t=He)){if(Qa(e))throw uh(),Error(R(418));for(;t;)lh(e,t),t=rn(t.nextSibling)}if(Qc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){He=rn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}He=null}}else He=Ke?rn(e.stateNode.nextSibling):null;return!0}function uh(){for(var e=He;e;)e=rn(e.nextSibling)}function pr(){He=Ke=null,ee=!1}function lu(e){lt===null?lt=[e]:lt.push(e)}var V0=It.ReactCurrentBatchConfig;function st(e,t){if(e&&e.defaultProps){t=se({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}var Ni=hn(null),Di=null,Xn=null,uu=null;function cu(){uu=Xn=Di=null}function du(e){var t=Ni.current;J(Ni),e._currentValue=t}function Xa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function lr(e,t){Di=e,uu=Xn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(je=!0),e.firstContext=null)}function rt(e){var t=e._currentValue;if(uu!==e)if(e={context:e,memoizedValue:t,next:null},Xn===null){if(Di===null)throw Error(R(308));Xn=e,Di.dependencies={lanes:0,firstContext:e}}else Xn=Xn.next=e;return t}var En=null;function fu(e){En===null?En=[e]:En.push(e)}function ch(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,fu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Dt(e,r)}function Dt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Qt=!1;function pu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function dh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function kt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function on(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,H&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Dt(e,n)}return o=r.interleaved,o===null?(t.next=t,fu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Dt(e,n)}function li(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ql(e,n)}}function Yc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Oi(e,t,n,r){var o=e.updateQueue;Qt=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==s&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(i!==null){var c=o.baseState;s=0,d=u=l=null,a=i;do{var f=a.lane,m=a.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,y=a;switch(f=t,m=n,y.tag){case 1:if(v=y.payload,typeof v=="function"){c=v.call(m,c,f);break e}c=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=y.payload,f=typeof v=="function"?v.call(m,c,f):v,f==null)break e;c=se({},c,f);break e;case 2:Qt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=o.effects,f===null?o.effects=[a]:f.push(a))}else m={eventTime:m,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=m,l=c):d=d.next=m,s|=f;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;f=a,a=f.next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}while(!0);if(d===null&&(l=c),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);On|=s,e.lanes=s,e.memoizedState=c}}function Xc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(R(191,o));o.call(r)}}}var fh=new cp.Component().refs;function Za(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:se({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ls={isMounted:function(e){return(e=e._reactInternals)?Fn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=_e(),o=an(e),i=kt(r,o);i.payload=t,n!=null&&(i.callback=n),t=on(e,i,o),t!==null&&(ct(t,e,o,r),li(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=_e(),o=an(e),i=kt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=on(e,i,o),t!==null&&(ct(t,e,o,r),li(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=_e(),r=an(e),o=kt(n,r);o.tag=2,t!=null&&(o.callback=t),t=on(e,o,r),t!==null&&(ct(t,e,r,n),li(t,e,r))}};function Zc(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!uo(n,r)||!uo(o,i):!0}function ph(e,t,n){var r=!1,o=cn,i=t.contextType;return typeof i=="object"&&i!==null?i=rt(i):(o=Fe(t)?Ln:Ae.current,r=t.contextTypes,i=(r=r!=null)?fr(e,o):cn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ls,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function qc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ls.enqueueReplaceState(t,t.state,null)}function qa(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=fh,pu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=rt(i):(i=Fe(t)?Ln:Ae.current,o.context=fr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Za(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&ls.enqueueReplaceState(o,o.state,null),Oi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Mr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=o.refs;a===fh&&(a=o.refs={}),s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function Ko(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Jc(e){var t=e._init;return t(e._payload)}function hh(e){function t(h,p){if(e){var g=h.deletions;g===null?(h.deletions=[p],h.flags|=16):g.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=ln(h,p),h.index=0,h.sibling=null,h}function i(h,p,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<p?(h.flags|=2,p):g):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function a(h,p,g,S){return p===null||p.tag!==6?(p=na(g,h.mode,S),p.return=h,p):(p=o(p,g),p.return=h,p)}function l(h,p,g,S){var P=g.type;return P===$n?d(h,p,g.props.children,S,g.key):p!==null&&(p.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Gt&&Jc(P)===p.type)?(S=o(p,g.props),S.ref=Mr(h,p,g),S.return=h,S):(S=hi(g.type,g.key,g.props,null,h.mode,S),S.ref=Mr(h,p,g),S.return=h,S)}function u(h,p,g,S){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=ra(g,h.mode,S),p.return=h,p):(p=o(p,g.children||[]),p.return=h,p)}function d(h,p,g,S,P){return p===null||p.tag!==7?(p=Mn(g,h.mode,S,P),p.return=h,p):(p=o(p,g),p.return=h,p)}function c(h,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=na(""+p,h.mode,g),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Oo:return g=hi(p.type,p.key,p.props,null,h.mode,g),g.ref=Mr(h,null,p),g.return=h,g;case Bn:return p=ra(p,h.mode,g),p.return=h,p;case Gt:var S=p._init;return c(h,S(p._payload),g)}if(jr(p)||Er(p))return p=Mn(p,h.mode,g,null),p.return=h,p;Ko(h,p)}return null}function f(h,p,g,S){var P=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return P!==null?null:a(h,p,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Oo:return g.key===P?l(h,p,g,S):null;case Bn:return g.key===P?u(h,p,g,S):null;case Gt:return P=g._init,f(h,p,P(g._payload),S)}if(jr(g)||Er(g))return P!==null?null:d(h,p,g,S,null);Ko(h,g)}return null}function m(h,p,g,S,P){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(g)||null,a(p,h,""+S,P);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Oo:return h=h.get(S.key===null?g:S.key)||null,l(p,h,S,P);case Bn:return h=h.get(S.key===null?g:S.key)||null,u(p,h,S,P);case Gt:var C=S._init;return m(h,p,g,C(S._payload),P)}if(jr(S)||Er(S))return h=h.get(g)||null,d(p,h,S,P,null);Ko(p,S)}return null}function v(h,p,g,S){for(var P=null,C=null,k=p,T=p=0,D=null;k!==null&&T<g.length;T++){k.index>T?(D=k,k=null):D=k.sibling;var L=f(h,k,g[T],S);if(L===null){k===null&&(k=D);break}e&&k&&L.alternate===null&&t(h,k),p=i(L,p,T),C===null?P=L:C.sibling=L,C=L,k=D}if(T===g.length)return n(h,k),ee&&xn(h,T),P;if(k===null){for(;T<g.length;T++)k=c(h,g[T],S),k!==null&&(p=i(k,p,T),C===null?P=k:C.sibling=k,C=k);return ee&&xn(h,T),P}for(k=r(h,k);T<g.length;T++)D=m(k,h,T,g[T],S),D!==null&&(e&&D.alternate!==null&&k.delete(D.key===null?T:D.key),p=i(D,p,T),C===null?P=D:C.sibling=D,C=D);return e&&k.forEach(function(_){return t(h,_)}),ee&&xn(h,T),P}function y(h,p,g,S){var P=Er(g);if(typeof P!="function")throw Error(R(150));if(g=P.call(g),g==null)throw Error(R(151));for(var C=P=null,k=p,T=p=0,D=null,L=g.next();k!==null&&!L.done;T++,L=g.next()){k.index>T?(D=k,k=null):D=k.sibling;var _=f(h,k,L.value,S);if(_===null){k===null&&(k=D);break}e&&k&&_.alternate===null&&t(h,k),p=i(_,p,T),C===null?P=_:C.sibling=_,C=_,k=D}if(L.done)return n(h,k),ee&&xn(h,T),P;if(k===null){for(;!L.done;T++,L=g.next())L=c(h,L.value,S),L!==null&&(p=i(L,p,T),C===null?P=L:C.sibling=L,C=L);return ee&&xn(h,T),P}for(k=r(h,k);!L.done;T++,L=g.next())L=m(k,h,T,L.value,S),L!==null&&(e&&L.alternate!==null&&k.delete(L.key===null?T:L.key),p=i(L,p,T),C===null?P=L:C.sibling=L,C=L);return e&&k.forEach(function(E){return t(h,E)}),ee&&xn(h,T),P}function x(h,p,g,S){if(typeof g=="object"&&g!==null&&g.type===$n&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Oo:e:{for(var P=g.key,C=p;C!==null;){if(C.key===P){if(P=g.type,P===$n){if(C.tag===7){n(h,C.sibling),p=o(C,g.props.children),p.return=h,h=p;break e}}else if(C.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Gt&&Jc(P)===C.type){n(h,C.sibling),p=o(C,g.props),p.ref=Mr(h,C,g),p.return=h,h=p;break e}n(h,C);break}else t(h,C);C=C.sibling}g.type===$n?(p=Mn(g.props.children,h.mode,S,g.key),p.return=h,h=p):(S=hi(g.type,g.key,g.props,null,h.mode,S),S.ref=Mr(h,p,g),S.return=h,h=S)}return s(h);case Bn:e:{for(C=g.key;p!==null;){if(p.key===C)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(h,p.sibling),p=o(p,g.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=ra(g,h.mode,S),p.return=h,h=p}return s(h);case Gt:return C=g._init,x(h,p,C(g._payload),S)}if(jr(g))return v(h,p,g,S);if(Er(g))return y(h,p,g,S);Ko(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,g),p.return=h,h=p):(n(h,p),p=na(g,h.mode,S),p.return=h,h=p),s(h)):n(h,p)}return x}var hr=hh(!0),mh=hh(!1),Ao={},vt=hn(Ao),ho=hn(Ao),mo=hn(Ao);function kn(e){if(e===Ao)throw Error(R(174));return e}function hu(e,t){switch(Y(mo,t),Y(ho,e),Y(vt,Ao),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ma(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ma(t,e)}J(vt),Y(vt,t)}function mr(){J(vt),J(ho),J(mo)}function gh(e){kn(mo.current);var t=kn(vt.current),n=Ma(t,e.type);t!==n&&(Y(ho,e),Y(vt,n))}function mu(e){ho.current===e&&(J(vt),J(ho))}var re=hn(0);function Vi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Xs=[];function gu(){for(var e=0;e<Xs.length;e++)Xs[e]._workInProgressVersionPrimary=null;Xs.length=0}var ui=It.ReactCurrentDispatcher,Zs=It.ReactCurrentBatchConfig,Dn=0,ie=null,he=null,ve=null,ji=!1,Gr=!1,go=0,j0=0;function Te(){throw Error(R(321))}function vu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!dt(e[n],t[n]))return!1;return!0}function yu(e,t,n,r,o,i){if(Dn=i,ie=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ui.current=e===null||e.memoizedState===null?B0:$0,e=n(r,o),Gr){i=0;do{if(Gr=!1,go=0,25<=i)throw Error(R(301));i+=1,ve=he=null,t.updateQueue=null,ui.current=U0,e=n(r,o)}while(Gr)}if(ui.current=Ii,t=he!==null&&he.next!==null,Dn=0,ve=he=ie=null,ji=!1,t)throw Error(R(300));return e}function wu(){var e=go!==0;return go=0,e}function pt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ve===null?ie.memoizedState=ve=e:ve=ve.next=e,ve}function ot(){if(he===null){var e=ie.alternate;e=e!==null?e.memoizedState:null}else e=he.next;var t=ve===null?ie.memoizedState:ve.next;if(t!==null)ve=t,he=e;else{if(e===null)throw Error(R(310));he=e,e={memoizedState:he.memoizedState,baseState:he.baseState,baseQueue:he.baseQueue,queue:he.queue,next:null},ve===null?ie.memoizedState=ve=e:ve=ve.next=e}return ve}function vo(e,t){return typeof t=="function"?t(e):t}function qs(e){var t=ot(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=he,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var d=u.lane;if((Dn&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var c={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=c,s=r):l=l.next=c,ie.lanes|=d,On|=d}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,dt(r,t.memoizedState)||(je=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ie.lanes|=i,On|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Js(e){var t=ot(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);dt(i,t.memoizedState)||(je=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function vh(){}function yh(e,t){var n=ie,r=ot(),o=t(),i=!dt(r.memoizedState,o);if(i&&(r.memoizedState=o,je=!0),r=r.queue,xu(Sh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ve!==null&&ve.memoizedState.tag&1){if(n.flags|=2048,yo(9,xh.bind(null,n,r,o,t),void 0,null),ye===null)throw Error(R(349));Dn&30||wh(n,t,o)}return o}function wh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ie.updateQueue,t===null?(t={lastEffect:null,stores:null},ie.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function xh(e,t,n,r){t.value=n,t.getSnapshot=r,Ph(t)&&Th(e)}function Sh(e,t,n){return n(function(){Ph(t)&&Th(e)})}function Ph(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!dt(e,n)}catch{return!0}}function Th(e){var t=Dt(e,1);t!==null&&ct(t,e,1,-1)}function ed(e){var t=pt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:vo,lastRenderedState:e},t.queue=e,e=e.dispatch=z0.bind(null,ie,e),[t.memoizedState,e]}function yo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ie.updateQueue,t===null?(t={lastEffect:null,stores:null},ie.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Ch(){return ot().memoizedState}function ci(e,t,n,r){var o=pt();ie.flags|=e,o.memoizedState=yo(1|t,n,void 0,r===void 0?null:r)}function us(e,t,n,r){var o=ot();r=r===void 0?null:r;var i=void 0;if(he!==null){var s=he.memoizedState;if(i=s.destroy,r!==null&&vu(r,s.deps)){o.memoizedState=yo(t,n,i,r);return}}ie.flags|=e,o.memoizedState=yo(1|t,n,i,r)}function td(e,t){return ci(8390656,8,e,t)}function xu(e,t){return us(2048,8,e,t)}function Eh(e,t){return us(4,2,e,t)}function kh(e,t){return us(4,4,e,t)}function Rh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function bh(e,t,n){return n=n!=null?n.concat([e]):null,us(4,4,Rh.bind(null,t,e),n)}function Su(){}function Ah(e,t){var n=ot();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&vu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Mh(e,t){var n=ot();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&vu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function _h(e,t,n){return Dn&21?(dt(n,t)||(n=Dp(),ie.lanes|=n,On|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,je=!0),e.memoizedState=n)}function I0(e,t){var n=G;G=n!==0&&4>n?n:4,e(!0);var r=Zs.transition;Zs.transition={};try{e(!1),t()}finally{G=n,Zs.transition=r}}function Lh(){return ot().memoizedState}function F0(e,t,n){var r=an(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Nh(e))Dh(t,n);else if(n=ch(e,t,n,r),n!==null){var o=_e();ct(n,e,r,o),Oh(n,t,r)}}function z0(e,t,n){var r=an(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Nh(e))Dh(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,dt(a,s)){var l=t.interleaved;l===null?(o.next=o,fu(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=ch(e,t,o,r),n!==null&&(o=_e(),ct(n,e,r,o),Oh(n,t,r))}}function Nh(e){var t=e.alternate;return e===ie||t!==null&&t===ie}function Dh(e,t){Gr=ji=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Oh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ql(e,n)}}var Ii={readContext:rt,useCallback:Te,useContext:Te,useEffect:Te,useImperativeHandle:Te,useInsertionEffect:Te,useLayoutEffect:Te,useMemo:Te,useReducer:Te,useRef:Te,useState:Te,useDebugValue:Te,useDeferredValue:Te,useTransition:Te,useMutableSource:Te,useSyncExternalStore:Te,useId:Te,unstable_isNewReconciler:!1},B0={readContext:rt,useCallback:function(e,t){return pt().memoizedState=[e,t===void 0?null:t],e},useContext:rt,useEffect:td,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ci(4194308,4,Rh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ci(4194308,4,e,t)},useInsertionEffect:function(e,t){return ci(4,2,e,t)},useMemo:function(e,t){var n=pt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=pt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=F0.bind(null,ie,e),[r.memoizedState,e]},useRef:function(e){var t=pt();return e={current:e},t.memoizedState=e},useState:ed,useDebugValue:Su,useDeferredValue:function(e){return pt().memoizedState=e},useTransition:function(){var e=ed(!1),t=e[0];return e=I0.bind(null,e[1]),pt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ie,o=pt();if(ee){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),ye===null)throw Error(R(349));Dn&30||wh(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,td(Sh.bind(null,r,i,e),[e]),r.flags|=2048,yo(9,xh.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=pt(),t=ye.identifierPrefix;if(ee){var n=Ct,r=Tt;n=(r&~(1<<32-ut(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=go++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=j0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},$0={readContext:rt,useCallback:Ah,useContext:rt,useEffect:xu,useImperativeHandle:bh,useInsertionEffect:Eh,useLayoutEffect:kh,useMemo:Mh,useReducer:qs,useRef:Ch,useState:function(){return qs(vo)},useDebugValue:Su,useDeferredValue:function(e){var t=ot();return _h(t,he.memoizedState,e)},useTransition:function(){var e=qs(vo)[0],t=ot().memoizedState;return[e,t]},useMutableSource:vh,useSyncExternalStore:yh,useId:Lh,unstable_isNewReconciler:!1},U0={readContext:rt,useCallback:Ah,useContext:rt,useEffect:xu,useImperativeHandle:bh,useInsertionEffect:Eh,useLayoutEffect:kh,useMemo:Mh,useReducer:Js,useRef:Ch,useState:function(){return Js(vo)},useDebugValue:Su,useDeferredValue:function(e){var t=ot();return he===null?t.memoizedState=e:_h(t,he.memoizedState,e)},useTransition:function(){var e=Js(vo)[0],t=ot().memoizedState;return[e,t]},useMutableSource:vh,useSyncExternalStore:yh,useId:Lh,unstable_isNewReconciler:!1};function gr(e,t){try{var n="",r=t;do n+=vy(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function ea(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ja(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var W0=typeof WeakMap=="function"?WeakMap:Map;function Vh(e,t,n){n=kt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){zi||(zi=!0,ul=r),Ja(e,t)},n}function jh(e,t,n){n=kt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ja(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Ja(e,t),typeof r!="function"&&(sn===null?sn=new Set([this]):sn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function nd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new W0;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=o1.bind(null,e,t,n),t.then(e,e))}function rd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function od(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=kt(-1,1),t.tag=2,on(n,t,1))),n.lanes|=1),e)}var H0=It.ReactCurrentOwner,je=!1;function Me(e,t,n,r){t.child=e===null?mh(t,null,n,r):hr(t,e.child,n,r)}function id(e,t,n,r,o){n=n.render;var i=t.ref;return lr(t,o),r=yu(e,t,n,r,i,o),n=wu(),e!==null&&!je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ot(e,t,o)):(ee&&n&&su(t),t.flags|=1,Me(e,t,r,o),t.child)}function sd(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Au(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Ih(e,t,i,r,o)):(e=hi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:uo,n(s,r)&&e.ref===t.ref)return Ot(e,t,o)}return t.flags|=1,e=ln(i,r),e.ref=t.ref,e.return=t,t.child=e}function Ih(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(uo(i,r)&&e.ref===t.ref)if(je=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(je=!0);else return t.lanes=e.lanes,Ot(e,t,o)}return el(e,t,n,r,o)}function Fh(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Y(qn,Ue),Ue|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Y(qn,Ue),Ue|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Y(qn,Ue),Ue|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Y(qn,Ue),Ue|=r;return Me(e,t,o,n),t.child}function zh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function el(e,t,n,r,o){var i=Fe(n)?Ln:Ae.current;return i=fr(t,i),lr(t,o),n=yu(e,t,n,r,i,o),r=wu(),e!==null&&!je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ot(e,t,o)):(ee&&r&&su(t),t.flags|=1,Me(e,t,n,o),t.child)}function ad(e,t,n,r,o){if(Fe(n)){var i=!0;Mi(t)}else i=!1;if(lr(t,o),t.stateNode===null)di(e,t),ph(t,n,r),qa(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=rt(u):(u=Fe(n)?Ln:Ae.current,u=fr(t,u));var d=n.getDerivedStateFromProps,c=typeof d=="function"||typeof s.getSnapshotBeforeUpdate=="function";c||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&qc(t,s,r,u),Qt=!1;var f=t.memoizedState;s.state=f,Oi(t,r,s,o),l=t.memoizedState,a!==r||f!==l||Ie.current||Qt?(typeof d=="function"&&(Za(t,n,d,r),l=t.memoizedState),(a=Qt||Zc(t,n,a,r,f,l,u))?(c||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,dh(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:st(t.type,a),s.props=u,c=t.pendingProps,f=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=rt(l):(l=Fe(n)?Ln:Ae.current,l=fr(t,l));var m=n.getDerivedStateFromProps;(d=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==c||f!==l)&&qc(t,s,r,l),Qt=!1,f=t.memoizedState,s.state=f,Oi(t,r,s,o);var v=t.memoizedState;a!==c||f!==v||Ie.current||Qt?(typeof m=="function"&&(Za(t,n,m,r),v=t.memoizedState),(u=Qt||Zc(t,n,u,r,f,v,l)||!1)?(d||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return tl(e,t,n,r,i,o)}function tl(e,t,n,r,o,i){zh(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Kc(t,n,!1),Ot(e,t,i);r=t.stateNode,H0.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=hr(t,e.child,null,i),t.child=hr(t,null,a,i)):Me(e,t,a,i),t.memoizedState=r.state,o&&Kc(t,n,!0),t.child}function Bh(e){var t=e.stateNode;t.pendingContext?Hc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Hc(e,t.context,!1),hu(e,t.containerInfo)}function ld(e,t,n,r,o){return pr(),lu(o),t.flags|=256,Me(e,t,n,r),t.child}var nl={dehydrated:null,treeContext:null,retryLane:0};function rl(e){return{baseLanes:e,cachePool:null,transitions:null}}function $h(e,t,n){var r=t.pendingProps,o=re.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Y(re,o&1),e===null)return Ya(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=fs(s,r,0,null),e=Mn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=rl(n),t.memoizedState=nl,e):Pu(t,s));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return K0(e,t,s,r,a,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=ln(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=ln(a,i):(i=Mn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?rl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=nl,r}return i=e.child,e=i.sibling,r=ln(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Pu(e,t){return t=fs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Go(e,t,n,r){return r!==null&&lu(r),hr(t,e.child,null,n),e=Pu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function K0(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=ea(Error(R(422))),Go(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=fs({mode:"visible",children:r.children},o,0,null),i=Mn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&hr(t,e.child,null,s),t.child.memoizedState=rl(s),t.memoizedState=nl,i);if(!(t.mode&1))return Go(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(R(419)),r=ea(i,r,void 0),Go(e,t,s,r)}if(a=(s&e.childLanes)!==0,je||a){if(r=ye,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Dt(e,o),ct(r,e,o,-1))}return bu(),r=ea(Error(R(421))),Go(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=i1.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,He=rn(o.nextSibling),Ke=t,ee=!0,lt=null,e!==null&&(Je[et++]=Tt,Je[et++]=Ct,Je[et++]=Nn,Tt=e.id,Ct=e.overflow,Nn=t),t=Pu(t,r.children),t.flags|=4096,t)}function ud(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Xa(e.return,t,n)}function ta(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Uh(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Me(e,t,r.children,n),r=re.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ud(e,n,t);else if(e.tag===19)ud(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Y(re,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Vi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),ta(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Vi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}ta(t,!0,n,null,i);break;case"together":ta(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function di(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ot(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),On|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=ln(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=ln(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function G0(e,t,n){switch(t.tag){case 3:Bh(t),pr();break;case 5:gh(t);break;case 1:Fe(t.type)&&Mi(t);break;case 4:hu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Y(Ni,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Y(re,re.current&1),t.flags|=128,null):n&t.child.childLanes?$h(e,t,n):(Y(re,re.current&1),e=Ot(e,t,n),e!==null?e.sibling:null);Y(re,re.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Uh(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Y(re,re.current),r)break;return null;case 22:case 23:return t.lanes=0,Fh(e,t,n)}return Ot(e,t,n)}var Wh,ol,Hh,Kh;Wh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ol=function(){};Hh=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,kn(vt.current);var i=null;switch(n){case"input":o=ka(e,o),r=ka(e,r),i=[];break;case"select":o=se({},o,{value:void 0}),r=se({},r,{value:void 0}),i=[];break;case"textarea":o=Aa(e,o),r=Aa(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=bi)}_a(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(no.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(no.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&q("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Kh=function(e,t,n,r){n!==r&&(t.flags|=4)};function _r(e,t){if(!ee)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ce(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Q0(e,t,n){var r=t.pendingProps;switch(au(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ce(t),null;case 1:return Fe(t.type)&&Ai(),Ce(t),null;case 3:return r=t.stateNode,mr(),J(Ie),J(Ae),gu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ho(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,lt!==null&&(fl(lt),lt=null))),ol(e,t),Ce(t),null;case 5:mu(t);var o=kn(mo.current);if(n=t.type,e!==null&&t.stateNode!=null)Hh(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return Ce(t),null}if(e=kn(vt.current),Ho(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[mt]=t,r[po]=i,e=(t.mode&1)!==0,n){case"dialog":q("cancel",r),q("close",r);break;case"iframe":case"object":case"embed":q("load",r);break;case"video":case"audio":for(o=0;o<Fr.length;o++)q(Fr[o],r);break;case"source":q("error",r);break;case"img":case"image":case"link":q("error",r),q("load",r);break;case"details":q("toggle",r);break;case"input":yc(r,i),q("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},q("invalid",r);break;case"textarea":xc(r,i),q("invalid",r)}_a(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Wo(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Wo(r.textContent,a,e),o=["children",""+a]):no.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&q("scroll",r)}switch(n){case"input":Vo(r),wc(r,i,!0);break;case"textarea":Vo(r),Sc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=bi)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=wp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[mt]=t,e[po]=r,Wh(e,t,!1,!1),t.stateNode=e;e:{switch(s=La(n,r),n){case"dialog":q("cancel",e),q("close",e),o=r;break;case"iframe":case"object":case"embed":q("load",e),o=r;break;case"video":case"audio":for(o=0;o<Fr.length;o++)q(Fr[o],e);o=r;break;case"source":q("error",e),o=r;break;case"img":case"image":case"link":q("error",e),q("load",e),o=r;break;case"details":q("toggle",e),o=r;break;case"input":yc(e,r),o=ka(e,r),q("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=se({},r,{value:void 0}),q("invalid",e);break;case"textarea":xc(e,r),o=Aa(e,r),q("invalid",e);break;default:o=r}_a(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?Pp(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&xp(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ro(e,l):typeof l=="number"&&ro(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(no.hasOwnProperty(i)?l!=null&&i==="onScroll"&&q("scroll",e):l!=null&&Kl(e,i,l,s))}switch(n){case"input":Vo(e),wc(e,r,!1);break;case"textarea":Vo(e),Sc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+un(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?or(e,!!r.multiple,i,!1):r.defaultValue!=null&&or(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=bi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ce(t),null;case 6:if(e&&t.stateNode!=null)Kh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=kn(mo.current),kn(vt.current),Ho(t)){if(r=t.stateNode,n=t.memoizedProps,r[mt]=t,(i=r.nodeValue!==n)&&(e=Ke,e!==null))switch(e.tag){case 3:Wo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Wo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[mt]=t,t.stateNode=r}return Ce(t),null;case 13:if(J(re),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ee&&He!==null&&t.mode&1&&!(t.flags&128))uh(),pr(),t.flags|=98560,i=!1;else if(i=Ho(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(R(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(R(317));i[mt]=t}else pr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ce(t),i=!1}else lt!==null&&(fl(lt),lt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||re.current&1?me===0&&(me=3):bu())),t.updateQueue!==null&&(t.flags|=4),Ce(t),null);case 4:return mr(),ol(e,t),e===null&&co(t.stateNode.containerInfo),Ce(t),null;case 10:return du(t.type._context),Ce(t),null;case 17:return Fe(t.type)&&Ai(),Ce(t),null;case 19:if(J(re),i=t.memoizedState,i===null)return Ce(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)_r(i,!1);else{if(me!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Vi(e),s!==null){for(t.flags|=128,_r(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Y(re,re.current&1|2),t.child}e=e.sibling}i.tail!==null&&ce()>vr&&(t.flags|=128,r=!0,_r(i,!1),t.lanes=4194304)}else{if(!r)if(e=Vi(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),_r(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!ee)return Ce(t),null}else 2*ce()-i.renderingStartTime>vr&&n!==1073741824&&(t.flags|=128,r=!0,_r(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ce(),t.sibling=null,n=re.current,Y(re,r?n&1|2:n&1),t):(Ce(t),null);case 22:case 23:return Ru(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ue&1073741824&&(Ce(t),t.subtreeFlags&6&&(t.flags|=8192)):Ce(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function Y0(e,t){switch(au(t),t.tag){case 1:return Fe(t.type)&&Ai(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return mr(),J(Ie),J(Ae),gu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return mu(t),null;case 13:if(J(re),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));pr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return J(re),null;case 4:return mr(),null;case 10:return du(t.type._context),null;case 22:case 23:return Ru(),null;case 24:return null;default:return null}}var Qo=!1,ke=!1,X0=typeof WeakSet=="function"?WeakSet:Set,N=null;function Zn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){le(e,t,r)}else n.current=null}function il(e,t,n){try{n()}catch(r){le(e,t,r)}}var cd=!1;function Z0(e,t){if($a=Ei,e=Xp(),iu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,d=0,c=e,f=null;t:for(;;){for(var m;c!==n||o!==0&&c.nodeType!==3||(a=s+o),c!==i||r!==0&&c.nodeType!==3||(l=s+r),c.nodeType===3&&(s+=c.nodeValue.length),(m=c.firstChild)!==null;)f=c,c=m;for(;;){if(c===e)break t;if(f===n&&++u===o&&(a=s),f===i&&++d===r&&(l=s),(m=c.nextSibling)!==null)break;c=f,f=c.parentNode}c=m}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ua={focusedElem:e,selectionRange:n},Ei=!1,N=t;N!==null;)if(t=N,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,N=e;else for(;N!==null;){t=N;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var y=v.memoizedProps,x=v.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?y:st(t.type,y),x);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(S){le(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,N=e;break}N=t.return}return v=cd,cd=!1,v}function Qr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&il(t,n,i)}o=o.next}while(o!==r)}}function cs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function sl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Gh(e){var t=e.alternate;t!==null&&(e.alternate=null,Gh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[mt],delete t[po],delete t[Ka],delete t[N0],delete t[D0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Qh(e){return e.tag===5||e.tag===3||e.tag===4}function dd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Qh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function al(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=bi));else if(r!==4&&(e=e.child,e!==null))for(al(e,t,n),e=e.sibling;e!==null;)al(e,t,n),e=e.sibling}function ll(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ll(e,t,n),e=e.sibling;e!==null;)ll(e,t,n),e=e.sibling}var we=null,at=!1;function Ut(e,t,n){for(n=n.child;n!==null;)Yh(e,t,n),n=n.sibling}function Yh(e,t,n){if(gt&&typeof gt.onCommitFiberUnmount=="function")try{gt.onCommitFiberUnmount(ns,n)}catch{}switch(n.tag){case 5:ke||Zn(n,t);case 6:var r=we,o=at;we=null,Ut(e,t,n),we=r,at=o,we!==null&&(at?(e=we,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):we.removeChild(n.stateNode));break;case 18:we!==null&&(at?(e=we,n=n.stateNode,e.nodeType===8?Qs(e.parentNode,n):e.nodeType===1&&Qs(e,n),ao(e)):Qs(we,n.stateNode));break;case 4:r=we,o=at,we=n.stateNode.containerInfo,at=!0,Ut(e,t,n),we=r,at=o;break;case 0:case 11:case 14:case 15:if(!ke&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&il(n,t,s),o=o.next}while(o!==r)}Ut(e,t,n);break;case 1:if(!ke&&(Zn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){le(n,t,a)}Ut(e,t,n);break;case 21:Ut(e,t,n);break;case 22:n.mode&1?(ke=(r=ke)||n.memoizedState!==null,Ut(e,t,n),ke=r):Ut(e,t,n);break;default:Ut(e,t,n)}}function fd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new X0),t.forEach(function(r){var o=s1.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function it(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:we=a.stateNode,at=!1;break e;case 3:we=a.stateNode.containerInfo,at=!0;break e;case 4:we=a.stateNode.containerInfo,at=!0;break e}a=a.return}if(we===null)throw Error(R(160));Yh(i,s,o),we=null,at=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){le(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Xh(t,e),t=t.sibling}function Xh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(it(t,e),ft(e),r&4){try{Qr(3,e,e.return),cs(3,e)}catch(y){le(e,e.return,y)}try{Qr(5,e,e.return)}catch(y){le(e,e.return,y)}}break;case 1:it(t,e),ft(e),r&512&&n!==null&&Zn(n,n.return);break;case 5:if(it(t,e),ft(e),r&512&&n!==null&&Zn(n,n.return),e.flags&32){var o=e.stateNode;try{ro(o,"")}catch(y){le(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&vp(o,i),La(a,s);var u=La(a,i);for(s=0;s<l.length;s+=2){var d=l[s],c=l[s+1];d==="style"?Pp(o,c):d==="dangerouslySetInnerHTML"?xp(o,c):d==="children"?ro(o,c):Kl(o,d,c,u)}switch(a){case"input":Ra(o,i);break;case"textarea":yp(o,i);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var m=i.value;m!=null?or(o,!!i.multiple,m,!1):f!==!!i.multiple&&(i.defaultValue!=null?or(o,!!i.multiple,i.defaultValue,!0):or(o,!!i.multiple,i.multiple?[]:"",!1))}o[po]=i}catch(y){le(e,e.return,y)}}break;case 6:if(it(t,e),ft(e),r&4){if(e.stateNode===null)throw Error(R(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){le(e,e.return,y)}}break;case 3:if(it(t,e),ft(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ao(t.containerInfo)}catch(y){le(e,e.return,y)}break;case 4:it(t,e),ft(e);break;case 13:it(t,e),ft(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Eu=ce())),r&4&&fd(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(ke=(u=ke)||d,it(t,e),ke=u):it(t,e),ft(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(N=e,d=e.child;d!==null;){for(c=N=d;N!==null;){switch(f=N,m=f.child,f.tag){case 0:case 11:case 14:case 15:Qr(4,f,f.return);break;case 1:Zn(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(y){le(r,n,y)}}break;case 5:Zn(f,f.return);break;case 22:if(f.memoizedState!==null){hd(c);continue}}m!==null?(m.return=f,N=m):hd(c)}d=d.sibling}e:for(d=null,c=e;;){if(c.tag===5){if(d===null){d=c;try{o=c.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=c.stateNode,l=c.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Sp("display",s))}catch(y){le(e,e.return,y)}}}else if(c.tag===6){if(d===null)try{c.stateNode.nodeValue=u?"":c.memoizedProps}catch(y){le(e,e.return,y)}}else if((c.tag!==22&&c.tag!==23||c.memoizedState===null||c===e)&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===e)break e;for(;c.sibling===null;){if(c.return===null||c.return===e)break e;d===c&&(d=null),c=c.return}d===c&&(d=null),c.sibling.return=c.return,c=c.sibling}}break;case 19:it(t,e),ft(e),r&4&&fd(e);break;case 21:break;default:it(t,e),ft(e)}}function ft(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Qh(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(ro(o,""),r.flags&=-33);var i=dd(e);ll(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=dd(e);al(e,a,s);break;default:throw Error(R(161))}}catch(l){le(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function q0(e,t,n){N=e,Zh(e)}function Zh(e,t,n){for(var r=(e.mode&1)!==0;N!==null;){var o=N,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Qo;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||ke;a=Qo;var u=ke;if(Qo=s,(ke=l)&&!u)for(N=o;N!==null;)s=N,l=s.child,s.tag===22&&s.memoizedState!==null?md(o):l!==null?(l.return=s,N=l):md(o);for(;i!==null;)N=i,Zh(i),i=i.sibling;N=o,Qo=a,ke=u}pd(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,N=i):pd(e)}}function pd(e){for(;N!==null;){var t=N;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ke||cs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ke)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:st(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Xc(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Xc(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var c=d.dehydrated;c!==null&&ao(c)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}ke||t.flags&512&&sl(t)}catch(f){le(t,t.return,f)}}if(t===e){N=null;break}if(n=t.sibling,n!==null){n.return=t.return,N=n;break}N=t.return}}function hd(e){for(;N!==null;){var t=N;if(t===e){N=null;break}var n=t.sibling;if(n!==null){n.return=t.return,N=n;break}N=t.return}}function md(e){for(;N!==null;){var t=N;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{cs(4,t)}catch(l){le(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){le(t,o,l)}}var i=t.return;try{sl(t)}catch(l){le(t,i,l)}break;case 5:var s=t.return;try{sl(t)}catch(l){le(t,s,l)}}}catch(l){le(t,t.return,l)}if(t===e){N=null;break}var a=t.sibling;if(a!==null){a.return=t.return,N=a;break}N=t.return}}var J0=Math.ceil,Fi=It.ReactCurrentDispatcher,Tu=It.ReactCurrentOwner,nt=It.ReactCurrentBatchConfig,H=0,ye=null,fe=null,Se=0,Ue=0,qn=hn(0),me=0,wo=null,On=0,ds=0,Cu=0,Yr=null,Ve=null,Eu=0,vr=1/0,St=null,zi=!1,ul=null,sn=null,Yo=!1,qt=null,Bi=0,Xr=0,cl=null,fi=-1,pi=0;function _e(){return H&6?ce():fi!==-1?fi:fi=ce()}function an(e){return e.mode&1?H&2&&Se!==0?Se&-Se:V0.transition!==null?(pi===0&&(pi=Dp()),pi):(e=G,e!==0||(e=window.event,e=e===void 0?16:Bp(e.type)),e):1}function ct(e,t,n,r){if(50<Xr)throw Xr=0,cl=null,Error(R(185));ko(e,n,r),(!(H&2)||e!==ye)&&(e===ye&&(!(H&2)&&(ds|=n),me===4&&Xt(e,Se)),ze(e,r),n===1&&H===0&&!(t.mode&1)&&(vr=ce()+500,as&&mn()))}function ze(e,t){var n=e.callbackNode;Vy(e,t);var r=Ci(e,e===ye?Se:0);if(r===0)n!==null&&Cc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Cc(n),t===1)e.tag===0?O0(gd.bind(null,e)):sh(gd.bind(null,e)),_0(function(){!(H&6)&&mn()}),n=null;else{switch(Op(r)){case 1:n=Zl;break;case 4:n=Lp;break;case 16:n=Ti;break;case 536870912:n=Np;break;default:n=Ti}n=im(n,qh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function qh(e,t){if(fi=-1,pi=0,H&6)throw Error(R(327));var n=e.callbackNode;if(ur()&&e.callbackNode!==n)return null;var r=Ci(e,e===ye?Se:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=$i(e,r);else{t=r;var o=H;H|=2;var i=em();(ye!==e||Se!==t)&&(St=null,vr=ce()+500,An(e,t));do try{n1();break}catch(a){Jh(e,a)}while(!0);cu(),Fi.current=i,H=o,fe!==null?t=0:(ye=null,Se=0,t=me)}if(t!==0){if(t===2&&(o=ja(e),o!==0&&(r=o,t=dl(e,o))),t===1)throw n=wo,An(e,0),Xt(e,r),ze(e,ce()),n;if(t===6)Xt(e,r);else{if(o=e.current.alternate,!(r&30)&&!e1(o)&&(t=$i(e,r),t===2&&(i=ja(e),i!==0&&(r=i,t=dl(e,i))),t===1))throw n=wo,An(e,0),Xt(e,r),ze(e,ce()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:Sn(e,Ve,St);break;case 3:if(Xt(e,r),(r&130023424)===r&&(t=Eu+500-ce(),10<t)){if(Ci(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){_e(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Ha(Sn.bind(null,e,Ve,St),t);break}Sn(e,Ve,St);break;case 4:if(Xt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-ut(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=ce()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*J0(r/1960))-r,10<r){e.timeoutHandle=Ha(Sn.bind(null,e,Ve,St),r);break}Sn(e,Ve,St);break;case 5:Sn(e,Ve,St);break;default:throw Error(R(329))}}}return ze(e,ce()),e.callbackNode===n?qh.bind(null,e):null}function dl(e,t){var n=Yr;return e.current.memoizedState.isDehydrated&&(An(e,t).flags|=256),e=$i(e,t),e!==2&&(t=Ve,Ve=n,t!==null&&fl(t)),e}function fl(e){Ve===null?Ve=e:Ve.push.apply(Ve,e)}function e1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!dt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Xt(e,t){for(t&=~Cu,t&=~ds,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ut(t),r=1<<n;e[n]=-1,t&=~r}}function gd(e){if(H&6)throw Error(R(327));ur();var t=Ci(e,0);if(!(t&1))return ze(e,ce()),null;var n=$i(e,t);if(e.tag!==0&&n===2){var r=ja(e);r!==0&&(t=r,n=dl(e,r))}if(n===1)throw n=wo,An(e,0),Xt(e,t),ze(e,ce()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Sn(e,Ve,St),ze(e,ce()),null}function ku(e,t){var n=H;H|=1;try{return e(t)}finally{H=n,H===0&&(vr=ce()+500,as&&mn())}}function Vn(e){qt!==null&&qt.tag===0&&!(H&6)&&ur();var t=H;H|=1;var n=nt.transition,r=G;try{if(nt.transition=null,G=1,e)return e()}finally{G=r,nt.transition=n,H=t,!(H&6)&&mn()}}function Ru(){Ue=qn.current,J(qn)}function An(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,M0(n)),fe!==null)for(n=fe.return;n!==null;){var r=n;switch(au(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ai();break;case 3:mr(),J(Ie),J(Ae),gu();break;case 5:mu(r);break;case 4:mr();break;case 13:J(re);break;case 19:J(re);break;case 10:du(r.type._context);break;case 22:case 23:Ru()}n=n.return}if(ye=e,fe=e=ln(e.current,null),Se=Ue=t,me=0,wo=null,Cu=ds=On=0,Ve=Yr=null,En!==null){for(t=0;t<En.length;t++)if(n=En[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}En=null}return e}function Jh(e,t){do{var n=fe;try{if(cu(),ui.current=Ii,ji){for(var r=ie.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}ji=!1}if(Dn=0,ve=he=ie=null,Gr=!1,go=0,Tu.current=null,n===null||n.return===null){me=1,wo=t,fe=null;break}e:{var i=e,s=n.return,a=n,l=t;if(t=Se,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,c=d.tag;if(!(d.mode&1)&&(c===0||c===11||c===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=rd(s);if(m!==null){m.flags&=-257,od(m,s,a,i,t),m.mode&1&&nd(i,u,t),t=m,l=u;var v=t.updateQueue;if(v===null){var y=new Set;y.add(l),t.updateQueue=y}else v.add(l);break e}else{if(!(t&1)){nd(i,u,t),bu();break e}l=Error(R(426))}}else if(ee&&a.mode&1){var x=rd(s);if(x!==null){!(x.flags&65536)&&(x.flags|=256),od(x,s,a,i,t),lu(gr(l,a));break e}}i=l=gr(l,a),me!==4&&(me=2),Yr===null?Yr=[i]:Yr.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=Vh(i,l,t);Yc(i,h);break e;case 1:a=l;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(sn===null||!sn.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=jh(i,a,t);Yc(i,S);break e}}i=i.return}while(i!==null)}nm(n)}catch(P){t=P,fe===n&&n!==null&&(fe=n=n.return);continue}break}while(!0)}function em(){var e=Fi.current;return Fi.current=Ii,e===null?Ii:e}function bu(){(me===0||me===3||me===2)&&(me=4),ye===null||!(On&268435455)&&!(ds&268435455)||Xt(ye,Se)}function $i(e,t){var n=H;H|=2;var r=em();(ye!==e||Se!==t)&&(St=null,An(e,t));do try{t1();break}catch(o){Jh(e,o)}while(!0);if(cu(),H=n,Fi.current=r,fe!==null)throw Error(R(261));return ye=null,Se=0,me}function t1(){for(;fe!==null;)tm(fe)}function n1(){for(;fe!==null&&!Ry();)tm(fe)}function tm(e){var t=om(e.alternate,e,Ue);e.memoizedProps=e.pendingProps,t===null?nm(e):fe=t,Tu.current=null}function nm(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Y0(n,t),n!==null){n.flags&=32767,fe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{me=6,fe=null;return}}else if(n=Q0(n,t,Ue),n!==null){fe=n;return}if(t=t.sibling,t!==null){fe=t;return}fe=t=e}while(t!==null);me===0&&(me=5)}function Sn(e,t,n){var r=G,o=nt.transition;try{nt.transition=null,G=1,r1(e,t,n,r)}finally{nt.transition=o,G=r}return null}function r1(e,t,n,r){do ur();while(qt!==null);if(H&6)throw Error(R(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(jy(e,i),e===ye&&(fe=ye=null,Se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Yo||(Yo=!0,im(Ti,function(){return ur(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=nt.transition,nt.transition=null;var s=G;G=1;var a=H;H|=4,Tu.current=null,Z0(e,n),Xh(n,e),T0(Ua),Ei=!!$a,Ua=$a=null,e.current=n,q0(n),by(),H=a,G=s,nt.transition=i}else e.current=n;if(Yo&&(Yo=!1,qt=e,Bi=o),i=e.pendingLanes,i===0&&(sn=null),_y(n.stateNode),ze(e,ce()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(zi)throw zi=!1,e=ul,ul=null,e;return Bi&1&&e.tag!==0&&ur(),i=e.pendingLanes,i&1?e===cl?Xr++:(Xr=0,cl=e):Xr=0,mn(),null}function ur(){if(qt!==null){var e=Op(Bi),t=nt.transition,n=G;try{if(nt.transition=null,G=16>e?16:e,qt===null)var r=!1;else{if(e=qt,qt=null,Bi=0,H&6)throw Error(R(331));var o=H;for(H|=4,N=e.current;N!==null;){var i=N,s=i.child;if(N.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(N=u;N!==null;){var d=N;switch(d.tag){case 0:case 11:case 15:Qr(8,d,i)}var c=d.child;if(c!==null)c.return=d,N=c;else for(;N!==null;){d=N;var f=d.sibling,m=d.return;if(Gh(d),d===u){N=null;break}if(f!==null){f.return=m,N=f;break}N=m}}}var v=i.alternate;if(v!==null){var y=v.child;if(y!==null){v.child=null;do{var x=y.sibling;y.sibling=null,y=x}while(y!==null)}}N=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,N=s;else e:for(;N!==null;){if(i=N,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Qr(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,N=h;break e}N=i.return}}var p=e.current;for(N=p;N!==null;){s=N;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,N=g;else e:for(s=p;N!==null;){if(a=N,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:cs(9,a)}}catch(P){le(a,a.return,P)}if(a===s){N=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,N=S;break e}N=a.return}}if(H=o,mn(),gt&&typeof gt.onPostCommitFiberRoot=="function")try{gt.onPostCommitFiberRoot(ns,e)}catch{}r=!0}return r}finally{G=n,nt.transition=t}}return!1}function vd(e,t,n){t=gr(n,t),t=Vh(e,t,1),e=on(e,t,1),t=_e(),e!==null&&(ko(e,1,t),ze(e,t))}function le(e,t,n){if(e.tag===3)vd(e,e,n);else for(;t!==null;){if(t.tag===3){vd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(sn===null||!sn.has(r))){e=gr(n,e),e=jh(t,e,1),t=on(t,e,1),e=_e(),t!==null&&(ko(t,1,e),ze(t,e));break}}t=t.return}}function o1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=_e(),e.pingedLanes|=e.suspendedLanes&n,ye===e&&(Se&n)===n&&(me===4||me===3&&(Se&130023424)===Se&&500>ce()-Eu?An(e,0):Cu|=n),ze(e,t)}function rm(e,t){t===0&&(e.mode&1?(t=Fo,Fo<<=1,!(Fo&130023424)&&(Fo=4194304)):t=1);var n=_e();e=Dt(e,t),e!==null&&(ko(e,t,n),ze(e,n))}function i1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),rm(e,n)}function s1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),rm(e,n)}var om;om=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ie.current)je=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return je=!1,G0(e,t,n);je=!!(e.flags&131072)}else je=!1,ee&&t.flags&1048576&&ah(t,Li,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;di(e,t),e=t.pendingProps;var o=fr(t,Ae.current);lr(t,n),o=yu(null,t,r,e,o,n);var i=wu();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Fe(r)?(i=!0,Mi(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,pu(t),o.updater=ls,t.stateNode=o,o._reactInternals=t,qa(t,r,e,n),t=tl(null,t,r,!0,i,n)):(t.tag=0,ee&&i&&su(t),Me(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(di(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=l1(r),e=st(r,e),o){case 0:t=el(null,t,r,e,n);break e;case 1:t=ad(null,t,r,e,n);break e;case 11:t=id(null,t,r,e,n);break e;case 14:t=sd(null,t,r,st(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),el(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),ad(e,t,r,o,n);case 3:e:{if(Bh(t),e===null)throw Error(R(387));r=t.pendingProps,i=t.memoizedState,o=i.element,dh(e,t),Oi(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=gr(Error(R(423)),t),t=ld(e,t,r,n,o);break e}else if(r!==o){o=gr(Error(R(424)),t),t=ld(e,t,r,n,o);break e}else for(He=rn(t.stateNode.containerInfo.firstChild),Ke=t,ee=!0,lt=null,n=mh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(pr(),r===o){t=Ot(e,t,n);break e}Me(e,t,r,n)}t=t.child}return t;case 5:return gh(t),e===null&&Ya(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Wa(r,o)?s=null:i!==null&&Wa(r,i)&&(t.flags|=32),zh(e,t),Me(e,t,s,n),t.child;case 6:return e===null&&Ya(t),null;case 13:return $h(e,t,n);case 4:return hu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=hr(t,null,r,n):Me(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),id(e,t,r,o,n);case 7:return Me(e,t,t.pendingProps,n),t.child;case 8:return Me(e,t,t.pendingProps.children,n),t.child;case 12:return Me(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,Y(Ni,r._currentValue),r._currentValue=s,i!==null)if(dt(i.value,s)){if(i.children===o.children&&!Ie.current){t=Ot(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=kt(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),Xa(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(R(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Xa(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Me(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,lr(t,n),o=rt(o),r=r(o),t.flags|=1,Me(e,t,r,n),t.child;case 14:return r=t.type,o=st(r,t.pendingProps),o=st(r.type,o),sd(e,t,r,o,n);case 15:return Ih(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),di(e,t),t.tag=1,Fe(r)?(e=!0,Mi(t)):e=!1,lr(t,n),ph(t,r,o),qa(t,r,o,n),tl(null,t,r,!0,e,n);case 19:return Uh(e,t,n);case 22:return Fh(e,t,n)}throw Error(R(156,t.tag))};function im(e,t){return _p(e,t)}function a1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function tt(e,t,n,r){return new a1(e,t,n,r)}function Au(e){return e=e.prototype,!(!e||!e.isReactComponent)}function l1(e){if(typeof e=="function")return Au(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ql)return 11;if(e===Yl)return 14}return 2}function ln(e,t){var n=e.alternate;return n===null?(n=tt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function hi(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Au(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case $n:return Mn(n.children,o,i,t);case Gl:s=8,o|=8;break;case Pa:return e=tt(12,n,t,o|2),e.elementType=Pa,e.lanes=i,e;case Ta:return e=tt(13,n,t,o),e.elementType=Ta,e.lanes=i,e;case Ca:return e=tt(19,n,t,o),e.elementType=Ca,e.lanes=i,e;case hp:return fs(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case fp:s=10;break e;case pp:s=9;break e;case Ql:s=11;break e;case Yl:s=14;break e;case Gt:s=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=tt(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Mn(e,t,n,r){return e=tt(7,e,r,t),e.lanes=n,e}function fs(e,t,n,r){return e=tt(22,e,r,t),e.elementType=hp,e.lanes=n,e.stateNode={isHidden:!1},e}function na(e,t,n){return e=tt(6,e,null,t),e.lanes=n,e}function ra(e,t,n){return t=tt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function u1(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=js(0),this.expirationTimes=js(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=js(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Mu(e,t,n,r,o,i,s,a,l){return e=new u1(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=tt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},pu(i),e}function c1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Bn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function sm(e){if(!e)return cn;e=e._reactInternals;e:{if(Fn(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Fe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(Fe(n))return ih(e,n,t)}return t}function am(e,t,n,r,o,i,s,a,l){return e=Mu(n,r,!0,e,o,i,s,a,l),e.context=sm(null),n=e.current,r=_e(),o=an(n),i=kt(r,o),i.callback=t??null,on(n,i,o),e.current.lanes=o,ko(e,o,r),ze(e,r),e}function ps(e,t,n,r){var o=t.current,i=_e(),s=an(o);return n=sm(n),t.context===null?t.context=n:t.pendingContext=n,t=kt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=on(o,t,s),e!==null&&(ct(e,o,s,i),li(e,o,s)),s}function Ui(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function yd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function _u(e,t){yd(e,t),(e=e.alternate)&&yd(e,t)}function d1(){return null}var lm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Lu(e){this._internalRoot=e}hs.prototype.render=Lu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));ps(e,t,null,null)};hs.prototype.unmount=Lu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Vn(function(){ps(null,e,null,null)}),t[Nt]=null}};function hs(e){this._internalRoot=e}hs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ip();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Yt.length&&t!==0&&t<Yt[n].priority;n++);Yt.splice(n,0,e),n===0&&zp(e)}};function Nu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ms(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function wd(){}function f1(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Ui(s);i.call(u)}}var s=am(t,r,e,0,null,!1,!1,"",wd);return e._reactRootContainer=s,e[Nt]=s.current,co(e.nodeType===8?e.parentNode:e),Vn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Ui(l);a.call(u)}}var l=Mu(e,0,!1,null,null,!1,!1,"",wd);return e._reactRootContainer=l,e[Nt]=l.current,co(e.nodeType===8?e.parentNode:e),Vn(function(){ps(t,l,n,r)}),l}function gs(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=Ui(s);a.call(l)}}ps(t,s,e,o)}else s=f1(n,t,e,o,r);return Ui(s)}Vp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Ir(t.pendingLanes);n!==0&&(ql(t,n|1),ze(t,ce()),!(H&6)&&(vr=ce()+500,mn()))}break;case 13:Vn(function(){var r=Dt(e,1);if(r!==null){var o=_e();ct(r,e,1,o)}}),_u(e,1)}};Jl=function(e){if(e.tag===13){var t=Dt(e,134217728);if(t!==null){var n=_e();ct(t,e,134217728,n)}_u(e,134217728)}};jp=function(e){if(e.tag===13){var t=an(e),n=Dt(e,t);if(n!==null){var r=_e();ct(n,e,t,r)}_u(e,t)}};Ip=function(){return G};Fp=function(e,t){var n=G;try{return G=e,t()}finally{G=n}};Da=function(e,t,n){switch(t){case"input":if(Ra(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=ss(r);if(!o)throw Error(R(90));gp(r),Ra(r,o)}}}break;case"textarea":yp(e,n);break;case"select":t=n.value,t!=null&&or(e,!!n.multiple,t,!1)}};Ep=ku;kp=Vn;var p1={usingClientEntryPoint:!1,Events:[bo,Kn,ss,Tp,Cp,ku]},Lr={findFiberByHostInstance:Cn,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},h1={bundleType:Lr.bundleType,version:Lr.version,rendererPackageName:Lr.rendererPackageName,rendererConfig:Lr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:It.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Ap(e),e===null?null:e.stateNode},findFiberByHostInstance:Lr.findFiberByHostInstance||d1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Xo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Xo.isDisabled&&Xo.supportsFiber)try{ns=Xo.inject(h1),gt=Xo}catch{}}Ye.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=p1;Ye.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Nu(t))throw Error(R(200));return c1(e,t,null,n)};Ye.createRoot=function(e,t){if(!Nu(e))throw Error(R(299));var n=!1,r="",o=lm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Mu(e,1,!1,null,null,n,!1,r,o),e[Nt]=t.current,co(e.nodeType===8?e.parentNode:e),new Lu(t)};Ye.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=Ap(t),e=e===null?null:e.stateNode,e};Ye.flushSync=function(e){return Vn(e)};Ye.hydrate=function(e,t,n){if(!ms(t))throw Error(R(200));return gs(null,e,t,!0,n)};Ye.hydrateRoot=function(e,t,n){if(!Nu(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=lm;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=am(t,null,e,1,n??null,o,!1,i,s),e[Nt]=t.current,co(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new hs(t)};Ye.render=function(e,t,n){if(!ms(t))throw Error(R(200));return gs(null,e,t,!1,n)};Ye.unmountComponentAtNode=function(e){if(!ms(e))throw Error(R(40));return e._reactRootContainer?(Vn(function(){gs(null,null,e,!1,function(){e._reactRootContainer=null,e[Nt]=null})}),!0):!1};Ye.unstable_batchedUpdates=ku;Ye.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ms(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return gs(e,t,n,!1,r)};Ye.version="18.2.0-next-9e3b772b8-20220608";function um(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(um)}catch(e){console.error(e)}}um(),ap.exports=Ye;var Du=ap.exports;const cm=Fl(Du);/**
 * @remix-run/router v1.15.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Wi(){return Wi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Wi.apply(this,arguments)}var Jt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Jt||(Jt={}));const xd="popstate";function m1(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:a}=r.location;return pl("",{pathname:i,search:s,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:fm(o)}return v1(t,n,null,e)}function Be(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function dm(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function g1(){return Math.random().toString(36).substr(2,8)}function Sd(e,t){return{usr:e.state,key:e.key,idx:t}}function pl(e,t,n,r){return n===void 0&&(n=null),Wi({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?vs(t):t,{state:n,key:t&&t.key||r||g1()})}function fm(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function vs(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function v1(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,a=Jt.Pop,l=null,u=d();u==null&&(u=0,s.replaceState(Wi({},s.state,{idx:u}),""));function d(){return(s.state||{idx:null}).idx}function c(){a=Jt.Pop;let x=d(),h=x==null?null:x-u;u=x,l&&l({action:a,location:y.location,delta:h})}function f(x,h){a=Jt.Push;let p=pl(y.location,x,h);n&&n(p,x),u=d()+1;let g=Sd(p,u),S=y.createHref(p);try{s.pushState(g,"",S)}catch(P){if(P instanceof DOMException&&P.name==="DataCloneError")throw P;o.location.assign(S)}i&&l&&l({action:a,location:y.location,delta:1})}function m(x,h){a=Jt.Replace;let p=pl(y.location,x,h);n&&n(p,x),u=d();let g=Sd(p,u),S=y.createHref(p);s.replaceState(g,"",S),i&&l&&l({action:a,location:y.location,delta:0})}function v(x){let h=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof x=="string"?x:fm(x);return p=p.replace(/ $/,"%20"),Be(h,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,h)}let y={get action(){return a},get location(){return e(o,s)},listen(x){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(xd,c),l=x,()=>{o.removeEventListener(xd,c),l=null}},createHref(x){return t(o,x)},createURL:v,encodeLocation(x){let h=v(x);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:f,replace:m,go(x){return s.go(x)}};return y}var Pd;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Pd||(Pd={}));function y1(e,t,n){n===void 0&&(n="/");let r=typeof t=="string"?vs(t):t,o=mm(r.pathname||"/",n);if(o==null)return null;let i=pm(e);w1(i);let s=null;for(let a=0;s==null&&a<i.length;++a){let l=_1(o);s=b1(i[a],l)}return s}function pm(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,a)=>{let l={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};l.relativePath.startsWith("/")&&(Be(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=cr([r,l.relativePath]),d=n.concat(l);i.children&&i.children.length>0&&(Be(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),pm(i.children,t,d,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:k1(u,i.index),routesMeta:d})};return e.forEach((i,s)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))o(i,s);else for(let l of hm(i.path))o(i,s,l)}),t}function hm(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=hm(r.join("/")),a=[];return a.push(...s.map(l=>l===""?i:[i,l].join("/"))),o&&a.push(...s),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function w1(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:R1(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const x1=/^:[\w-]+$/,S1=3,P1=2,T1=1,C1=10,E1=-2,Td=e=>e==="*";function k1(e,t){let n=e.split("/"),r=n.length;return n.some(Td)&&(r+=E1),t&&(r+=P1),n.filter(o=>!Td(o)).reduce((o,i)=>o+(x1.test(i)?S1:i===""?T1:C1),r)}function R1(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function b1(e,t){let{routesMeta:n}=e,r={},o="/",i=[];for(let s=0;s<n.length;++s){let a=n[s],l=s===n.length-1,u=o==="/"?t:t.slice(o.length)||"/",d=A1({path:a.relativePath,caseSensitive:a.caseSensitive,end:l},u);if(!d)return null;Object.assign(r,d.params);let c=a.route;i.push({params:r,pathname:cr([o,d.pathname]),pathnameBase:L1(cr([o,d.pathnameBase])),route:c}),d.pathnameBase!=="/"&&(o=cr([o,d.pathnameBase]))}return i}function A1(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=M1(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((u,d,c)=>{let{paramName:f,isOptional:m}=d;if(f==="*"){let y=a[c]||"";s=i.slice(0,i.length-y.length).replace(/(.)\/+$/,"$1")}const v=a[c];return m&&!v?u[f]=void 0:u[f]=(v||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function M1(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),dm(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function _1(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return dm(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function mm(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}const cr=e=>e.join("/").replace(/\/\/+/g,"/"),L1=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");function N1(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const gm=["post","put","patch","delete"];new Set(gm);const D1=["get",...gm];new Set(D1);/**
 * React Router v6.22.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Hi(){return Hi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Hi.apply(this,arguments)}const O1=w.createContext(null),V1=w.createContext(null),vm=w.createContext(null),ys=w.createContext(null),ws=w.createContext({outlet:null,matches:[],isDataRoute:!1}),ym=w.createContext(null);function Ou(){return w.useContext(ys)!=null}function j1(){return Ou()||Be(!1),w.useContext(ys).location}function I1(e,t){return F1(e,t)}function F1(e,t,n,r){Ou()||Be(!1);let{navigator:o}=w.useContext(vm),{matches:i}=w.useContext(ws),s=i[i.length-1],a=s?s.params:{};s&&s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let u=j1(),d;if(t){var c;let x=typeof t=="string"?vs(t):t;l==="/"||(c=x.pathname)!=null&&c.startsWith(l)||Be(!1),d=x}else d=u;let f=d.pathname||"/",m=f;if(l!=="/"){let x=l.replace(/^\//,"").split("/");m="/"+f.replace(/^\//,"").split("/").slice(x.length).join("/")}let v=y1(e,{pathname:m}),y=W1(v&&v.map(x=>Object.assign({},x,{params:Object.assign({},a,x.params),pathname:cr([l,o.encodeLocation?o.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?l:cr([l,o.encodeLocation?o.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),i,n,r);return t&&y?w.createElement(ys.Provider,{value:{location:Hi({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Jt.Pop}},y):y}function z1(){let e=Q1(),t=N1(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},t),n?w.createElement("pre",{style:o},n):null,null)}const B1=w.createElement(z1,null);class $1 extends w.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?w.createElement(ws.Provider,{value:this.props.routeContext},w.createElement(ym.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function U1(e){let{routeContext:t,match:n,children:r}=e,o=w.useContext(O1);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),w.createElement(ws.Provider,{value:t},r)}function W1(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if((i=n)!=null&&i.errors)e=n.matches;else return null}let s=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let d=s.findIndex(c=>c.route.id&&(a==null?void 0:a[c.route.id]));d>=0||Be(!1),s=s.slice(0,Math.min(s.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<s.length;d++){let c=s[d];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(u=d),c.route.id){let{loaderData:f,errors:m}=n,v=c.route.loader&&f[c.route.id]===void 0&&(!m||m[c.route.id]===void 0);if(c.route.lazy||v){l=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((d,c,f)=>{let m,v=!1,y=null,x=null;n&&(m=a&&c.route.id?a[c.route.id]:void 0,y=c.route.errorElement||B1,l&&(u<0&&f===0?(Y1("route-fallback",!1),v=!0,x=null):u===f&&(v=!0,x=c.route.hydrateFallbackElement||null)));let h=t.concat(s.slice(0,f+1)),p=()=>{let g;return m?g=y:v?g=x:c.route.Component?g=w.createElement(c.route.Component,null):c.route.element?g=c.route.element:g=d,w.createElement(U1,{match:c,routeContext:{outlet:d,matches:h,isDataRoute:n!=null},children:g})};return n&&(c.route.ErrorBoundary||c.route.errorElement||f===0)?w.createElement($1,{location:n.location,revalidation:n.revalidation,component:y,error:m,children:p(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):p()},null)}var hl=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(hl||{});function H1(e){let t=w.useContext(V1);return t||Be(!1),t}function K1(e){let t=w.useContext(ws);return t||Be(!1),t}function G1(e){let t=K1(),n=t.matches[t.matches.length-1];return n.route.id||Be(!1),n.route.id}function Q1(){var e;let t=w.useContext(ym),n=H1(hl.UseRouteError),r=G1(hl.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}const Cd={};function Y1(e,t,n){!t&&!Cd[e]&&(Cd[e]=!0)}function ml(e){Be(!1)}function X1(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Jt.Pop,navigator:i,static:s=!1,future:a}=e;Ou()&&Be(!1);let l=t.replace(/^\/*/,"/"),u=w.useMemo(()=>({basename:l,navigator:i,static:s,future:Hi({v7_relativeSplatPath:!1},a)}),[l,a,i,s]);typeof r=="string"&&(r=vs(r));let{pathname:d="/",search:c="",hash:f="",state:m=null,key:v="default"}=r,y=w.useMemo(()=>{let x=mm(d,l);return x==null?null:{location:{pathname:x,search:c,hash:f,state:m,key:v},navigationType:o}},[l,d,c,f,m,v,o]);return y==null?null:w.createElement(vm.Provider,{value:u},w.createElement(ys.Provider,{children:n,value:y}))}function Z1(e){let{children:t,location:n}=e;return I1(gl(t),n)}new Promise(()=>{});function gl(e,t){t===void 0&&(t=[]);let n=[];return w.Children.forEach(e,(r,o)=>{if(!w.isValidElement(r))return;let i=[...t,o];if(r.type===w.Fragment){n.push.apply(n,gl(r.props.children,i));return}r.type!==ml&&Be(!1),!r.props.index||!r.props.children||Be(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=gl(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.22.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const q1="6";try{window.__reactRouterVersion=q1}catch{}const J1="startTransition",Ed=iy[J1];function ew(e){let{basename:t,children:n,future:r,window:o}=e,i=w.useRef();i.current==null&&(i.current=m1({window:o,v5Compat:!0}));let s=i.current,[a,l]=w.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},d=w.useCallback(c=>{u&&Ed?Ed(()=>l(c)):l(c)},[l,u]);return w.useLayoutEffect(()=>s.listen(d),[s,d]),w.createElement(X1,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:s,future:r})}var kd;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(kd||(kd={}));var Rd;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Rd||(Rd={}));const tw="modulepreload",nw=function(e){return"/"+e},bd={},rw=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){const i=document.getElementsByTagName("link");o=Promise.all(n.map(s=>{if(s=nw(s),s in bd)return;bd[s]=!0;const a=s.endsWith(".css"),l=a?'[rel="stylesheet"]':"";if(!!r)for(let c=i.length-1;c>=0;c--){const f=i[c];if(f.href===s&&(!a||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${l}`))return;const d=document.createElement("link");if(d.rel=a?"stylesheet":tw,a||(d.as="script",d.crossOrigin=""),d.href=s,document.head.appendChild(d),a)return new Promise((c,f)=>{d.addEventListener("load",c),d.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${s}`)))})}))}return o.then(()=>t()).catch(i=>{const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=i,window.dispatchEvent(s),!s.defaultPrevented)throw i})};function ow(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,o)=>o==="create"?e:(t.has(o)||t.set(o,e(o)),t.get(o))})}function xo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const vl=e=>Array.isArray(e);function wm(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function So(e){return typeof e=="string"||Array.isArray(e)}function Ad(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Vu(e,t,n,r){if(typeof t=="function"){const[o,i]=Ad(r);t=t(n!==void 0?n:e.custom,o,i)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,i]=Ad(r);t=t(n!==void 0?n:e.custom,o,i)}return t}function xs(e,t,n){const r=e.getProps();return Vu(r,t,n!==void 0?n:r.custom,e)}const ju=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Iu=["initial",...ju],Mo=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gn=new Set(Mo),Rt=e=>e*1e3,bt=e=>e/1e3,iw={type:"spring",stiffness:500,damping:25,restSpeed:10},sw=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),aw={type:"keyframes",duration:.8},lw={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},uw=(e,{keyframes:t})=>t.length>2?aw:gn.has(e)?e.startsWith("scale")?sw(t[1]):iw:lw;function cw({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:i,repeatType:s,repeatDelay:a,from:l,elapsed:u,...d}){return!!Object.keys(d).length}function Fu(e,t){return e[t]||e.default||e}const dw={skipAnimations:!1,useManualTiming:!1},fw=e=>e!==null;function Ss(e,{repeat:t,repeatType:n="loop"},r){const o=e.filter(fw),i=t&&n!=="loop"&&t%2===1?0:o.length-1;return!i||r===void 0?o[i]:r}const be=e=>e;function pw(e){let t=new Set,n=new Set,r=!1,o=!1;const i=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1};function a(u){i.has(u)&&(l.schedule(u),e()),u(s)}const l={schedule:(u,d=!1,c=!1)=>{const m=c&&r?t:n;return d&&i.add(u),m.has(u)||m.add(u),u},cancel:u=>{n.delete(u),i.delete(u)},process:u=>{if(s=u,r){o=!0;return}r=!0,[t,n]=[n,t],n.clear(),t.forEach(a),r=!1,o&&(o=!1,l.process(u))}};return l}const Zo=["read","resolveKeyframes","update","preRender","render","postRender"],hw=40;function xm(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,s=Zo.reduce((h,p)=>(h[p]=pw(i),h),{}),{read:a,resolveKeyframes:l,update:u,preRender:d,render:c,postRender:f}=s,m=()=>{const h=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(h-o.timestamp,hw),1),o.timestamp=h,o.isProcessing=!0,a.process(o),l.process(o),u.process(o),d.process(o),c.process(o),f.process(o),o.isProcessing=!1,n&&t&&(r=!1,e(m))},v=()=>{n=!0,r=!0,o.isProcessing||e(m)};return{schedule:Zo.reduce((h,p)=>{const g=s[p];return h[p]=(S,P=!1,C=!1)=>(n||v(),g.schedule(S,P,C)),h},{}),cancel:h=>{for(let p=0;p<Zo.length;p++)s[Zo[p]].cancel(h)},state:o,steps:s}}const{schedule:K,cancel:Vt,state:xe,steps:oa}=xm(typeof requestAnimationFrame<"u"?requestAnimationFrame:be,!0),Sm=e=>/^0[^.\s]+$/u.test(e);function mw(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Sm(e):!0}let Pm=be;const Tm=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Cm=e=>t=>typeof t=="string"&&t.startsWith(e),Em=Cm("--"),gw=Cm("var(--"),zu=e=>gw(e)?vw.test(e.split("/*")[0].trim()):!1,vw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,yw=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ww(e){const t=yw.exec(e);if(!t)return[,];const[,n,r,o]=t;return[`--${n??r}`,o]}function km(e,t,n=1){const[r,o]=ww(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const s=i.trim();return Tm(s)?parseFloat(s):s}return zu(o)?km(o,t,n+1):o}const dn=(e,t,n)=>n>t?t:n<e?e:n,Tr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Zr={...Tr,transform:e=>dn(0,1,e)},qo={...Tr,default:1},qr=e=>Math.round(e*1e5)/1e5,Bu=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,xw=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,Sw=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu;function _o(e){return typeof e=="string"}function Pw(e){return e==null}const Lo=e=>({test:t=>_o(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Kt=Lo("deg"),yt=Lo("%"),j=Lo("px"),Tw=Lo("vh"),Cw=Lo("vw"),Md={...yt,parse:e=>yt.parse(e)/100,transform:e=>yt.transform(e*100)},Ew=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),_d=e=>e===Tr||e===j,Ld=(e,t)=>parseFloat(e.split(", ")[t]),Nd=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const o=r.match(/^matrix3d\((.+)\)$/u);if(o)return Ld(o[1],t);{const i=r.match(/^matrix\((.+)\)$/u);return i?Ld(i[1],e):0}},kw=new Set(["x","y","z"]),Rw=Mo.filter(e=>!kw.has(e));function bw(e){const t=[];return Rw.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const yr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Nd(4,13),y:Nd(5,14)};yr.translateX=yr.x;yr.translateY=yr.y;const Rm=e=>t=>t.test(e),Aw={test:e=>e==="auto",parse:e=>e},bm=[Tr,j,yt,Kt,Cw,Tw,Aw],Dd=e=>bm.find(Rm(e)),_n=new Set;let yl=!1,wl=!1;function Am(){if(wl){const e=Array.from(_n).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const o=bw(r);o.length&&(n.set(r,o),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const o=n.get(r);o&&o.forEach(([i,s])=>{var a;(a=r.getValue(i))===null||a===void 0||a.set(s)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}wl=!1,yl=!1,_n.forEach(e=>e.complete()),_n.clear()}function Mm(){_n.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(wl=!0)})}function Mw(){Mm(),Am()}class $u{constructor(t,n,r,o,i,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=o,this.element=i,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(_n.add(this),yl||(yl=!0,K.read(Mm),K.resolveKeyframes(Am))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:o}=this;for(let i=0;i<t.length;i++)if(t[i]===null)if(i===0){const s=o==null?void 0:o.get(),a=t[t.length-1];if(s!==void 0)t[0]=s;else if(r&&n){const l=r.readValue(n,a);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=a),o&&s===void 0&&o.set(t[0])}else t[i]=t[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),_n.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,_n.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Uu=(e,t)=>n=>!!(_o(n)&&Sw.test(n)&&n.startsWith(e)||t&&!Pw(n)&&Object.prototype.hasOwnProperty.call(n,t)),_m=(e,t,n)=>r=>{if(!_o(r))return r;const[o,i,s,a]=r.match(Bu);return{[e]:parseFloat(o),[t]:parseFloat(i),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},_w=e=>dn(0,255,e),ia={...Tr,transform:e=>Math.round(_w(e))},Rn={test:Uu("rgb","red"),parse:_m("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+ia.transform(e)+", "+ia.transform(t)+", "+ia.transform(n)+", "+qr(Zr.transform(r))+")"};function Lw(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const xl={test:Uu("#"),parse:Lw,transform:Rn.transform},Jn={test:Uu("hsl","hue"),parse:_m("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+yt.transform(qr(t))+", "+yt.transform(qr(n))+", "+qr(Zr.transform(r))+")"},Ee={test:e=>Rn.test(e)||xl.test(e)||Jn.test(e),parse:e=>Rn.test(e)?Rn.parse(e):Jn.test(e)?Jn.parse(e):xl.parse(e),transform:e=>_o(e)?e:e.hasOwnProperty("red")?Rn.transform(e):Jn.transform(e)};function Nw(e){var t,n;return isNaN(e)&&_o(e)&&(((t=e.match(Bu))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(xw))===null||n===void 0?void 0:n.length)||0)>0}const Lm="number",Nm="color",Dw="var",Ow="var(",Od="${}",Vw=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Po(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},o=[];let i=0;const a=t.replace(Vw,l=>(Ee.test(l)?(r.color.push(i),o.push(Nm),n.push(Ee.parse(l))):l.startsWith(Ow)?(r.var.push(i),o.push(Dw),n.push(l)):(r.number.push(i),o.push(Lm),n.push(parseFloat(l))),++i,Od)).split(Od);return{values:n,split:a,indexes:r,types:o}}function Dm(e){return Po(e).values}function Om(e){const{split:t,types:n}=Po(e),r=t.length;return o=>{let i="";for(let s=0;s<r;s++)if(i+=t[s],o[s]!==void 0){const a=n[s];a===Lm?i+=qr(o[s]):a===Nm?i+=Ee.transform(o[s]):i+=o[s]}return i}}const jw=e=>typeof e=="number"?0:e;function Iw(e){const t=Dm(e);return Om(e)(t.map(jw))}const fn={test:Nw,parse:Dm,createTransformer:Om,getAnimatableNone:Iw},Fw=new Set(["brightness","contrast","saturate","opacity"]);function zw(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Bu)||[];if(!r)return e;const o=n.replace(r,"");let i=Fw.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+o+")"}const Bw=/\b([a-z-]*)\(.*?\)/gu,Sl={...fn,getAnimatableNone:e=>{const t=e.match(Bw);return t?t.map(zw).join(" "):e}},Vd={...Tr,transform:Math.round},Wu={borderWidth:j,borderTopWidth:j,borderRightWidth:j,borderBottomWidth:j,borderLeftWidth:j,borderRadius:j,radius:j,borderTopLeftRadius:j,borderTopRightRadius:j,borderBottomRightRadius:j,borderBottomLeftRadius:j,width:j,maxWidth:j,height:j,maxHeight:j,size:j,top:j,right:j,bottom:j,left:j,padding:j,paddingTop:j,paddingRight:j,paddingBottom:j,paddingLeft:j,margin:j,marginTop:j,marginRight:j,marginBottom:j,marginLeft:j,rotate:Kt,rotateX:Kt,rotateY:Kt,rotateZ:Kt,scale:qo,scaleX:qo,scaleY:qo,scaleZ:qo,skew:Kt,skewX:Kt,skewY:Kt,distance:j,translateX:j,translateY:j,translateZ:j,x:j,y:j,z:j,perspective:j,transformPerspective:j,opacity:Zr,originX:Md,originY:Md,originZ:j,zIndex:Vd,backgroundPositionX:j,backgroundPositionY:j,fillOpacity:Zr,strokeOpacity:Zr,numOctaves:Vd},$w={...Wu,color:Ee,backgroundColor:Ee,outlineColor:Ee,fill:Ee,stroke:Ee,borderColor:Ee,borderTopColor:Ee,borderRightColor:Ee,borderBottomColor:Ee,borderLeftColor:Ee,filter:Sl,WebkitFilter:Sl},Hu=e=>$w[e];function Vm(e,t){let n=Hu(e);return n!==Sl&&(n=fn),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Uw=new Set(["auto","none","0"]);function Ww(e,t,n){let r=0,o;for(;r<e.length&&!o;){const i=e[r];typeof i=="string"&&!Uw.has(i)&&Po(i).values.length&&(o=e[r]),r++}if(o&&n)for(const i of t)e[i]=Vm(n,o)}class jm extends $u{constructor(t,n,r,o,i){super(t,n,r,o,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),zu(u))){const d=km(u,n.current);d!==void 0&&(t[l]=d),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!Ew.has(r)||t.length!==2)return;const[o,i]=t,s=Dd(o),a=Dd(i);if(s!==a)if(_d(s)&&_d(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let o=0;o<t.length;o++)mw(t[o])&&r.push(o);r.length&&Ww(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=yr[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const o=n[n.length-1];o!==void 0&&t.getValue(r,o).jump(o,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:o}=this;if(!n||!n.current)return;const i=n.getValue(r);i&&i.jump(this.measuredOrigin,!1);const s=o.length-1,a=o[s];o[s]=yr[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}function Im(e){let t;return()=>(t===void 0&&(t=e()),t)}let mi;function Hw(){mi=void 0}const At={now:()=>(mi===void 0&&At.set(xe.isProcessing||dw.useManualTiming?xe.timestamp:performance.now()),mi),set:e=>{mi=e,queueMicrotask(Hw)}},jd=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(fn.test(e)||e==="0")&&!e.startsWith("url("));function Kw(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function Gw(e,t,n,r){const o=e[0];if(o===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],s=jd(o,t),a=jd(i,t);return!s||!a?!1:Kw(e)||n==="spring"&&r}const Qw=40;class Fm{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:o=0,repeatDelay:i=0,repeatType:s="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=At.now(),this.options={autoplay:t,delay:n,type:r,repeat:o,repeatDelay:i,repeatType:s,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Qw?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Mw(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=At.now(),this.hasAttemptedResolve=!0;const{name:r,type:o,velocity:i,delay:s,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!Gw(t,r,o,i))if(s)this.options.duration=0;else{l==null||l(Ss(t,this.options,n)),a==null||a(),this.resolveFinishedPromise();return}const d=this.initPlayback(t,n);d!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...d},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}function zm(e,t){return t?e*(1e3/t):0}const Yw=5;function Bm(e,t,n){const r=Math.max(t-Yw,0);return zm(n-e(r),t-r)}const sa=.001,Xw=.01,Zw=10,qw=.05,Jw=1;function ex({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let o,i,s=1-t;s=dn(qw,Jw,s),e=dn(Xw,Zw,bt(e)),s<1?(o=u=>{const d=u*s,c=d*e,f=d-n,m=Pl(u,s),v=Math.exp(-c);return sa-f/m*v},i=u=>{const c=u*s*e,f=c*n+n,m=Math.pow(s,2)*Math.pow(u,2)*e,v=Math.exp(-c),y=Pl(Math.pow(u,2),s);return(-o(u)+sa>0?-1:1)*((f-m)*v)/y}):(o=u=>{const d=Math.exp(-u*e),c=(u-n)*e+1;return-sa+d*c},i=u=>{const d=Math.exp(-u*e),c=(n-u)*(e*e);return d*c});const a=5/e,l=nx(o,i,a);if(e=Rt(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const tx=12;function nx(e,t,n){let r=n;for(let o=1;o<tx;o++)r=r-e(r)/t(r);return r}function Pl(e,t){return e*Math.sqrt(1-t*t)}const rx=["duration","bounce"],ox=["stiffness","damping","mass"];function Id(e,t){return t.some(n=>e[n]!==void 0)}function ix(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Id(e,ox)&&Id(e,rx)){const n=ex(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function $m({keyframes:e,restDelta:t,restSpeed:n,...r}){const o=e[0],i=e[e.length-1],s={done:!1,value:o},{stiffness:a,damping:l,mass:u,duration:d,velocity:c,isResolvedFromDuration:f}=ix({...r,velocity:-bt(r.velocity||0)}),m=c||0,v=l/(2*Math.sqrt(a*u)),y=i-o,x=bt(Math.sqrt(a/u)),h=Math.abs(y)<5;n||(n=h?.01:2),t||(t=h?.005:.5);let p;if(v<1){const g=Pl(x,v);p=S=>{const P=Math.exp(-v*x*S);return i-P*((m+v*x*y)/g*Math.sin(g*S)+y*Math.cos(g*S))}}else if(v===1)p=g=>i-Math.exp(-x*g)*(y+(m+x*y)*g);else{const g=x*Math.sqrt(v*v-1);p=S=>{const P=Math.exp(-v*x*S),C=Math.min(g*S,300);return i-P*((m+v*x*y)*Math.sinh(C)+g*y*Math.cosh(C))/g}}return{calculatedDuration:f&&d||null,next:g=>{const S=p(g);if(f)s.done=g>=d;else{let P=0;v<1&&(P=g===0?Rt(m):Bm(p,g,S));const C=Math.abs(P)<=n,k=Math.abs(i-S)<=t;s.done=C&&k}return s.value=s.done?i:S,s}}}function Fd({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:i=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:d}){const c=e[0],f={done:!1,value:c},m=T=>a!==void 0&&T<a||l!==void 0&&T>l,v=T=>a===void 0?l:l===void 0||Math.abs(a-T)<Math.abs(l-T)?a:l;let y=n*t;const x=c+y,h=s===void 0?x:s(x);h!==x&&(y=h-c);const p=T=>-y*Math.exp(-T/r),g=T=>h+p(T),S=T=>{const D=p(T),L=g(T);f.done=Math.abs(D)<=u,f.value=f.done?h:L};let P,C;const k=T=>{m(f.value)&&(P=T,C=$m({keyframes:[f.value,v(f.value)],velocity:Bm(g,T,f.value),damping:o,stiffness:i,restDelta:u,restSpeed:d}))};return k(0),{calculatedDuration:null,next:T=>{let D=!1;return!C&&P===void 0&&(D=!0,S(T),k(T)),P!==void 0&&T>=P?C.next(T-P):(!D&&S(T),f)}}}const Um=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,sx=1e-7,ax=12;function lx(e,t,n,r,o){let i,s,a=0;do s=t+(n-t)/2,i=Um(s,r,o)-e,i>0?n=s:t=s;while(Math.abs(i)>sx&&++a<ax);return s}function No(e,t,n,r){if(e===t&&n===r)return be;const o=i=>lx(i,0,1,e,n);return i=>i===0||i===1?i:Um(o(i),t,r)}const ux=No(.42,0,1,1),cx=No(0,0,.58,1),Wm=No(.42,0,.58,1),dx=e=>Array.isArray(e)&&typeof e[0]!="number",Hm=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Km=e=>t=>1-e(1-t),Ku=e=>1-Math.sin(Math.acos(e)),Gm=Km(Ku),fx=Hm(Ku),Qm=No(.33,1.53,.69,.99),Gu=Km(Qm),px=Hm(Gu),hx=e=>(e*=2)<1?.5*Gu(e):.5*(2-Math.pow(2,-10*(e-1))),mx={linear:be,easeIn:ux,easeInOut:Wm,easeOut:cx,circIn:Ku,circInOut:fx,circOut:Gm,backIn:Gu,backInOut:px,backOut:Qm,anticipate:hx},zd=e=>{if(Array.isArray(e)){Pm(e.length===4);const[t,n,r,o]=e;return No(t,n,r,o)}else if(typeof e=="string")return mx[e];return e},gx=(e,t)=>n=>t(e(n)),Mt=(...e)=>e.reduce(gx),To=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},oe=(e,t,n)=>e+(t-e)*n;function aa(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function vx({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,i=0,s=0;if(!t)o=i=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;o=aa(l,a,e+1/3),i=aa(l,a,e),s=aa(l,a,e-1/3)}return{red:Math.round(o*255),green:Math.round(i*255),blue:Math.round(s*255),alpha:r}}function Ki(e,t){return n=>n>0?t:e}const la=(e,t,n)=>{const r=e*e,o=n*(t*t-r)+r;return o<0?0:Math.sqrt(o)},yx=[xl,Rn,Jn],wx=e=>yx.find(t=>t.test(e));function Bd(e){const t=wx(e);if(!t)return!1;let n=t.parse(e);return t===Jn&&(n=vx(n)),n}const $d=(e,t)=>{const n=Bd(e),r=Bd(t);if(!n||!r)return Ki(e,t);const o={...n};return i=>(o.red=la(n.red,r.red,i),o.green=la(n.green,r.green,i),o.blue=la(n.blue,r.blue,i),o.alpha=oe(n.alpha,r.alpha,i),Rn.transform(o))},Tl=new Set(["none","hidden"]);function xx(e,t){return Tl.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function Sx(e,t){return n=>oe(e,t,n)}function Qu(e){return typeof e=="number"?Sx:typeof e=="string"?zu(e)?Ki:Ee.test(e)?$d:Cx:Array.isArray(e)?Ym:typeof e=="object"?Ee.test(e)?$d:Px:Ki}function Ym(e,t){const n=[...e],r=n.length,o=e.map((i,s)=>Qu(i)(i,t[s]));return i=>{for(let s=0;s<r;s++)n[s]=o[s](i);return n}}function Px(e,t){const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=Qu(e[o])(e[o],t[o]));return o=>{for(const i in r)n[i]=r[i](o);return n}}function Tx(e,t){var n;const r=[],o={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const s=t.types[i],a=e.indexes[s][o[s]],l=(n=e.values[a])!==null&&n!==void 0?n:0;r[i]=l,o[s]++}return r}const Cx=(e,t)=>{const n=fn.createTransformer(t),r=Po(e),o=Po(t);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?Tl.has(e)&&!o.values.length||Tl.has(t)&&!r.values.length?xx(e,t):Mt(Ym(Tx(r,o),o.values),n):Ki(e,t)};function Xm(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?oe(e,t,n):Qu(e)(e,t)}function Ex(e,t,n){const r=[],o=n||Xm,i=e.length-1;for(let s=0;s<i;s++){let a=o(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]||be:t;a=Mt(l,a)}r.push(a)}return r}function kx(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const i=e.length;if(Pm(i===t.length),i===1)return()=>t[0];if(i===2&&e[0]===e[1])return()=>t[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=Ex(t,r,o),a=s.length,l=u=>{let d=0;if(a>1)for(;d<e.length-2&&!(u<e[d+1]);d++);const c=To(e[d],e[d+1],u);return s[d](c)};return n?u=>l(dn(e[0],e[i-1],u)):l}function Rx(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=To(0,t,r);e.push(oe(n,1,o))}}function bx(e){const t=[0];return Rx(t,e.length-1),t}function Ax(e,t){return e.map(n=>n*t)}function Mx(e,t){return e.map(()=>t||Wm).splice(0,e.length-1)}function Gi({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=dx(r)?r.map(zd):zd(r),i={done:!1,value:t[0]},s=Ax(n&&n.length===t.length?n:bx(t),e),a=kx(s,t,{ease:Array.isArray(o)?o:Mx(t,o)});return{calculatedDuration:e,next:l=>(i.value=a(l),i.done=l>=e,i)}}const Ud=2e4;function _x(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Ud;)t+=n,r=e.next(t);return t>=Ud?1/0:t}const Lx=e=>{const t=({timestamp:n})=>e(n);return{start:()=>K.update(t,!0),stop:()=>Vt(t),now:()=>xe.isProcessing?xe.timestamp:At.now()}},Nx={decay:Fd,inertia:Fd,tween:Gi,keyframes:Gi,spring:$m},Dx=e=>e/100;class Ps extends Fm{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:r,element:o,keyframes:i}=this.options,s=(o==null?void 0:o.KeyframeResolver)||$u,a=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new s(i,a,n,r,o),this.resolver.scheduleResolve()}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:i,velocity:s=0}=this.options,a=Nx[n]||Gi;let l,u;a!==Gi&&typeof t[0]!="number"&&(l=Mt(Dx,Xm(t[0],t[1])),t=[0,100]);const d=a({...this.options,keyframes:t});i==="mirror"&&(u=a({...this.options,keyframes:[...t].reverse(),velocity:-s})),d.calculatedDuration===null&&(d.calculatedDuration=_x(d));const{calculatedDuration:c}=d,f=c+o,m=f*(r+1)-o;return{generator:d,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:c,resolvedDuration:f,totalDuration:m}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:T}=this.options;return{done:!0,value:T[T.length-1]}}const{finalKeyframe:o,generator:i,mirroredGenerator:s,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:d,resolvedDuration:c}=r;if(this.startTime===null)return i.next(0);const{delay:f,repeat:m,repeatType:v,repeatDelay:y,onUpdate:x}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-d/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const h=this.currentTime-f*(this.speed>=0?1:-1),p=this.speed>=0?h<0:h>d;this.currentTime=Math.max(h,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=d);let g=this.currentTime,S=i;if(m){const T=Math.min(this.currentTime,d)/c;let D=Math.floor(T),L=T%1;!L&&T>=1&&(L=1),L===1&&D--,D=Math.min(D,m+1),!!(D%2)&&(v==="reverse"?(L=1-L,y&&(L-=y/c)):v==="mirror"&&(S=s)),g=dn(0,1,L)*c}const P=p?{done:!1,value:l[0]}:S.next(g);a&&(P.value=a(P.value));let{done:C}=P;!p&&u!==null&&(C=this.speed>=0?this.currentTime>=d:this.currentTime<=0);const k=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&C);return k&&o!==void 0&&(P.value=Ss(l,this.options,o)),x&&x(P.value),k&&this.finish(),P}get duration(){const{resolved:t}=this;return t?bt(t.calculatedDuration):0}get time(){return bt(this.currentTime)}set time(t){t=Rt(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=bt(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=Lx,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(i=>this.tick(i))),n&&n();const o=this.driver.now();this.holdTime!==null?this.startTime=o-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=o):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}function BE(e){return new Ps(e)}const Zm=new Set(["opacity","clipPath","filter","transform"]),qm=e=>Array.isArray(e)&&typeof e[0]=="number";function Jm(e){return!!(!e||typeof e=="string"&&e in Yu||qm(e)||Array.isArray(e)&&e.every(Jm))}const zr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Yu={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:zr([0,.65,.55,1]),circOut:zr([.55,0,1,.45]),backIn:zr([.31,.01,.66,-.59]),backOut:zr([.33,1.53,.69,.99])};function Ox(e){return eg(e)||Yu.easeOut}function eg(e){if(e)return qm(e)?zr(e):Array.isArray(e)?e.map(Ox):Yu[e]}function Vx(e,t,n,{delay:r=0,duration:o=300,repeat:i=0,repeatType:s="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const d=eg(a);return Array.isArray(d)&&(u.easing=d),e.animate(u,{delay:r,duration:o,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:i+1,direction:s==="reverse"?"alternate":"normal"})}const jx=Im(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Qi=10,Ix=2e4;function Fx(e){return e.type==="spring"||!Jm(e.ease)}function zx(e,t){const n=new Ps({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const o=[];let i=0;for(;!r.done&&i<Ix;)r=n.sample(i),o.push(r.value),i+=Qi;return{times:void 0,keyframes:o,duration:i-Qi,ease:"linear"}}class Wd extends Fm{constructor(t){super(t);const{name:n,motionValue:r,element:o,keyframes:i}=this.options;this.resolver=new jm(i,(s,a)=>this.onKeyframesResolved(s,a),n,r,o),this.resolver.scheduleResolve()}initPlayback(t,n){var r;let{duration:o=300,times:i,ease:s,type:a,motionValue:l,name:u,startTime:d}=this.options;if(!(!((r=l.owner)===null||r===void 0)&&r.current))return!1;if(Fx(this.options)){const{onComplete:f,onUpdate:m,motionValue:v,element:y,...x}=this.options,h=zx(t,x);t=h.keyframes,t.length===1&&(t[1]=t[0]),o=h.duration,i=h.times,s=h.ease,a="keyframes"}const c=Vx(l.owner.current,u,t,{...this.options,duration:o,times:i,ease:s});return c.startTime=d??this.calcStartTime(),this.pendingTimeline?(c.timeline=this.pendingTimeline,this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:f}=this.options;l.set(Ss(t,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:o,times:i,type:a,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return bt(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return bt(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=Rt(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return be;const{animation:r}=n;r.timeline=t,r.onfinish=null}return be}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:o,type:i,ease:s,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:d,onComplete:c,element:f,...m}=this.options,v=new Ps({...m,keyframes:r,duration:o,type:i,ease:s,times:a,isGenerator:!0}),y=Rt(this.time);u.setWithVelocity(v.sample(y-Qi).value,v.sample(y).value,Qi)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:o,repeatType:i,damping:s,type:a}=t;return jx()&&r&&Zm.has(r)&&n&&n.owner&&n.owner.current instanceof HTMLElement&&!n.owner.getProps().onUpdate&&!o&&i!=="mirror"&&s!==0&&a!=="inertia"}}function Bx(e,t){let n;const r=()=>{const{currentTime:o}=t,s=(o===null?0:o.value)/100;n!==s&&e(s),n=s};return K.update(r,!0),()=>Vt(r)}const $x=Im(()=>window.ScrollTimeline!==void 0);class Ux{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}then(t,n){return Promise.all(this.animations).then(t).catch(n)}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t){const n=this.animations.map(r=>{if($x()&&r.attachTimeline)r.attachTimeline(t);else return r.pause(),Bx(o=>{r.time=r.duration*o},t)});return()=>{n.forEach((r,o)=>{r&&r(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}const Xu=(e,t,n,r={},o,i,s)=>a=>{const l=Fu(r,e)||{},u=l.delay||r.delay||0;let{elapsed:d=0}=r;d=d-Rt(u);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-d,onUpdate:m=>{t.set(m),l.onUpdate&&l.onUpdate(m)},onComplete:()=>{a(),l.onComplete&&l.onComplete(),s&&s()},onStop:s,name:e,motionValue:t,element:i?void 0:o};cw(l)||(c={...c,...uw(e,c)}),c.duration&&(c.duration=Rt(c.duration)),c.repeatDelay&&(c.repeatDelay=Rt(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let f=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(f=!0)),f&&!i&&t.get()!==void 0){const m=Ss(c.keyframes,l);if(m!==void 0)return K.update(()=>{c.onUpdate(m),c.onComplete()}),new Ux([])}return!i&&Wd.supports(c)?new Wd(c):new Ps(c)},Wx=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Hx=e=>vl(e)?e[e.length-1]||0:e;function Ts(e,t){e.indexOf(t)===-1&&e.push(t)}function Cs(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Zu{constructor(){this.subscriptions=[]}add(t){return Ts(this.subscriptions,t),()=>Cs(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let i=0;i<o;i++){const s=this.subscriptions[i];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Hd=30,Kx=e=>!isNaN(parseFloat(e));class tg{constructor(t,n={}){this.version="11.5.4",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,o=!0)=>{const i=At.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=At.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=Kx(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Zu);const r=this.events[t].add(n);return t==="change"?()=>{r(),K.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=At.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Hd)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Hd);return zm(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Co(e,t){return new tg(e,t)}function Gx(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Co(n))}function Qx(e,t){const n=xs(e,t);let{transitionEnd:r={},transition:o={},...i}=n||{};i={...i,...r};for(const s in i){const a=Hx(i[s]);Gx(e,s,a)}}const Es=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Yx="framerAppearId",ng="data-"+Es(Yx);function rg(e){return e.props[ng]}function og(e){if(gn.has(e))return"transform";if(Zm.has(e))return Es(e)}class Xx extends tg{constructor(){super(...arguments),this.output=[],this.counts=new Map}add(t){const n=og(t);if(!n)return;const r=this.counts.get(n)||0;this.counts.set(n,r+1),r===0&&(this.output.push(n),this.update());let o=!1;return()=>{if(o)return;o=!0;const i=this.counts.get(n)-1;this.counts.set(n,i),i===0&&(Cs(this.output,n),this.update())}}update(){this.set(this.output.length?this.output.join(", "):"auto")}}const Re=e=>!!(e&&e.getVelocity);function Zx(e){return!!(Re(e)&&e.add)}function Cl(e,t){var n;if(!e.applyWillChange)return;let r=e.getValue("willChange");if(!r&&!(!((n=e.props.style)===null||n===void 0)&&n.willChange)&&(r=new Xx("auto"),e.addValue("willChange",r)),Zx(r))return r.add(t)}function qx({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function ig(e,t,{delay:n=0,transitionOverride:r,type:o}={}){var i;let{transition:s=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(s=r);const u=[],d=o&&e.animationState&&e.animationState.getState()[o];for(const c in l){const f=e.getValue(c,(i=e.latestValues[c])!==null&&i!==void 0?i:null),m=l[c];if(m===void 0||d&&qx(d,c))continue;const v={delay:n,...Fu(s||{},c)};let y=!1;if(window.MotionHandoffAnimation){const h=rg(e);if(h){const p=window.MotionHandoffAnimation(h,c,K);p!==null&&(v.startTime=p,y=!0)}}f.start(Xu(c,f,m,e.shouldReduceMotion&&gn.has(c)?{type:!1}:v,e,y,Cl(e,c)));const x=f.animation;x&&u.push(x)}return a&&Promise.all(u).then(()=>{K.update(()=>{a&&Qx(e,a)})}),u}function El(e,t,n={}){var r;const o=xs(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=o||{};n.transitionOverride&&(i=n.transitionOverride);const s=o?()=>Promise.all(ig(e,o,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:d=0,staggerChildren:c,staggerDirection:f}=i;return Jx(e,t,d+u,c,f,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[u,d]=l==="beforeChildren"?[s,a]:[a,s];return u().then(()=>d())}else return Promise.all([s(),a(n.delay)])}function Jx(e,t,n=0,r=0,o=1,i){const s=[],a=(e.variantChildren.size-1)*r,l=o===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(eS).forEach((u,d)=>{u.notify("AnimationStart",t),s.push(El(u,t,{...i,delay:n+l(d)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function eS(e,t){return e.sortNodePosition(t)}function tS(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(i=>El(e,i,n));r=Promise.all(o)}else if(typeof t=="string")r=El(e,t,n);else{const o=typeof t=="function"?xs(e,t,n.custom):t;r=Promise.all(ig(e,o,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const nS=[...ju].reverse(),rS=ju.length;function oS(e){return t=>Promise.all(t.map(({animation:n,options:r})=>tS(e,n,r)))}function iS(e){let t=oS(e),n=Kd(),r=!0;const o=l=>(u,d)=>{var c;const f=xs(e,d,l==="exit"?(c=e.presenceContext)===null||c===void 0?void 0:c.custom:void 0);if(f){const{transition:m,transitionEnd:v,...y}=f;u={...u,...y,...v}}return u};function i(l){t=l(e)}function s(l){const u=e.getProps(),d=e.getVariantContext(!0)||{},c=[],f=new Set;let m={},v=1/0;for(let x=0;x<rS;x++){const h=nS[x],p=n[h],g=u[h]!==void 0?u[h]:d[h],S=So(g),P=h===l?p.isActive:null;P===!1&&(v=x);let C=g===d[h]&&g!==u[h]&&S;if(C&&r&&e.manuallyAnimateOnMount&&(C=!1),p.protectedKeys={...m},!p.isActive&&P===null||!g&&!p.prevProp||xo(g)||typeof g=="boolean")continue;let T=sS(p.prevProp,g)||h===l&&p.isActive&&!C&&S||x>v&&S,D=!1;const L=Array.isArray(g)?g:[g];let _=L.reduce(o(h),{});P===!1&&(_={});const{prevResolvedValues:E={}}=p,F={...E,..._},A=V=>{T=!0,f.has(V)&&(D=!0,f.delete(V)),p.needsAnimating[V]=!0;const B=e.getValue(V);B&&(B.liveStyle=!1)};for(const V in F){const B=_[V],X=E[V];if(m.hasOwnProperty(V))continue;let M=!1;vl(B)&&vl(X)?M=!wm(B,X):M=B!==X,M?B!=null?A(V):f.add(V):B!==void 0&&f.has(V)?A(V):p.protectedKeys[V]=!0}p.prevProp=g,p.prevResolvedValues=_,p.isActive&&(m={...m,..._}),r&&e.blockInitialAnimation&&(T=!1),T&&(!C||D)&&c.push(...L.map(V=>({animation:V,options:{type:h}})))}if(f.size){const x={};f.forEach(h=>{const p=e.getBaseTarget(h),g=e.getValue(h);g&&(g.liveStyle=!0),x[h]=p??null}),c.push({animation:x})}let y=!!c.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(y=!1),r=!1,y?t(c):Promise.resolve()}function a(l,u){var d;if(n[l].isActive===u)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(f=>{var m;return(m=f.animationState)===null||m===void 0?void 0:m.setActive(l,u)}),n[l].isActive=u;const c=s(l);for(const f in n)n[f].protectedKeys={};return c}return{animateChanges:s,setActive:a,setAnimateFunction:i,getState:()=>n,reset:()=>{n=Kd(),r=!0}}}function sS(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!wm(t,e):!1}function wn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Kd(){return{animate:wn(!0),whileInView:wn(),whileHover:wn(),whileTap:wn(),whileDrag:wn(),whileFocus:wn(),exit:wn()}}class vn{constructor(t){this.isMounted=!1,this.node=t}update(){}}class aS extends vn{constructor(t){super(t),t.animationState||(t.animationState=iS(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();xo(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let lS=0;class uS extends vn{constructor(){super(...arguments),this.id=lS++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const o=this.node.animationState.setActive("exit",!t);n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const cS={animation:{Feature:aS},exit:{Feature:uS}},sg=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function ks(e,t="page"){return{point:{x:e[`${t}X`],y:e[`${t}Y`]}}}const dS=e=>t=>sg(t)&&e(t,ks(t));function Et(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function _t(e,t,n,r){return Et(e,t,dS(n),r)}const Gd=(e,t)=>Math.abs(e-t);function fS(e,t){const n=Gd(e.x,t.x),r=Gd(e.y,t.y);return Math.sqrt(n**2+r**2)}class ag{constructor(t,n,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const c=ca(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,m=fS(c.offset,{x:0,y:0})>=3;if(!f&&!m)return;const{point:v}=c,{timestamp:y}=xe;this.history.push({...v,timestamp:y});const{onStart:x,onMove:h}=this.handlers;f||(x&&x(this.lastMoveEvent,c),this.startEvent=this.lastMoveEvent),h&&h(this.lastMoveEvent,c)},this.handlePointerMove=(c,f)=>{this.lastMoveEvent=c,this.lastMoveEventInfo=ua(f,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(c,f)=>{this.end();const{onEnd:m,onSessionEnd:v,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=ca(c.type==="pointercancel"?this.lastMoveEventInfo:ua(f,this.transformPagePoint),this.history);this.startEvent&&m&&m(c,x),v&&v(c,x)},!sg(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=o||window;const s=ks(t),a=ua(s,this.transformPagePoint),{point:l}=a,{timestamp:u}=xe;this.history=[{...l,timestamp:u}];const{onSessionStart:d}=n;d&&d(t,ca(a,this.history)),this.removeListeners=Mt(_t(this.contextWindow,"pointermove",this.handlePointerMove),_t(this.contextWindow,"pointerup",this.handlePointerUp),_t(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Vt(this.updatePoint)}}function ua(e,t){return t?{point:t(e.point)}:e}function Qd(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ca({point:e},t){return{point:e,delta:Qd(e,lg(t)),offset:Qd(e,pS(t)),velocity:hS(t,.1)}}function pS(e){return e[0]}function lg(e){return e[e.length-1]}function hS(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=lg(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>Rt(t)));)n--;if(!r)return{x:0,y:0};const i=bt(o.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const s={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function ug(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Yd=ug("dragHorizontal"),Xd=ug("dragVertical");function cg(e){let t=!1;if(e==="y")t=Xd();else if(e==="x")t=Yd();else{const n=Yd(),r=Xd();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function dg(){const e=cg(!0);return e?(e(),!1):!0}function er(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}const fg=1e-4,mS=1-fg,gS=1+fg,pg=.01,vS=0-pg,yS=0+pg;function Qe(e){return e.max-e.min}function wS(e,t,n){return Math.abs(e-t)<=n}function Zd(e,t,n,r=.5){e.origin=r,e.originPoint=oe(t.min,t.max,e.origin),e.scale=Qe(n)/Qe(t),e.translate=oe(n.min,n.max,e.origin)-e.originPoint,(e.scale>=mS&&e.scale<=gS||isNaN(e.scale))&&(e.scale=1),(e.translate>=vS&&e.translate<=yS||isNaN(e.translate))&&(e.translate=0)}function Jr(e,t,n,r){Zd(e.x,t.x,n.x,r?r.originX:void 0),Zd(e.y,t.y,n.y,r?r.originY:void 0)}function qd(e,t,n){e.min=n.min+t.min,e.max=e.min+Qe(t)}function xS(e,t,n){qd(e.x,t.x,n.x),qd(e.y,t.y,n.y)}function Jd(e,t,n){e.min=t.min-n.min,e.max=e.min+Qe(t)}function eo(e,t,n){Jd(e.x,t.x,n.x),Jd(e.y,t.y,n.y)}function SS(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?oe(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?oe(n,e,r.max):Math.min(e,n)),e}function ef(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function PS(e,{top:t,left:n,bottom:r,right:o}){return{x:ef(e.x,n,o),y:ef(e.y,t,r)}}function tf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function TS(e,t){return{x:tf(e.x,t.x),y:tf(e.y,t.y)}}function CS(e,t){let n=.5;const r=Qe(e),o=Qe(t);return o>r?n=To(t.min,t.max-r,e.min):r>o&&(n=To(e.min,e.max-o,t.min)),dn(0,1,n)}function ES(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const kl=.35;function kS(e=kl){return e===!1?e=0:e===!0&&(e=kl),{x:nf(e,"left","right"),y:nf(e,"top","bottom")}}function nf(e,t,n){return{min:rf(e,t),max:rf(e,n)}}function rf(e,t){return typeof e=="number"?e:e[t]||0}const of=()=>({translate:0,scale:1,origin:0,originPoint:0}),tr=()=>({x:of(),y:of()}),sf=()=>({min:0,max:0}),ue=()=>({x:sf(),y:sf()});function qe(e){return[e("x"),e("y")]}function hg({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function RS({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function bS(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function da(e){return e===void 0||e===1}function Rl({scale:e,scaleX:t,scaleY:n}){return!da(e)||!da(t)||!da(n)}function Pn(e){return Rl(e)||mg(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function mg(e){return af(e.x)||af(e.y)}function af(e){return e&&e!=="0%"}function Yi(e,t,n){const r=e-n,o=t*r;return n+o}function lf(e,t,n,r,o){return o!==void 0&&(e=Yi(e,o,r)),Yi(e,n,r)+t}function bl(e,t=0,n=1,r,o){e.min=lf(e.min,t,n,r,o),e.max=lf(e.max,t,n,r,o)}function gg(e,{x:t,y:n}){bl(e.x,t.translate,t.scale,t.originPoint),bl(e.y,n.translate,n.scale,n.originPoint)}const uf=.999999999999,cf=1.0000000000001;function AS(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let i,s;for(let a=0;a<o;a++){i=n[a],s=i.projectionDelta;const{visualElement:l}=i.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rr(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,gg(e,s)),r&&Pn(i.latestValues)&&rr(e,i.latestValues))}t.x<cf&&t.x>uf&&(t.x=1),t.y<cf&&t.y>uf&&(t.y=1)}function nr(e,t){e.min=e.min+t,e.max=e.max+t}function df(e,t,n,r,o=.5){const i=oe(e.min,e.max,o);bl(e,t,n,i,r)}function rr(e,t){df(e.x,t.x,t.scaleX,t.scale,t.originX),df(e.y,t.y,t.scaleY,t.scale,t.originY)}function vg(e,t){return hg(bS(e.getBoundingClientRect(),t))}function MS(e,t,n){const r=vg(e,n),{scroll:o}=t;return o&&(nr(r.x,o.offset.x),nr(r.y,o.offset.y)),r}const yg=({current:e})=>e?e.ownerDocument.defaultView:null,_S=new WeakMap;class LS{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ue(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=d=>{const{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(ks(d,"page").point)},i=(d,c)=>{var f;const{drag:m,dragPropagation:v,onDragStart:y}=this.getProps();if(m&&!v&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=cg(m),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),qe(h=>{let p=this.getAxisMotionValue(h).get()||0;if(yt.test(p)){const{projection:g}=this.visualElement;if(g&&g.layout){const S=g.layout.layoutBox[h];S&&(p=Qe(S)*(parseFloat(p)/100))}}this.originPoint[h]=p}),y&&K.postRender(()=>y(d,c)),(f=this.removeWillChange)===null||f===void 0||f.call(this),this.removeWillChange=Cl(this.visualElement,"transform");const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},s=(d,c)=>{const{dragPropagation:f,dragDirectionLock:m,onDirectionLock:v,onDrag:y}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:x}=c;if(m&&this.currentDirection===null){this.currentDirection=NS(x),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",c.point,x),this.updateAxis("y",c.point,x),this.visualElement.render(),y&&y(d,c)},a=(d,c)=>this.stop(d,c),l=()=>qe(d=>{var c;return this.getAnimationState(d)==="paused"&&((c=this.getAxisMotionValue(d).animation)===null||c===void 0?void 0:c.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new ag(t,{onSessionStart:o,onStart:i,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:yg(this.visualElement)})}stop(t,n){var r;(r=this.removeWillChange)===null||r===void 0||r.call(this);const o=this.isDragging;if(this.cancel(),!o)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&K.postRender(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!Jo(t,o,this.currentDirection))return;const i=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=SS(s,this.constraints[t],this.elastic[t])),i.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&er(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&o?this.constraints=PS(o.layoutBox,n):this.constraints=!1,this.elastic=kS(r),i!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&qe(s=>{this.constraints!==!1&&this.getAxisMotionValue(s)&&(this.constraints[s]=ES(o.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!er(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const i=MS(r,o.root,this.visualElement.getTransformPagePoint());let s=TS(o.layout.layoutBox,i);if(n){const a=n(RS(s));this.hasMutatedConstraints=!!a,a&&(s=hg(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=qe(d=>{if(!Jo(d,n,this.currentDirection))return;let c=l&&l[d]||{};s&&(c={min:0,max:0});const f=o?200:1e6,m=o?40:1e7,v={type:"inertia",velocity:r?t[d]:0,bounceStiffness:f,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...i,...c};return this.startAxisValueAnimation(d,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(Xu(t,r,0,n,this.visualElement,!1,Cl(this.visualElement,t)))}stopAnimation(){qe(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){qe(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),o=r[n];return o||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){qe(n=>{const{drag:r}=this.getProps();if(!Jo(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,i=this.getAxisMotionValue(n);if(o&&o.layout){const{min:s,max:a}=o.layout.layoutBox[n];i.set(t[n]-oe(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!er(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};qe(s=>{const a=this.getAxisMotionValue(s);if(a&&this.constraints!==!1){const l=a.get();o[s]=CS({min:l,max:l},this.constraints[s])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),qe(s=>{if(!Jo(s,t,null))return;const a=this.getAxisMotionValue(s),{min:l,max:u}=this.constraints[s];a.set(oe(l,u,o[s]))})}addListeners(){if(!this.visualElement.current)return;_S.set(this.visualElement,this);const t=this.visualElement.current,n=_t(t,"pointerdown",l=>{const{drag:u,dragListener:d=!0}=this.getProps();u&&d&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();er(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,i=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),K.read(r);const s=Et(window,"resize",()=>this.scalePositionWithinConstraints()),a=o.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(qe(d=>{const c=this.getAxisMotionValue(d);c&&(this.originPoint[d]+=l[d].translate,c.set(c.get()+l[d].translate))}),this.visualElement.render())});return()=>{s(),n(),i(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:i=!1,dragElastic:s=kl,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:i,dragElastic:s,dragMomentum:a}}}function Jo(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function NS(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class DS extends vn{constructor(t){super(t),this.removeGroupControls=be,this.removeListeners=be,this.controls=new LS(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||be}unmount(){this.removeGroupControls(),this.removeListeners()}}const ff=e=>(t,n)=>{e&&K.postRender(()=>e(t,n))};class OS extends vn{constructor(){super(...arguments),this.removePointerDownListener=be}onPointerDown(t){this.session=new ag(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:yg(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:ff(t),onStart:ff(n),onMove:r,onEnd:(i,s)=>{delete this.session,o&&K.postRender(()=>o(i,s))}}}mount(){this.removePointerDownListener=_t(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const qu=w.createContext(null);function VS(){const e=w.useContext(qu);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,o=w.useId();w.useEffect(()=>r(o),[]);const i=w.useCallback(()=>n&&n(o),[o,n]);return!t&&n?[!1,i]:[!0]}const wg=w.createContext({}),xg=w.createContext({}),gi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function pf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Nr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(j.test(e))e=parseFloat(e);else return e;const n=pf(e,t.target.x),r=pf(e,t.target.y);return`${n}% ${r}%`}},jS={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=fn.parse(e);if(o.length>5)return r;const i=fn.createTransformer(e),s=typeof o[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;o[0+s]/=a,o[1+s]/=l;const u=oe(a,l,.5);return typeof o[2+s]=="number"&&(o[2+s]/=u),typeof o[3+s]=="number"&&(o[3+s]/=u),i(o)}},Xi={};function IS(e){Object.assign(Xi,e)}const{schedule:Ju,cancel:$E}=xm(queueMicrotask,!1);class FS extends w.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:i}=t;IS(zS),i&&(n.group&&n.group.add(i),r&&r.register&&o&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),gi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:i}=this.props,s=r.projection;return s&&(s.isPresent=i,o||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?s.promote():s.relegate()||K.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Ju.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Sg(e){const[t,n]=VS(),r=w.useContext(wg);return b.jsx(FS,{...e,layoutGroup:r,switchLayoutGroup:w.useContext(xg),isPresent:t,safeToRemove:n})}const zS={borderRadius:{...Nr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Nr,borderTopRightRadius:Nr,borderBottomLeftRadius:Nr,borderBottomRightRadius:Nr,boxShadow:jS},Pg=["TopLeft","TopRight","BottomLeft","BottomRight"],BS=Pg.length,hf=e=>typeof e=="string"?parseFloat(e):e,mf=e=>typeof e=="number"||j.test(e);function $S(e,t,n,r,o,i){o?(e.opacity=oe(0,n.opacity!==void 0?n.opacity:1,US(r)),e.opacityExit=oe(t.opacity!==void 0?t.opacity:1,0,WS(r))):i&&(e.opacity=oe(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<BS;s++){const a=`border${Pg[s]}Radius`;let l=gf(t,a),u=gf(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||mf(l)===mf(u)?(e[a]=Math.max(oe(hf(l),hf(u),r),0),(yt.test(u)||yt.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=oe(t.rotate||0,n.rotate||0,r))}function gf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const US=Tg(0,.5,Gm),WS=Tg(.5,.95,be);function Tg(e,t,n){return r=>r<e?0:r>t?1:n(To(e,t,r))}function vf(e,t){e.min=t.min,e.max=t.max}function Ze(e,t){vf(e.x,t.x),vf(e.y,t.y)}function yf(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function wf(e,t,n,r,o){return e-=t,e=Yi(e,1/n,r),o!==void 0&&(e=Yi(e,1/o,r)),e}function HS(e,t=0,n=1,r=.5,o,i=e,s=e){if(yt.test(t)&&(t=parseFloat(t),t=oe(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=oe(i.min,i.max,r);e===i&&(a-=t),e.min=wf(e.min,t,n,a,o),e.max=wf(e.max,t,n,a,o)}function xf(e,t,[n,r,o],i,s){HS(e,t[n],t[r],t[o],t.scale,i,s)}const KS=["x","scaleX","originX"],GS=["y","scaleY","originY"];function Sf(e,t,n,r){xf(e.x,t,KS,n?n.x:void 0,r?r.x:void 0),xf(e.y,t,GS,n?n.y:void 0,r?r.y:void 0)}function Pf(e){return e.translate===0&&e.scale===1}function Cg(e){return Pf(e.x)&&Pf(e.y)}function Tf(e,t){return e.min===t.min&&e.max===t.max}function QS(e,t){return Tf(e.x,t.x)&&Tf(e.y,t.y)}function Cf(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Eg(e,t){return Cf(e.x,t.x)&&Cf(e.y,t.y)}function Ef(e){return Qe(e.x)/Qe(e.y)}function kf(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class YS{constructor(){this.members=[]}add(t){Ts(this.members,t),t.scheduleRender()}remove(t){if(Cs(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const i=this.members[o];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function XS(e,t,n){let r="";const o=e.x.translate/t.x,i=e.y.translate/t.y,s=(n==null?void 0:n.z)||0;if((o||i||s)&&(r=`translate3d(${o}px, ${i}px, ${s}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:d,rotateX:c,rotateY:f,skewX:m,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),d&&(r+=`rotate(${d}deg) `),c&&(r+=`rotateX(${c}deg) `),f&&(r+=`rotateY(${f}deg) `),m&&(r+=`skewX(${m}deg) `),v&&(r+=`skewY(${v}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}const ZS=(e,t)=>e.depth-t.depth;class qS{constructor(){this.children=[],this.isDirty=!1}add(t){Ts(this.children,t),this.isDirty=!0}remove(t){Cs(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(ZS),this.isDirty=!1,this.children.forEach(t)}}function vi(e){const t=Re(e)?e.get():e;return Wx(t)?t.toValue():t}function JS(e,t){const n=At.now(),r=({timestamp:o})=>{const i=o-n;i>=t&&(Vt(r),e(i-t))};return K.read(r,!0),()=>Vt(r)}function eP(e){return e instanceof SVGElement&&e.tagName!=="svg"}function tP(e,t,n){const r=Re(e)?e:Co(e);return r.start(Xu("",r,t,n)),r.animation}const Tn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Br=typeof window<"u"&&window.MotionDebug!==void 0,fa=["","X","Y","Z"],nP={visibility:"hidden"},Rf=1e3;let rP=0;function pa(e,t,n,r){const{latestValues:o}=t;o[e]&&(n[e]=o[e],t.setStaticValue(e,0),r&&(r[e]=0))}function kg(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=rg(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:o,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",K,!(o||i))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&kg(r)}function Rg({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(s={},a=t==null?void 0:t()){this.id=rP++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Br&&(Tn.totalNodes=Tn.resolvedTargetDeltas=Tn.recalculatedProjection=0),this.nodes.forEach(sP),this.nodes.forEach(dP),this.nodes.forEach(fP),this.nodes.forEach(aP),Br&&window.MotionDebug.record(Tn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new qS)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new Zu),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l&&l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=eP(s),this.instance=s;const{layoutId:l,layout:u,visualElement:d}=this.options;if(d&&!d.current&&d.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let c;const f=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=JS(f,250),gi.hasAnimatedSinceResize&&(gi.hasAnimatedSinceResize=!1,this.nodes.forEach(Af))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&d&&(l||u)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:f,hasRelativeTargetChanged:m,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const y=this.options.transition||d.getDefaultTransition()||vP,{onLayoutAnimationStart:x,onLayoutAnimationComplete:h}=d.getProps(),p=!this.targetLayout||!Eg(this.targetLayout,v)||m,g=!f&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||f&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(c,g);const S={...Fu(y,"layout"),onPlay:x,onComplete:h};(d.shouldReduceMotion||this.options.layoutRoot)&&(S.delay=0,S.type=!1),this.startAnimation(S)}else f||Af(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Vt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(pP),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&kg(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let d=0;d<this.path.length;d++){const c=this.path[d];c.shouldResetTransform=!0,c.updateScroll("snapshot"),c.options.layoutRoot&&c.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(bf);return}this.isUpdating||this.nodes.forEach(uP),this.isUpdating=!1,this.nodes.forEach(cP),this.nodes.forEach(oP),this.nodes.forEach(iP),this.clearAllSnapshots();const a=At.now();xe.delta=dn(0,1e3/60,a-xe.timestamp),xe.timestamp=a,xe.isProcessing=!0,oa.update.process(xe),oa.preRender.process(xe),oa.render.process(xe),xe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ju.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(lP),this.sharedNodes.forEach(hP)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ue(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:s,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!o)return;const s=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Cg(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,d=u!==this.prevTransformTemplateValue;s&&(a||Pn(this.latestValues)||d)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),yP(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var s;const{visualElement:a}=this.options;if(!a)return ue();const l=a.measureViewportBox();if(!(((s=this.scroll)===null||s===void 0?void 0:s.wasRoot)||this.path.some(wP))){const{scroll:d}=this.root;d&&(nr(l.x,d.offset.x),nr(l.y,d.offset.y))}return l}removeElementScroll(s){var a;const l=ue();if(Ze(l,s),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let u=0;u<this.path.length;u++){const d=this.path[u],{scroll:c,options:f}=d;d!==this.root&&c&&f.layoutScroll&&(c.wasRoot&&Ze(l,s),nr(l.x,c.offset.x),nr(l.y,c.offset.y))}return l}applyTransform(s,a=!1){const l=ue();Ze(l,s);for(let u=0;u<this.path.length;u++){const d=this.path[u];!a&&d.options.layoutScroll&&d.scroll&&d!==d.root&&rr(l,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),Pn(d.latestValues)&&rr(l,d.latestValues)}return Pn(this.latestValues)&&rr(l,this.latestValues),l}removeTransform(s){const a=ue();Ze(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Pn(u.latestValues))continue;Rl(u.latestValues)&&u.updateSnapshot();const d=ue(),c=u.measurePageBox();Ze(d,c),Sf(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,d)}return Pn(this.latestValues)&&Sf(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==xe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:c,layoutId:f}=this.options;if(!(!this.layout||!(c||f))){if(this.resolvedRelativeTargetAt=xe.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ue(),this.relativeTargetOrigin=ue(),eo(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),Ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ue(),this.targetWithTransforms=ue()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),xS(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ze(this.target,this.layout.layoutBox),gg(this.target,this.targetDelta)):Ze(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ue(),this.relativeTargetOrigin=ue(),eo(this.relativeTargetOrigin,this.target,m.target),Ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Br&&Tn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Rl(this.parent.latestValues)||mg(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===xe.timestamp&&(u=!1),u)return;const{layout:d,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||c))return;Ze(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,m=this.treeScale.y;AS(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=ue());const{target:v}=a;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(yf(this.prevProjectionDelta.x,this.projectionDelta.x),yf(this.prevProjectionDelta.y,this.projectionDelta.y)),Jr(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==m||!kf(this.projectionDelta.x,this.prevProjectionDelta.x)||!kf(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),Br&&Tn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=tr(),this.projectionDelta=tr(),this.projectionDeltaWithTransform=tr()}setAnimationOrigin(s,a=!1){const l=this.snapshot,u=l?l.latestValues:{},d={...this.latestValues},c=tr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=ue(),m=l?l.source:void 0,v=this.layout?this.layout.source:void 0,y=m!==v,x=this.getStack(),h=!x||x.members.length<=1,p=!!(y&&!h&&this.options.crossfade===!0&&!this.path.some(gP));this.animationProgress=0;let g;this.mixTargetDelta=S=>{const P=S/1e3;Mf(c.x,s.x,P),Mf(c.y,s.y,P),this.setTargetDelta(c),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(eo(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),mP(this.relativeTarget,this.relativeTargetOrigin,f,P),g&&QS(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=ue()),Ze(g,this.relativeTarget)),y&&(this.animationValues=d,$S(d,u,this.latestValues,P,p,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=P},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Vt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{gi.hasAnimatedSinceResize=!0,this.currentAnimation=tP(0,Rf,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Rf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:d}=s;if(!(!a||!l||!u)){if(this!==s&&this.layout&&u&&bg(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ue();const c=Qe(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+c;const f=Qe(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+f}Ze(a,l),rr(a,d),Jr(this.projectionDeltaWithTransform,this.layoutCorrected,a,d)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new YS),this.sharedNodes.get(s).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetSkewAndRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&pa("z",s,u,this.animationValues);for(let d=0;d<fa.length;d++)pa(`rotate${fa[d]}`,s,u,this.animationValues),pa(`skew${fa[d]}`,s,u,this.animationValues);s.render();for(const d in u)s.setStaticValue(d,u[d]),this.animationValues&&(this.animationValues[d]=u[d]);s.scheduleRender()}getProjectionStyles(s){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nP;const u={visibility:""},d=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=vi(s==null?void 0:s.pointerEvents)||"",u.transform=d?d(this.latestValues,""):"none",u;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const y={};return this.options.layoutId&&(y.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,y.pointerEvents=vi(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!Pn(this.latestValues)&&(y.transform=d?d({},""):"none",this.hasProjected=!1),y}const f=c.animationValues||c.latestValues;this.applyTransformsToTarget(),u.transform=XS(this.projectionDeltaWithTransform,this.treeScale,f),d&&(u.transform=d(f,u.transform));const{x:m,y:v}=this.projectionDelta;u.transformOrigin=`${m.origin*100}% ${v.origin*100}% 0`,c.animationValues?u.opacity=c===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=c===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const y in Xi){if(f[y]===void 0)continue;const{correct:x,applyTo:h}=Xi[y],p=u.transform==="none"?f[y]:x(f[y],c);if(h){const g=h.length;for(let S=0;S<g;S++)u[h[S]]=p}else u[y]=p}return this.options.layoutId&&(u.pointerEvents=c===this?vi(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(bf),this.root.sharedNodes.clear()}}}function oP(e){e.updateLayout()}function iP(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=e.layout,{animationType:i}=e.options,s=n.source!==e.layout.source;i==="size"?qe(c=>{const f=s?n.measuredBox[c]:n.layoutBox[c],m=Qe(f);f.min=r[c].min,f.max=f.min+m}):bg(i,n.layoutBox,r)&&qe(c=>{const f=s?n.measuredBox[c]:n.layoutBox[c],m=Qe(r[c]);f.max=f.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[c].max=e.relativeTarget[c].min+m)});const a=tr();Jr(a,r,n.layoutBox);const l=tr();s?Jr(l,e.applyTransform(o,!0),n.measuredBox):Jr(l,r,n.layoutBox);const u=!Cg(a);let d=!1;if(!e.resumeFrom){const c=e.getClosestProjectingParent();if(c&&!c.resumeFrom){const{snapshot:f,layout:m}=c;if(f&&m){const v=ue();eo(v,n.layoutBox,f.layoutBox);const y=ue();eo(y,r,m.layoutBox),Eg(v,y)||(d=!0),c.options.layoutRoot&&(e.relativeTarget=y,e.relativeTargetOrigin=v,e.relativeParent=c)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:d})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function sP(e){Br&&Tn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function aP(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function lP(e){e.clearSnapshot()}function bf(e){e.clearMeasurements()}function uP(e){e.isLayoutDirty=!1}function cP(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Af(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function dP(e){e.resolveTargetDelta()}function fP(e){e.calcProjection()}function pP(e){e.resetSkewAndRotation()}function hP(e){e.removeLeadSnapshot()}function Mf(e,t,n){e.translate=oe(t.translate,0,n),e.scale=oe(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function _f(e,t,n,r){e.min=oe(t.min,n.min,r),e.max=oe(t.max,n.max,r)}function mP(e,t,n,r){_f(e.x,t.x,n.x,r),_f(e.y,t.y,n.y,r)}function gP(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const vP={duration:.45,ease:[.4,0,.1,1]},Lf=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Nf=Lf("applewebkit/")&&!Lf("chrome/")?Math.round:be;function Df(e){e.min=Nf(e.min),e.max=Nf(e.max)}function yP(e){Df(e.x),Df(e.y)}function bg(e,t,n){return e==="position"||e==="preserve-aspect"&&!wS(Ef(t),Ef(n),.2)}function wP(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const xP=Rg({attachResizeListener:(e,t)=>Et(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ha={current:void 0},Ag=Rg({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ha.current){const e=new xP({});e.mount(window),e.setOptions({layoutScroll:!0}),ha.current=e}return ha.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),SP={pan:{Feature:OS},drag:{Feature:DS,ProjectionNode:Ag,MeasureLayout:Sg}};function Of(e,t){const n=t?"pointerenter":"pointerleave",r=t?"onHoverStart":"onHoverEnd",o=(i,s)=>{if(i.pointerType==="touch"||dg())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t);const l=a[r];l&&K.postRender(()=>l(i,s))};return _t(e.current,n,o,{passive:!e.getProps()[r]})}class PP extends vn{mount(){this.unmount=Mt(Of(this.node,!0),Of(this.node,!1))}unmount(){}}class TP extends vn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Mt(Et(this.node.current,"focus",()=>this.onFocus()),Et(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Mg=(e,t)=>t?e===t?!0:Mg(e,t.parentElement):!1;function ma(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,ks(n))}class CP extends vn{constructor(){super(...arguments),this.removeStartListeners=be,this.removeEndListeners=be,this.removeAccessibleListeners=be,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),i=_t(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:d,globalTapTarget:c}=this.node.getProps(),f=!c&&!Mg(this.node.current,a.target)?d:u;f&&K.update(()=>f(a,l))},{passive:!(r.onTap||r.onPointerUp)}),s=_t(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Mt(i,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=i=>{if(i.key!=="Enter"||this.isPressing)return;const s=a=>{a.key!=="Enter"||!this.checkPressEnd()||ma("up",(l,u)=>{const{onTap:d}=this.node.getProps();d&&K.postRender(()=>d(l,u))})};this.removeEndListeners(),this.removeEndListeners=Et(this.node.current,"keyup",s),ma("down",(a,l)=>{this.startPress(a,l)})},n=Et(this.node.current,"keydown",t),r=()=>{this.isPressing&&ma("cancel",(i,s)=>this.cancelPress(i,s))},o=Et(this.node.current,"blur",r);this.removeAccessibleListeners=Mt(n,o)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:o}=this.node.getProps();o&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&K.postRender(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!dg()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&K.postRender(()=>r(t,n))}mount(){const t=this.node.getProps(),n=_t(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Et(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Mt(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Al=new WeakMap,ga=new WeakMap,EP=e=>{const t=Al.get(e.target);t&&t(e)},kP=e=>{e.forEach(EP)};function RP({root:e,...t}){const n=e||document;ga.has(n)||ga.set(n,{});const r=ga.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(kP,{root:e,...t})),r[o]}function bP(e,t,n){const r=RP(t);return Al.set(e,n),r.observe(e),()=>{Al.delete(e),r.unobserve(e)}}const AP={some:0,all:1};class MP extends vn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:i}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:AP[o]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:d,onViewportLeave:c}=this.node.getProps(),f=u?d:c;f&&f(l)};return bP(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(_P(t,n))&&this.startObserver()}unmount(){}}function _P({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const LP={inView:{Feature:MP},tap:{Feature:CP},focus:{Feature:TP},hover:{Feature:PP}},NP={layout:{ProjectionNode:Ag,MeasureLayout:Sg}},_g=w.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Rs=w.createContext({}),ec=typeof window<"u",DP=ec?w.useLayoutEffect:w.useEffect,Lg=w.createContext({strict:!1});let Vf=!1;function OP(e,t,n,r,o){var i;const{visualElement:s}=w.useContext(Rs),a=w.useContext(Lg),l=w.useContext(qu),u=w.useContext(_g).reducedMotion,d=w.useRef();r=r||a.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:s,props:n,presenceContext:l,blockInitialAnimation:l?l.initial===!1:!1,reducedMotionConfig:u}));const c=d.current,f=w.useContext(xg);c&&!c.projection&&o&&(c.type==="html"||c.type==="svg")&&jP(d.current,n,o,f),w.useInsertionEffect(()=>{c&&c.update(n,l)});const m=n[ng],v=w.useRef(!!m&&!window.MotionHandoffIsComplete&&((i=window.MotionHasOptimisedAnimation)===null||i===void 0?void 0:i.call(window,m)));return DP(()=>{c&&(c.updateFeatures(),Ju.render(c.render),v.current&&c.animationState&&c.animationState.animateChanges())}),w.useEffect(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current=!1,Vf||(Vf=!0,queueMicrotask(VP)))}),c}function VP(){window.MotionHandoffIsComplete=!0}function jP(e,t,n,r){const{layoutId:o,layout:i,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Ng(e.parent)),e.projection.setOptions({layoutId:o,layout:i,alwaysMeasureLayout:!!s||a&&er(a),visualElement:e,animationType:typeof i=="string"?i:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}function Ng(e){if(e)return e.options.allowProjection!==!1?e.projection:Ng(e.parent)}function IP(e,t,n){return w.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):er(n)&&(n.current=r))},[t])}function bs(e){return xo(e.animate)||Iu.some(t=>So(e[t]))}function Dg(e){return!!(bs(e)||e.variants)}function FP(e,t){if(bs(e)){const{initial:n,animate:r}=e;return{initial:n===!1||So(n)?n:void 0,animate:So(r)?r:void 0}}return e.inherit!==!1?t:{}}function zP(e){const{initial:t,animate:n}=FP(e,w.useContext(Rs));return w.useMemo(()=>({initial:t,animate:n}),[jf(t),jf(n)])}function jf(e){return Array.isArray(e)?e.join(" "):e}const If={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},wr={};for(const e in If)wr[e]={isEnabled:t=>If[e].some(n=>!!t[n])};function BP(e){for(const t in e)wr[t]={...wr[t],...e[t]}}const $P=Symbol.for("motionComponentSymbol");function UP({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){e&&BP(e);function i(a,l){let u;const d={...w.useContext(_g),...a,layoutId:WP(a)},{isStatic:c}=d,f=zP(a),m=r(a,c);if(!c&&ec){HP();const v=KP(d);u=v.MeasureLayout,f.visualElement=OP(o,m,d,t,v.ProjectionNode)}return b.jsxs(Rs.Provider,{value:f,children:[u&&f.visualElement?b.jsx(u,{visualElement:f.visualElement,...d}):null,n(o,a,IP(m,f.visualElement,l),m,c,f.visualElement)]})}const s=w.forwardRef(i);return s[$P]=o,s}function WP({layoutId:e}){const t=w.useContext(wg).id;return t&&e!==void 0?t+"-"+e:e}function HP(e,t){w.useContext(Lg).strict}function KP(e){const{drag:t,layout:n}=wr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const GP=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function tc(e){return typeof e!="string"||e.includes("-")?!1:!!(GP.indexOf(e)>-1||/[A-Z]/u.test(e))}function Og(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const Vg=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function jg(e,t,n,r){Og(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(Vg.has(o)?o:Es(o),t.attrs[o])}function Ig(e,{layout:t,layoutId:n}){return gn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Xi[e]||e==="opacity")}function nc(e,t,n){var r;const{style:o}=e,i={};for(const s in o)(Re(o[s])||t.style&&Re(t.style[s])||Ig(s,e)||((r=n==null?void 0:n.getValue(s))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(i[s]=o[s]);return n&&o&&typeof o.willChange=="string"&&(n.applyWillChange=!1),i}function Fg(e,t,n){const r=nc(e,t,n);for(const o in e)if(Re(e[o])||Re(t[o])){const i=Mo.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[i]=e[o]}return r}function QP(e){const t=w.useRef(null);return t.current===null&&(t.current=e()),t.current}function YP({applyWillChange:e=!1,scrapeMotionValuesFromProps:t,createRenderState:n,onMount:r},o,i,s,a){const l={latestValues:ZP(o,i,s,a?!1:e,t),renderState:n()};return r&&(l.mount=u=>r(o,u,l)),l}const zg=e=>(t,n)=>{const r=w.useContext(Rs),o=w.useContext(qu),i=()=>YP(e,t,r,o,n);return n?i():QP(i)};function XP(e,t){const n=og(t);n&&Ts(e,n)}function Ff(e,t,n){const r=Array.isArray(t)?t:[t];for(let o=0;o<r.length;o++){const i=Vu(e,r[o]);if(i){const{transitionEnd:s,transition:a,...l}=i;n(l,s)}}}function ZP(e,t,n,r,o){var i;const s={},a=[],l=r&&((i=e.style)===null||i===void 0?void 0:i.willChange)===void 0,u=o(e,{});for(const x in u)s[x]=vi(u[x]);let{initial:d,animate:c}=e;const f=bs(e),m=Dg(e);t&&m&&!f&&e.inherit!==!1&&(d===void 0&&(d=t.initial),c===void 0&&(c=t.animate));let v=n?n.initial===!1:!1;v=v||d===!1;const y=v?c:d;return y&&typeof y!="boolean"&&!xo(y)&&Ff(e,y,(x,h)=>{for(const p in x){let g=x[p];if(Array.isArray(g)){const S=v?g.length-1:0;g=g[S]}g!==null&&(s[p]=g)}for(const p in h)s[p]=h[p]}),l&&(c&&d!==!1&&!xo(c)&&Ff(e,c,x=>{for(const h in x)XP(a,h)}),a.length&&(s.willChange=a.join(","))),s}const rc=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Bg=()=>({...rc(),attrs:{}}),$g=(e,t)=>t&&typeof e=="number"?t.transform(e):e,qP={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},JP=Mo.length;function eT(e,t,n){let r="",o=!0;for(let i=0;i<JP;i++){const s=Mo[i],a=e[s];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(s.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=$g(a,Wu[s]);if(!l){o=!1;const d=qP[s]||s;r+=`${d}(${u}) `}n&&(t[s]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function oc(e,t,n){const{style:r,vars:o,transformOrigin:i}=e;let s=!1,a=!1;for(const l in t){const u=t[l];if(gn.has(l)){s=!0;continue}else if(Em(l)){o[l]=u;continue}else{const d=$g(u,Wu[l]);l.startsWith("origin")?(a=!0,i[l]=d):r[l]=d}}if(t.transform||(s||n?r.transform=eT(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:d=0}=i;r.transformOrigin=`${l} ${u} ${d}`}}function zf(e,t,n){return typeof e=="string"?e:j.transform(t+n*e)}function tT(e,t,n){const r=zf(t,e.x,e.width),o=zf(n,e.y,e.height);return`${r} ${o}`}const nT={offset:"stroke-dashoffset",array:"stroke-dasharray"},rT={offset:"strokeDashoffset",array:"strokeDasharray"};function oT(e,t,n=1,r=0,o=!0){e.pathLength=1;const i=o?nT:rT;e[i.offset]=j.transform(-r);const s=j.transform(t),a=j.transform(n);e[i.array]=`${s} ${a}`}function ic(e,{attrX:t,attrY:n,attrScale:r,originX:o,originY:i,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},d,c){if(oc(e,u,c),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:m,dimensions:v}=e;f.transform&&(v&&(m.transform=f.transform),delete f.transform),v&&(o!==void 0||i!==void 0||m.transform)&&(m.transformOrigin=tT(v,o!==void 0?o:.5,i!==void 0?i:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),s!==void 0&&oT(f,s,a,l,!1)}const sc=e=>typeof e=="string"&&e.toLowerCase()==="svg",iT={useVisualState:zg({scrapeMotionValuesFromProps:Fg,createRenderState:Bg,onMount:(e,t,{renderState:n,latestValues:r})=>{K.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),K.render(()=>{ic(n,r,sc(t.tagName),e.transformTemplate),jg(t,n)})}})},sT={useVisualState:zg({applyWillChange:!0,scrapeMotionValuesFromProps:nc,createRenderState:rc})};function Ug(e,t,n){for(const r in t)!Re(t[r])&&!Ig(r,n)&&(e[r]=t[r])}function aT({transformTemplate:e},t){return w.useMemo(()=>{const n=rc();return oc(n,t,e),Object.assign({},n.vars,n.style)},[t])}function lT(e,t){const n=e.style||{},r={};return Ug(r,n,e),Object.assign(r,aT(e,t)),r}function uT(e,t){const n={},r=lT(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const cT=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Zi(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||cT.has(e)}let Wg=e=>!Zi(e);function dT(e){e&&(Wg=t=>t.startsWith("on")?!Zi(t):e(t))}try{dT(require("@emotion/is-prop-valid").default)}catch{}function fT(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(Wg(o)||n===!0&&Zi(o)||!t&&!Zi(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function pT(e,t,n,r){const o=w.useMemo(()=>{const i=Bg();return ic(i,t,sc(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};Ug(i,e.style,e),o.style={...i,...o.style}}return o}function hT(e=!1){return(n,r,o,{latestValues:i},s)=>{const l=(tc(n)?pT:uT)(r,i,s,n),u=fT(r,typeof n=="string",e),d=n!==w.Fragment?{...u,...l,ref:o}:{},{children:c}=r,f=w.useMemo(()=>Re(c)?c.get():c,[c]);return w.createElement(n,{...d,children:f})}}function mT(e,t){return function(r,{forwardMotionProps:o}={forwardMotionProps:!1}){const s={...tc(r)?iT:sT,preloadedFeatures:e,useRender:hT(o),createVisualElement:t,Component:r};return UP(s)}}const Ml={current:null},Hg={current:!1};function gT(){if(Hg.current=!0,!!ec)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Ml.current=e.matches;e.addListener(t),t()}else Ml.current=!1}function vT(e,t,n){for(const r in t){const o=t[r],i=n[r];if(Re(o))e.addValue(r,o);else if(Re(i))e.addValue(r,Co(o,{owner:e}));else if(i!==o)if(e.hasValue(r)){const s=e.getValue(r);s.liveStyle===!0?s.jump(o):s.hasAnimated||s.set(o)}else{const s=e.getStaticValue(r);e.addValue(r,Co(s!==void 0?s:o,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Bf=new WeakMap,yT=[...bm,Ee,fn],wT=e=>yT.find(Rm(e)),$f=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],xT=Iu.length;class ST{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:i,visualState:s},a={}){this.applyWillChange=!1,this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=$u,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.isRenderScheduled=!1,this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.isRenderScheduled=!1,this.scheduleRender=()=>{this.isRenderScheduled||(this.isRenderScheduled=!0,K.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=s;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=bs(n),this.isVariantNode=Dg(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:d,...c}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in c){const m=c[f];l[f]!==void 0&&Re(m)&&m.set(l[f],!1)}}mount(t){this.current=t,Bf.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Hg.current||gT(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ml.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Bf.delete(this.current),this.projection&&this.projection.unmount(),Vt(this.notifyUpdate),Vt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=gn.has(t),o=n.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&K.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{o(),i(),s&&s(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in wr){const n=wr[t];if(!n)continue;const{isEnabled:r,Feature:o}=n;if(!this.features[t]&&o&&r(this.props)&&(this.features[t]=new o(this)),this.features[t]){const i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ue()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<$f.length;r++){const o=$f[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const i="on"+o,s=t[i];s&&(this.propEventSubscriptions[o]=this.on(o,s))}this.prevMotionValues=vT(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<xT;r++){const o=Iu[r],i=this.props[o];(So(i)||i===!1)&&(n[o]=i)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Co(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let o=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return o!=null&&(typeof o=="string"&&(Tm(o)||Sm(o))?o=parseFloat(o):!wT(o)&&fn.test(n)&&(o=Vm(t,n)),this.setBaseTarget(t,Re(o)?o.get():o)),Re(o)?o.get():o}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let o;if(typeof r=="string"||typeof r=="object"){const s=Vu(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);s&&(o=s[t])}if(r&&o!==void 0)return o;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!Re(i)?i:this.initialValues[t]!==void 0&&o===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Zu),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Kg extends ST{constructor(){super(...arguments),this.KeyframeResolver=jm}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}}function PT(e){return window.getComputedStyle(e)}class TT extends Kg{constructor(){super(...arguments),this.type="html",this.applyWillChange=!0,this.renderInstance=Og}readValueFromInstance(t,n){if(gn.has(n)){const r=Hu(n);return r&&r.default||0}else{const r=PT(t),o=(Em(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return vg(t,n)}build(t,n,r){oc(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return nc(t,n,r)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Re(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}class CT extends Kg{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ue}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(gn.has(n)){const r=Hu(n);return r&&r.default||0}return n=Vg.has(n)?n:Es(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return Fg(t,n,r)}build(t,n,r){ic(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,o){jg(t,n,r,o)}mount(t){this.isSVGTag=sc(t.tagName),super.mount(t)}}const ET=(e,t)=>tc(e)?new CT(t):new TT(t,{allowProjection:e!==w.Fragment}),kT=mT({...cS,...LP,...SP,...NP},ET),Gg=ow(kT);function qi(){return qi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},qi.apply(this,arguments)}function RT(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Qg(...e){return t=>e.forEach(n=>RT(n,t))}function WE(...e){return w.useCallback(Qg(...e),e)}const Yg=w.forwardRef((e,t)=>{const{children:n,...r}=e,o=w.Children.toArray(n),i=o.find(AT);if(i){const s=i.props.children,a=o.map(l=>l===i?w.Children.count(s)>1?w.Children.only(null):w.isValidElement(s)?s.props.children:null:l);return w.createElement(_l,qi({},r,{ref:t}),w.isValidElement(s)?w.cloneElement(s,void 0,a):null)}return w.createElement(_l,qi({},r,{ref:t}),n)});Yg.displayName="Slot";const _l=w.forwardRef((e,t)=>{const{children:n,...r}=e;return w.isValidElement(n)?w.cloneElement(n,{...MT(r,n.props),ref:t?Qg(t,n.ref):n.ref}):w.Children.count(n)>1?w.Children.only(null):null});_l.displayName="SlotClone";const bT=({children:e})=>w.createElement(w.Fragment,null,e);function AT(e){return w.isValidElement(e)&&e.type===bT}function MT(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function Xg(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Xg(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function _T(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Xg(e))&&(r&&(r+=" "),r+=t);return r}const Uf=e=>typeof e=="boolean"?"".concat(e):e===0?"0":e,Wf=_T,Zg=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Wf(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const d=n==null?void 0:n[u],c=i==null?void 0:i[u];if(d===null)return null;const f=Uf(d)||Uf(c);return o[u][f]}),a=n&&Object.entries(n).reduce((u,d)=>{let[c,f]=d;return f===void 0||(u[c]=f),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,d)=>{let{class:c,className:f,...m}=d;return Object.entries(m).every(v=>{let[y,x]=v;return Array.isArray(x)?x.includes({...i,...a}[y]):{...i,...a}[y]===x})?[...u,c,f]:u},[]);return Wf(e,s,l,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var LT={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NT=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),qg=(e,t)=>{const n=w.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:a="",children:l,...u},d)=>w.createElement("svg",{ref:d,...LT,width:o,height:o,stroke:r,strokeWidth:s?Number(i)*24/Number(o):i,className:["lucide",`lucide-${NT(e)}`,a].join(" "),...u},[...t.map(([c,f])=>w.createElement(c,f)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DT=qg("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const OT=qg("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function Jg(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Jg(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function VT(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Jg(e))&&(r&&(r+=" "),r+=t);return r}const ac="-";function jT(e){const t=FT(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;function o(s){const a=s.split(ac);return a[0]===""&&a.length!==1&&a.shift(),ev(a,t)||IT(s)}function i(s,a){const l=n[s]||[];return a&&r[s]?[...l,...r[s]]:l}return{getClassGroupId:o,getConflictingClassGroupIds:i}}function ev(e,t){var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?ev(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(ac);return(s=t.validators.find(({validator:a})=>a(i)))==null?void 0:s.classGroupId}const Hf=/^\[(.+)\]$/;function IT(e){if(Hf.test(e)){const t=Hf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}function FT(e){const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return BT(Object.entries(e.classGroups),n).forEach(([i,s])=>{Ll(s,r,i,t)}),r}function Ll(e,t,n,r){e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:Kf(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(zT(o)){Ll(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{Ll(s,Kf(t,i),n,r)})})}function Kf(e,t){let n=e;return t.split(ac).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function zT(e){return e.isThemeGetter}function BT(e,t){return t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,a])=>[t+s,a])):i);return[n,o]}):e}function $T(e){if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;function o(i,s){n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)}return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}}const tv="!";function UT(e){const t=e.separator,n=t.length===1,r=t[0],o=t.length;return function(s){const a=[];let l=0,u=0,d;for(let y=0;y<s.length;y++){let x=s[y];if(l===0){if(x===r&&(n||s.slice(y,y+o)===t)){a.push(s.slice(u,y)),u=y+o;continue}if(x==="/"){d=y;continue}}x==="["?l++:x==="]"&&l--}const c=a.length===0?s:s.substring(u),f=c.startsWith(tv),m=f?c.substring(1):c,v=d&&d>u?d-u:void 0;return{modifiers:a,hasImportantModifier:f,baseClassName:m,maybePostfixModifierPosition:v}}}function WT(e){if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t}function HT(e){return{cache:$T(e.cacheSize),splitModifiers:UT(e),...jT(e)}}const KT=/\s+/;function GT(e,t){const{splitModifiers:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=new Set;return e.trim().split(KT).map(s=>{const{modifiers:a,hasImportantModifier:l,baseClassName:u,maybePostfixModifierPosition:d}=n(s);let c=r(d?u.substring(0,d):u),f=!!d;if(!c){if(!d)return{isTailwindClass:!1,originalClassName:s};if(c=r(u),!c)return{isTailwindClass:!1,originalClassName:s};f=!1}const m=WT(a).join(":");return{isTailwindClass:!0,modifierId:l?m+tv:m,classGroupId:c,originalClassName:s,hasPostfixModifier:f}}).reverse().filter(s=>{if(!s.isTailwindClass)return!0;const{modifierId:a,classGroupId:l,hasPostfixModifier:u}=s,d=a+l;return i.has(d)?!1:(i.add(d),o(l,u).forEach(c=>i.add(a+c)),!0)}).reverse().map(s=>s.originalClassName).join(" ")}function QT(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=nv(t))&&(r&&(r+=" "),r+=n);return r}function nv(e){if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=nv(e[r]))&&(n&&(n+=" "),n+=t);return n}function YT(e,...t){let n,r,o,i=s;function s(l){const u=t.reduce((d,c)=>c(d),e());return n=HT(u),r=n.cache.get,o=n.cache.set,i=a,a(l)}function a(l){const u=r(l);if(u)return u;const d=GT(l,n);return o(l,d),d}return function(){return i(QT.apply(null,arguments))}}function Z(e){const t=n=>n[e]||[];return t.isThemeGetter=!0,t}const rv=/^\[(?:([a-z-]+):)?(.+)\]$/i,XT=/^\d+\/\d+$/,ZT=new Set(["px","full","screen"]),qT=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,JT=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,eC=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,tC=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,nC=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function xt(e){return bn(e)||ZT.has(e)||XT.test(e)}function Wt(e){return Cr(e,"length",cC)}function bn(e){return!!e&&!Number.isNaN(Number(e))}function ei(e){return Cr(e,"number",bn)}function Dr(e){return!!e&&Number.isInteger(Number(e))}function rC(e){return e.endsWith("%")&&bn(e.slice(0,-1))}function z(e){return rv.test(e)}function Ht(e){return qT.test(e)}const oC=new Set(["length","size","percentage"]);function iC(e){return Cr(e,oC,ov)}function sC(e){return Cr(e,"position",ov)}const aC=new Set(["image","url"]);function lC(e){return Cr(e,aC,fC)}function uC(e){return Cr(e,"",dC)}function Or(){return!0}function Cr(e,t,n){const r=rv.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1}function cC(e){return JT.test(e)&&!eC.test(e)}function ov(){return!1}function dC(e){return tC.test(e)}function fC(e){return nC.test(e)}function pC(){const e=Z("colors"),t=Z("spacing"),n=Z("blur"),r=Z("brightness"),o=Z("borderColor"),i=Z("borderRadius"),s=Z("borderSpacing"),a=Z("borderWidth"),l=Z("contrast"),u=Z("grayscale"),d=Z("hueRotate"),c=Z("invert"),f=Z("gap"),m=Z("gradientColorStops"),v=Z("gradientColorStopPositions"),y=Z("inset"),x=Z("margin"),h=Z("opacity"),p=Z("padding"),g=Z("saturate"),S=Z("scale"),P=Z("sepia"),C=Z("skew"),k=Z("space"),T=Z("translate"),D=()=>["auto","contain","none"],L=()=>["auto","hidden","clip","visible","scroll"],_=()=>["auto",z,t],E=()=>[z,t],F=()=>["",xt,Wt],A=()=>["auto",bn,z],V=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],B=()=>["solid","dashed","dotted","double","none"],X=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"],M=()=>["start","end","center","between","around","evenly","stretch"],O=()=>["","0",z],I=()=>["auto","avoid","all","avoid-page","page","left","right","column"],U=()=>[bn,ei],W=()=>[bn,z];return{cacheSize:500,separator:":",theme:{colors:[Or],spacing:[xt,Wt],blur:["none","",Ht,z],brightness:U(),borderColor:[e],borderRadius:["none","","full",Ht,z],borderSpacing:E(),borderWidth:F(),contrast:U(),grayscale:O(),hueRotate:W(),invert:O(),gap:E(),gradientColorStops:[e],gradientColorStopPositions:[rC,Wt],inset:_(),margin:_(),opacity:U(),padding:E(),saturate:U(),scale:U(),sepia:O(),skew:W(),space:E(),translate:E()},classGroups:{aspect:[{aspect:["auto","square","video",z]}],container:["container"],columns:[{columns:[Ht]}],"break-after":[{"break-after":I()}],"break-before":[{"break-before":I()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...V(),z]}],overflow:[{overflow:L()}],"overflow-x":[{"overflow-x":L()}],"overflow-y":[{"overflow-y":L()}],overscroll:[{overscroll:D()}],"overscroll-x":[{"overscroll-x":D()}],"overscroll-y":[{"overscroll-y":D()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Dr,z]}],basis:[{basis:_()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",z]}],grow:[{grow:O()}],shrink:[{shrink:O()}],order:[{order:["first","last","none",Dr,z]}],"grid-cols":[{"grid-cols":[Or]}],"col-start-end":[{col:["auto",{span:["full",Dr,z]},z]}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":[Or]}],"row-start-end":[{row:["auto",{span:[Dr,z]},z]}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",z]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",z]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...M()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...M(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...M(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",z,t]}],"min-w":[{"min-w":[z,t,"min","max","fit"]}],"max-w":[{"max-w":[z,t,"none","full","min","max","fit","prose",{screen:[Ht]},Ht]}],h:[{h:[z,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[z,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[z,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[z,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Ht,Wt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",ei]}],"font-family":[{font:[Or]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",z]}],"line-clamp":[{"line-clamp":["none",bn,ei]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",xt,z]}],"list-image":[{"list-image":["none",z]}],"list-style-type":[{list:["none","disc","decimal",z]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",xt,Wt]}],"underline-offset":[{"underline-offset":["auto",xt,z]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:E()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...V(),sC]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",iC]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},lC]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[v]}],"gradient-via-pos":[{via:[v]}],"gradient-to-pos":[{to:[v]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...B(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:B()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...B()]}],"outline-offset":[{"outline-offset":[xt,z]}],"outline-w":[{outline:[xt,Wt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:F()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[xt,Wt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ht,uC]}],"shadow-color":[{shadow:[Or]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":X()}],"bg-blend":[{"bg-blend":X()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Ht,z]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[c]}],saturate:[{saturate:[g]}],sepia:[{sepia:[P]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[g]}],"backdrop-sepia":[{"backdrop-sepia":[P]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",z]}],duration:[{duration:W()}],ease:[{ease:["linear","in","out","in-out",z]}],delay:[{delay:W()}],animate:[{animate:["none","spin","ping","pulse","bounce",z]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[Dr,z]}],"translate-x":[{"translate-x":[T]}],"translate-y":[{"translate-y":[T]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",z]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",z]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":E()}],"scroll-mx":[{"scroll-mx":E()}],"scroll-my":[{"scroll-my":E()}],"scroll-ms":[{"scroll-ms":E()}],"scroll-me":[{"scroll-me":E()}],"scroll-mt":[{"scroll-mt":E()}],"scroll-mr":[{"scroll-mr":E()}],"scroll-mb":[{"scroll-mb":E()}],"scroll-ml":[{"scroll-ml":E()}],"scroll-p":[{"scroll-p":E()}],"scroll-px":[{"scroll-px":E()}],"scroll-py":[{"scroll-py":E()}],"scroll-ps":[{"scroll-ps":E()}],"scroll-pe":[{"scroll-pe":E()}],"scroll-pt":[{"scroll-pt":E()}],"scroll-pr":[{"scroll-pr":E()}],"scroll-pb":[{"scroll-pb":E()}],"scroll-pl":[{"scroll-pl":E()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",z]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[xt,Wt,ei]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}const hC=YT(pC);function yn(...e){return hC(VT(e))}const mC=Zg("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"h-full gap-1 flex bg-[var(--button)] max-md:h-12 capitalize font-bold items-center hover:shadow-xl justify-center rounded-full cursor-pointer px-6 py-3 transition duration-100 transform bg-[var(--button)] border-2 border-[var(--button-border)] text-[var(--button-text)] hoverd hover:bg-[var(--button-hover)] hover:text-[var(--button-text-hover)]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"h-full gap-1 flex max-md:h-12 capitalize font-bold items-center justify-center  bg-transparent rounded-full cursor-pointer px-6 py-3 transition duration-100 transform border-[var(--button)] border-2 border-[var(--button-border)] text-[var(--outline-button-text)] hoverd hover:bg-[var(--button-hover)] hover:text-[var(--button-text-hover)]",secondary:"h-full gap-1 flex max-md:h-12 capitalize font-bold items-center justify-center rounded-full cursor-pointer px-6 py-3 transition duration-100 transform bg-[var(--button)] border-2 border-[var(--button-border)] text-[var(--button-text)] hoverd hover:bg-[var(--button-hover)] hover:text-[var(--button-text-hover)]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",active:"transition duration-100 transform bg-[var(--active)] text-[var(--active-text)]"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),iv=w.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,icon:o,isLoading:i,children:s,...a},l)=>{const u=r?Yg:"button";return b.jsxs(u,{className:yn(mC({variant:t,size:n,className:e})),ref:l,disabled:i||a.disabled,...a,children:[i&&b.jsx(DT,{className:"mr-2 h-4 w-4 animate-spin"}),o&&!i&&b.jsx("span",{className:"mr-2",children:o}),s]})});iv.displayName="Button";function gC({className:e,angle:t=65,cellSize:n=60,opacity:r=.5,lightLineColor:o="gray",darkLineColor:i="gray",...s}){const a={"--grid-angle":`${t}deg`,"--cell-size":`${n}px`,"--opacity":r,"--light-line":o,"--dark-line":i};return b.jsxs("div",{className:yn("pointer-events-none absolute size-full overflow-hidden [perspective:200px]","opacity-[30%]",e),style:a,...s,children:[b.jsx("div",{className:"notfoundeffect absolute inset-0 [transform:rotateX(var(--grid-angle))]",children:b.jsx("div",{className:"animate-grid [background-image:linear-gradient(to_right,var(--light-line)_1px,transparent_0),linear-gradient(to_bottom,var(--light-line)_1px,transparent_0)] [background-repeat:repeat] [background-size:var(--cell-size)_var(--cell-size)] [height:300vh] [inset:0%_0px] [margin-left:-200%] [transform-origin:100%_0_0] [width:600vw] dark:[background-image:linear-gradient(to_right,var(--dark-line)_1px,transparent_0),linear-gradient(to_bottom,var(--dark-line)_1px,transparent_0)]"})}),b.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-white to-transparent to-90% dark:from-black"})]})}function vC(){return b.jsxs("div",{className:"relative flex h-[100vh] w-full flex-col items-center justify-center overflow-hidden rounded-lg bg-[var(--background)] md:shadow-xl",children:[b.jsx(Gg.span,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1},className:"pointer-events-none z-10 whitespace-pre-wrap bg-gradient-to-b from-[#ffd319] via-[#ff2975] to-[#8c1eff] bg-clip-text text-center text-7xl font-bold leading-none tracking-tighter text-transparent",children:"404 Not Found"}),b.jsx(gC,{})]})}const yC=ht.lazy(()=>rw(()=>import("./HomePage-DpP-E86B.js"),__vite__mapDeps([]))),wC=()=>{const e=()=>b.jsx(Gg.div,{className:"flex h-screen items-center justify-center bg-[var(--background)]",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:b.jsx("div",{className:"text-base",children:b.jsx(iv,{isLoading:!0,className:"flex cursor-text gap-0 border-none bg-transparent",children:"Loading..."})})});return b.jsx(w.Suspense,{fallback:b.jsx(e,{}),children:b.jsxs(Z1,{children:[b.jsx(ml,{path:"/",element:b.jsx(yC,{})}),b.jsx(ml,{path:"*",element:b.jsx(vC,{})})]})})};var xC="@vercel/analytics",SC="1.3.1",PC=()=>{window.va||(window.va=function(...t){(window.vaq=window.vaq||[]).push(t)})};function sv(){return typeof window<"u"}function av(){try{const e="production"}catch{}return"production"}function TC(e="auto"){if(e==="auto"){window.vam=av();return}window.vam=e}function CC(){return(sv()?window.vam:av())||"production"}function va(){return CC()==="development"}var EC="https://va.vercel-scripts.com/v1/script.debug.js",kC="/_vercel/insights/script.js";function RC(e={debug:!0}){var t;if(!sv())return;TC(e.mode),PC(),e.beforeSend&&((t=window.va)==null||t.call(window,"beforeSend",e.beforeSend));const n=e.scriptSrc||(va()?EC:kC);if(document.head.querySelector(`script[src*="${n}"]`))return;const r=document.createElement("script");r.src=n,r.defer=!0,r.dataset.sdkn=xC+(e.framework?`/${e.framework}`:""),r.dataset.sdkv=SC,e.disableAutoTrack&&(r.dataset.disableAutoTrack="1"),e.endpoint&&(r.dataset.endpoint=e.endpoint),e.dsn&&(r.dataset.dsn=e.dsn),r.onerror=()=>{const o=va()?"Please check if any ad blockers are enabled and try again.":"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";console.log(`[Vercel Web Analytics] Failed to load script from ${n}. ${o}`)},va()&&e.debug===!1&&(r.dataset.debug="false"),document.head.appendChild(r)}function We(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function bC(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function lv(...e){return t=>e.forEach(n=>bC(n,t))}function jn(...e){return w.useCallback(lv(...e),e)}function AC(e,t=[]){let n=[];function r(i,s){const a=w.createContext(s),l=n.length;n=[...n,s];function u(c){const{scope:f,children:m,...v}=c,y=(f==null?void 0:f[e][l])||a,x=w.useMemo(()=>v,Object.values(v));return b.jsx(y.Provider,{value:x,children:m})}function d(c,f){const m=(f==null?void 0:f[e][l])||a,v=w.useContext(m);if(v)return v;if(s!==void 0)return s;throw new Error(`\`${c}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,d]}const o=()=>{const i=n.map(s=>w.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return w.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,MC(o,...t)]}function MC(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const c=l(i)[`__scope${u}`];return{...a,...c}},{});return w.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var Ji=w.forwardRef((e,t)=>{const{children:n,...r}=e,o=w.Children.toArray(n),i=o.find(LC);if(i){const s=i.props.children,a=o.map(l=>l===i?w.Children.count(s)>1?w.Children.only(null):w.isValidElement(s)?s.props.children:null:l);return b.jsx(Nl,{...r,ref:t,children:w.isValidElement(s)?w.cloneElement(s,void 0,a):null})}return b.jsx(Nl,{...r,ref:t,children:n})});Ji.displayName="Slot";var Nl=w.forwardRef((e,t)=>{const{children:n,...r}=e;if(w.isValidElement(n)){const o=DC(n);return w.cloneElement(n,{...NC(r,n.props),ref:t?lv(t,o):o})}return w.Children.count(n)>1?w.Children.only(null):null});Nl.displayName="SlotClone";var _C=({children:e})=>b.jsx(b.Fragment,{children:e});function LC(e){return w.isValidElement(e)&&e.type===_C}function NC(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function DC(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function OC(e){const t=e+"CollectionProvider",[n,r]=AC(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=m=>{const{scope:v,children:y}=m,x=ht.useRef(null),h=ht.useRef(new Map).current;return b.jsx(o,{scope:v,itemMap:h,collectionRef:x,children:y})};s.displayName=t;const a=e+"CollectionSlot",l=ht.forwardRef((m,v)=>{const{scope:y,children:x}=m,h=i(a,y),p=jn(v,h.collectionRef);return b.jsx(Ji,{ref:p,children:x})});l.displayName=a;const u=e+"CollectionItemSlot",d="data-radix-collection-item",c=ht.forwardRef((m,v)=>{const{scope:y,children:x,...h}=m,p=ht.useRef(null),g=jn(v,p),S=i(u,y);return ht.useEffect(()=>(S.itemMap.set(p,{ref:p,...h}),()=>void S.itemMap.delete(p))),b.jsx(Ji,{[d]:"",ref:g,children:x})});c.displayName=u;function f(m){const v=i(e+"CollectionConsumer",m);return ht.useCallback(()=>{const x=v.collectionRef.current;if(!x)return[];const h=Array.from(x.querySelectorAll(`[${d}]`));return Array.from(v.itemMap.values()).sort((S,P)=>h.indexOf(S.ref.current)-h.indexOf(P.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:s,Slot:l,ItemSlot:c},f,r]}function VC(e,t=[]){let n=[];function r(i,s){const a=w.createContext(s),l=n.length;n=[...n,s];const u=c=>{var h;const{scope:f,children:m,...v}=c,y=((h=f==null?void 0:f[e])==null?void 0:h[l])||a,x=w.useMemo(()=>v,Object.values(v));return b.jsx(y.Provider,{value:x,children:m})};u.displayName=i+"Provider";function d(c,f){var y;const m=((y=f==null?void 0:f[e])==null?void 0:y[l])||a,v=w.useContext(m);if(v)return v;if(s!==void 0)return s;throw new Error(`\`${c}\` must be used within \`${i}\``)}return[u,d]}const o=()=>{const i=n.map(s=>w.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return w.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,jC(o,...t)]}function jC(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const c=l(i)[`__scope${u}`];return{...a,...c}},{});return w.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var IC=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Ft=IC.reduce((e,t)=>{const n=w.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?Ji:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),b.jsx(a,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function uv(e,t){e&&Du.flushSync(()=>e.dispatchEvent(t))}function jt(e){const t=w.useRef(e);return w.useEffect(()=>{t.current=e}),w.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function FC(e,t=globalThis==null?void 0:globalThis.document){const n=jt(e);w.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var zC="DismissableLayer",Dl="dismissableLayer.update",BC="dismissableLayer.pointerDownOutside",$C="dismissableLayer.focusOutside",Gf,cv=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),dv=w.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:a,...l}=e,u=w.useContext(cv),[d,c]=w.useState(null),f=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,m]=w.useState({}),v=jn(t,k=>c(k)),y=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),h=y.indexOf(x),p=d?y.indexOf(d):-1,g=u.layersWithOutsidePointerEventsDisabled.size>0,S=p>=h,P=WC(k=>{const T=k.target,D=[...u.branches].some(L=>L.contains(T));!S||D||(o==null||o(k),s==null||s(k),k.defaultPrevented||a==null||a())},f),C=HC(k=>{const T=k.target;[...u.branches].some(L=>L.contains(T))||(i==null||i(k),s==null||s(k),k.defaultPrevented||a==null||a())},f);return FC(k=>{p===u.layers.size-1&&(r==null||r(k),!k.defaultPrevented&&a&&(k.preventDefault(),a()))},f),w.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Gf=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),Qf(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(f.body.style.pointerEvents=Gf)}},[d,f,n,u]),w.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),Qf())},[d,u]),w.useEffect(()=>{const k=()=>m({});return document.addEventListener(Dl,k),()=>document.removeEventListener(Dl,k)},[]),b.jsx(Ft.div,{...l,ref:v,style:{pointerEvents:g?S?"auto":"none":void 0,...e.style},onFocusCapture:We(e.onFocusCapture,C.onFocusCapture),onBlurCapture:We(e.onBlurCapture,C.onBlurCapture),onPointerDownCapture:We(e.onPointerDownCapture,P.onPointerDownCapture)})});dv.displayName=zC;var UC="DismissableLayerBranch",fv=w.forwardRef((e,t)=>{const n=w.useContext(cv),r=w.useRef(null),o=jn(t,r);return w.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),b.jsx(Ft.div,{...e,ref:o})});fv.displayName=UC;function WC(e,t=globalThis==null?void 0:globalThis.document){const n=jt(e),r=w.useRef(!1),o=w.useRef(()=>{});return w.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let l=function(){pv(BC,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function HC(e,t=globalThis==null?void 0:globalThis.document){const n=jt(e),r=w.useRef(!1);return w.useEffect(()=>{const o=i=>{i.target&&!r.current&&pv($C,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Qf(){const e=new CustomEvent(Dl);document.dispatchEvent(e)}function pv(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?uv(o,i):o.dispatchEvent(i)}var KC=dv,GC=fv,es=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},QC="Portal",hv=w.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,i]=w.useState(!1);es(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?cm.createPortal(b.jsx(Ft.div,{...r,ref:t}),s):null});hv.displayName=QC;function YC(e,t){return w.useReducer((n,r)=>t[n][r]??n,e)}var mv=e=>{const{present:t,children:n}=e,r=XC(t),o=typeof n=="function"?n({present:r.isPresent}):w.Children.only(n),i=jn(r.ref,ZC(o));return typeof n=="function"||r.isPresent?w.cloneElement(o,{ref:i}):null};mv.displayName="Presence";function XC(e){const[t,n]=w.useState(),r=w.useRef({}),o=w.useRef(e),i=w.useRef("none"),s=e?"mounted":"unmounted",[a,l]=YC(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const u=ti(r.current);i.current=a==="mounted"?u:"none"},[a]),es(()=>{const u=r.current,d=o.current;if(d!==e){const f=i.current,m=ti(u);e?l("MOUNT"):m==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&f!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),es(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,c=m=>{const y=ti(r.current).includes(m.animationName);if(m.target===t&&y&&(l("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},f=m=>{m.target===t&&(i.current=ti(r.current))};return t.addEventListener("animationstart",f),t.addEventListener("animationcancel",c),t.addEventListener("animationend",c),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",f),t.removeEventListener("animationcancel",c),t.removeEventListener("animationend",c)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:w.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function ti(e){return(e==null?void 0:e.animationName)||"none"}function ZC(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function qC({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=JC({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,a=jt(n),l=w.useCallback(u=>{if(i){const c=typeof u=="function"?u(e):u;c!==e&&a(c)}else o(u)},[i,e,o,a]);return[s,l]}function JC({defaultProp:e,onChange:t}){const n=w.useState(e),[r]=n,o=w.useRef(r),i=jt(t);return w.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}function eE(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function tE(...e){return t=>e.forEach(n=>eE(n,t))}var gv=w.forwardRef((e,t)=>{const{children:n,...r}=e,o=w.Children.toArray(n),i=o.find(rE);if(i){const s=i.props.children,a=o.map(l=>l===i?w.Children.count(s)>1?w.Children.only(null):w.isValidElement(s)?s.props.children:null:l);return b.jsx(Ol,{...r,ref:t,children:w.isValidElement(s)?w.cloneElement(s,void 0,a):null})}return b.jsx(Ol,{...r,ref:t,children:n})});gv.displayName="Slot";var Ol=w.forwardRef((e,t)=>{const{children:n,...r}=e;if(w.isValidElement(n)){const o=iE(n);return w.cloneElement(n,{...oE(r,n.props),ref:t?tE(t,o):o})}return w.Children.count(n)>1?w.Children.only(null):null});Ol.displayName="SlotClone";var nE=({children:e})=>b.jsx(b.Fragment,{children:e});function rE(e){return w.isValidElement(e)&&e.type===nE}function oE(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function iE(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var sE=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],aE=sE.reduce((e,t)=>{const n=w.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?gv:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),b.jsx(a,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),lE="VisuallyHidden",As=w.forwardRef((e,t)=>b.jsx(aE.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));As.displayName=lE;var HE=As,lc="ToastProvider",[uc,uE,cE]=OC("Toast"),[vv,KE]=VC("Toast",[cE]),[dE,Ms]=vv(lc),yv=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[a,l]=w.useState(null),[u,d]=w.useState(0),c=w.useRef(!1),f=w.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${lc}\`. Expected non-empty \`string\`.`),b.jsx(uc.Provider,{scope:t,children:b.jsx(dE,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:a,onViewportChange:l,onToastAdd:w.useCallback(()=>d(m=>m+1),[]),onToastRemove:w.useCallback(()=>d(m=>m-1),[]),isFocusedToastEscapeKeyDownRef:c,isClosePausedRef:f,children:s})})};yv.displayName=lc;var wv="ToastViewport",fE=["F8"],Vl="toast.viewportPause",jl="toast.viewportResume",xv=w.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=fE,label:o="Notifications ({hotkey})",...i}=e,s=Ms(wv,n),a=uE(n),l=w.useRef(null),u=w.useRef(null),d=w.useRef(null),c=w.useRef(null),f=jn(t,c,s.onViewportChange),m=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),v=s.toastCount>0;w.useEffect(()=>{const x=h=>{var g;r.length!==0&&r.every(S=>h[S]||h.code===S)&&((g=c.current)==null||g.focus())};return document.addEventListener("keydown",x),()=>document.removeEventListener("keydown",x)},[r]),w.useEffect(()=>{const x=l.current,h=c.current;if(v&&x&&h){const p=()=>{if(!s.isClosePausedRef.current){const C=new CustomEvent(Vl);h.dispatchEvent(C),s.isClosePausedRef.current=!0}},g=()=>{if(s.isClosePausedRef.current){const C=new CustomEvent(jl);h.dispatchEvent(C),s.isClosePausedRef.current=!1}},S=C=>{!x.contains(C.relatedTarget)&&g()},P=()=>{x.contains(document.activeElement)||g()};return x.addEventListener("focusin",p),x.addEventListener("focusout",S),x.addEventListener("pointermove",p),x.addEventListener("pointerleave",P),window.addEventListener("blur",p),window.addEventListener("focus",g),()=>{x.removeEventListener("focusin",p),x.removeEventListener("focusout",S),x.removeEventListener("pointermove",p),x.removeEventListener("pointerleave",P),window.removeEventListener("blur",p),window.removeEventListener("focus",g)}}},[v,s.isClosePausedRef]);const y=w.useCallback(({tabbingDirection:x})=>{const p=a().map(g=>{const S=g.ref.current,P=[S,...EE(S)];return x==="forwards"?P:P.reverse()});return(x==="forwards"?p.reverse():p).flat()},[a]);return w.useEffect(()=>{const x=c.current;if(x){const h=p=>{var P,C,k;const g=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!g){const T=document.activeElement,D=p.shiftKey;if(p.target===x&&D){(P=u.current)==null||P.focus();return}const E=y({tabbingDirection:D?"backwards":"forwards"}),F=E.findIndex(A=>A===T);ya(E.slice(F+1))?p.preventDefault():D?(C=u.current)==null||C.focus():(k=d.current)==null||k.focus()}};return x.addEventListener("keydown",h),()=>x.removeEventListener("keydown",h)}},[a,y]),b.jsxs(GC,{ref:l,role:"region","aria-label":o.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:v?void 0:"none"},children:[v&&b.jsx(Il,{ref:u,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"forwards"});ya(x)}}),b.jsx(uc.Slot,{scope:n,children:b.jsx(Ft.ol,{tabIndex:-1,...i,ref:f})}),v&&b.jsx(Il,{ref:d,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"backwards"});ya(x)}})]})});xv.displayName=wv;var Sv="ToastFocusProxy",Il=w.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=Ms(Sv,n);return b.jsx(As,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const a=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(a))&&r()}})});Il.displayName=Sv;var _s="Toast",pE="toast.swipeStart",hE="toast.swipeMove",mE="toast.swipeCancel",gE="toast.swipeEnd",Pv=w.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[a=!0,l]=qC({prop:r,defaultProp:o,onChange:i});return b.jsx(mv,{present:n||a,children:b.jsx(wE,{open:a,...s,ref:t,onClose:()=>l(!1),onPause:jt(e.onPause),onResume:jt(e.onResume),onSwipeStart:We(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:We(e.onSwipeMove,u=>{const{x:d,y:c}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${c}px`)}),onSwipeCancel:We(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:We(e.onSwipeEnd,u=>{const{x:d,y:c}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${c}px`),l(!1)})})})});Pv.displayName=_s;var[vE,yE]=vv(_s,{onClose(){}}),wE=w.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:a,onPause:l,onResume:u,onSwipeStart:d,onSwipeMove:c,onSwipeCancel:f,onSwipeEnd:m,...v}=e,y=Ms(_s,n),[x,h]=w.useState(null),p=jn(t,A=>h(A)),g=w.useRef(null),S=w.useRef(null),P=o||y.duration,C=w.useRef(0),k=w.useRef(P),T=w.useRef(0),{onToastAdd:D,onToastRemove:L}=y,_=jt(()=>{var V;(x==null?void 0:x.contains(document.activeElement))&&((V=y.viewport)==null||V.focus()),s()}),E=w.useCallback(A=>{!A||A===1/0||(window.clearTimeout(T.current),C.current=new Date().getTime(),T.current=window.setTimeout(_,A))},[_]);w.useEffect(()=>{const A=y.viewport;if(A){const V=()=>{E(k.current),u==null||u()},B=()=>{const X=new Date().getTime()-C.current;k.current=k.current-X,window.clearTimeout(T.current),l==null||l()};return A.addEventListener(Vl,B),A.addEventListener(jl,V),()=>{A.removeEventListener(Vl,B),A.removeEventListener(jl,V)}}},[y.viewport,P,l,u,E]),w.useEffect(()=>{i&&!y.isClosePausedRef.current&&E(P)},[i,P,y.isClosePausedRef,E]),w.useEffect(()=>(D(),()=>L()),[D,L]);const F=w.useMemo(()=>x?Av(x):null,[x]);return y.viewport?b.jsxs(b.Fragment,{children:[F&&b.jsx(xE,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:F}),b.jsx(vE,{scope:n,onClose:_,children:Du.createPortal(b.jsx(uc.ItemSlot,{scope:n,children:b.jsx(KC,{asChild:!0,onEscapeKeyDown:We(a,()=>{y.isFocusedToastEscapeKeyDownRef.current||_(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:b.jsx(Ft.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":y.swipeDirection,...v,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:We(e.onKeyDown,A=>{A.key==="Escape"&&(a==null||a(A.nativeEvent),A.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,_()))}),onPointerDown:We(e.onPointerDown,A=>{A.button===0&&(g.current={x:A.clientX,y:A.clientY})}),onPointerMove:We(e.onPointerMove,A=>{if(!g.current)return;const V=A.clientX-g.current.x,B=A.clientY-g.current.y,X=!!S.current,M=["left","right"].includes(y.swipeDirection),O=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,I=M?O(0,V):0,U=M?0:O(0,B),W=A.pointerType==="touch"?10:2,de={x:I,y:U},te={originalEvent:A,delta:de};X?(S.current=de,ni(hE,c,te,{discrete:!1})):Yf(de,y.swipeDirection,W)?(S.current=de,ni(pE,d,te,{discrete:!1}),A.target.setPointerCapture(A.pointerId)):(Math.abs(V)>W||Math.abs(B)>W)&&(g.current=null)}),onPointerUp:We(e.onPointerUp,A=>{const V=S.current,B=A.target;if(B.hasPointerCapture(A.pointerId)&&B.releasePointerCapture(A.pointerId),S.current=null,g.current=null,V){const X=A.currentTarget,M={originalEvent:A,delta:V};Yf(V,y.swipeDirection,y.swipeThreshold)?ni(gE,m,M,{discrete:!0}):ni(mE,f,M,{discrete:!0}),X.addEventListener("click",O=>O.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),xE=e=>{const{__scopeToast:t,children:n,...r}=e,o=Ms(_s,t),[i,s]=w.useState(!1),[a,l]=w.useState(!1);return TE(()=>s(!0)),w.useEffect(()=>{const u=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:b.jsx(hv,{asChild:!0,children:b.jsx(As,{...r,children:i&&b.jsxs(b.Fragment,{children:[o.label," ",n]})})})},SE="ToastTitle",Tv=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return b.jsx(Ft.div,{...r,ref:t})});Tv.displayName=SE;var PE="ToastDescription",Cv=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return b.jsx(Ft.div,{...r,ref:t})});Cv.displayName=PE;var Ev="ToastAction",kv=w.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?b.jsx(bv,{altText:n,asChild:!0,children:b.jsx(cc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Ev}\`. Expected non-empty \`string\`.`),null)});kv.displayName=Ev;var Rv="ToastClose",cc=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=yE(Rv,n);return b.jsx(bv,{asChild:!0,children:b.jsx(Ft.button,{type:"button",...r,ref:t,onClick:We(e.onClick,o.onClose)})})});cc.displayName=Rv;var bv=w.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return b.jsx(Ft.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Av(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),CE(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Av(r))}}),t}function ni(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?uv(o,i):o.dispatchEvent(i)}var Yf=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function TE(e=()=>{}){const t=jt(e);es(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function CE(e){return e.nodeType===e.ELEMENT_NODE}function EE(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ya(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var kE=yv,Mv=xv,_v=Pv,Lv=Tv,Nv=Cv,Dv=kv,Ov=cc;const RE=kE,Vv=w.forwardRef(({className:e,...t},n)=>b.jsx(Mv,{ref:n,className:yn("fixed bottom-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Vv.displayName=Mv.displayName;const bE=Zg("group pointer-events-auto relative  flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full ",{variants:{variant:{default:"bg-white rounded-md text-stone-700 shadow-sm border p-2 py-4",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),jv=w.forwardRef(({className:e,variant:t,...n},r)=>b.jsx(_v,{ref:r,className:yn(bE({variant:t}),e),...n}));jv.displayName=_v.displayName;const AE=w.forwardRef(({className:e,...t},n)=>b.jsx(Dv,{ref:n,className:yn("group-[.destructive]:focus:ring-destructiv group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground",e),...t}));AE.displayName=Dv.displayName;const Iv=w.forwardRef(({className:e,...t},n)=>b.jsx(Ov,{ref:n,className:yn("text-foreground/50 absolute right-2 top-2 rounded-md p-1 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:b.jsx(OT,{className:"h-4 w-4"})}));Iv.displayName=Ov.displayName;const Fv=w.forwardRef(({className:e,...t},n)=>b.jsx(Lv,{ref:n,className:yn("text-sm font-semibold",e),...t}));Fv.displayName=Lv.displayName;const zv=w.forwardRef(({className:e,...t},n)=>b.jsx(Nv,{ref:n,className:yn("text-sm opacity-90",e),...t}));zv.displayName=Nv.displayName;const ME=20,_E=1e3;let wa=0;function LE(){return wa=(wa+1)%Number.MAX_SAFE_INTEGER,wa.toString()}const xa=new Map,Xf=e=>{if(xa.has(e))return;const t=setTimeout(()=>{xa.delete(e),to({type:"REMOVE_TOAST",toastId:e})},_E);xa.set(e,t)},NE=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,ME)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?Xf(n):e.toasts.forEach(r=>{Xf(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},yi=[];let wi={toasts:[]};function to(e){wi=NE(wi,e),yi.forEach(t=>{t(wi)})}function DE({...e}){const t=LE(),n=o=>to({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>to({type:"DISMISS_TOAST",toastId:t});return to({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function OE(){const[e,t]=w.useState(wi);return w.useEffect(()=>(yi.push(t),()=>{const n=yi.indexOf(t);n>-1&&yi.splice(n,1)}),[e]),{...e,toast:DE,dismiss:n=>to({type:"DISMISS_TOAST",toastId:n})}}function VE(){const{toasts:e}=OE();return b.jsxs(RE,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return b.jsxs(jv,{className:"my-1 bg-gray-50",...i,children:[b.jsxs("div",{className:"grid gap-1",children:[n&&b.jsx(Fv,{children:n}),r&&b.jsx(zv,{children:r})]}),o,b.jsx(Iv,{})]},t)}),b.jsx(Vv,{})]})}const jE=()=>b.jsx("div",{className:"absolute inset-0 -z-10 h-full w-full bg-[var(--background)] bg-[linear-gradient(to_right,var(--grid-color,rgba(139,92,246,0.025))_1px,transparent_1px),linear-gradient(to_bottom,var(--grid-color,rgba(139,92,246,0.025))_1px,transparent_1px)] bg-[size:14px_24px]"}),IE=()=>(RC(),b.jsxs("div",{dir:"ltr",className:"App relative min-h-screen overflow-x-hidden",children:[b.jsx(jE,{}),b.jsx(VE,{}),b.jsx(wC,{})]}));var Bv={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(Uv,function(){return function(n){function r(i){if(o[i])return o[i].exports;var s=o[i]={exports:{},id:i,loaded:!1};return n[i].call(s.exports,s,s.exports,r),s.loaded=!0,s.exports}var o={};return r.m=n,r.c=o,r.p="dist/",r(0)}([function(n,r,o){function i(A){return A&&A.__esModule?A:{default:A}}var s=Object.assign||function(A){for(var V=1;V<arguments.length;V++){var B=arguments[V];for(var X in B)Object.prototype.hasOwnProperty.call(B,X)&&(A[X]=B[X])}return A},a=o(1),l=(i(a),o(6)),u=i(l),d=o(7),c=i(d),f=o(8),m=i(f),v=o(9),y=i(v),x=o(10),h=i(x),p=o(11),g=i(p),S=o(14),P=i(S),C=[],k=!1,T={offset:120,delay:0,easing:"ease",duration:400,disable:!1,once:!1,startEvent:"DOMContentLoaded",throttleDelay:99,debounceDelay:50,disableMutationObserver:!1},D=function(){var A=arguments.length>0&&arguments[0]!==void 0&&arguments[0];if(A&&(k=!0),k)return C=(0,g.default)(C,T),(0,h.default)(C,T.once),C},L=function(){C=(0,P.default)(),D()},_=function(){C.forEach(function(A,V){A.node.removeAttribute("data-aos"),A.node.removeAttribute("data-aos-easing"),A.node.removeAttribute("data-aos-duration"),A.node.removeAttribute("data-aos-delay")})},E=function(A){return A===!0||A==="mobile"&&y.default.mobile()||A==="phone"&&y.default.phone()||A==="tablet"&&y.default.tablet()||typeof A=="function"&&A()===!0},F=function(A){T=s(T,A),C=(0,P.default)();var V=document.all&&!window.atob;return E(T.disable)||V?_():(T.disableMutationObserver||m.default.isSupported()||(console.info(`
      aos: MutationObserver is not supported on this browser,
      code mutations observing has been disabled.
      You may have to call "refreshHard()" by yourself.
    `),T.disableMutationObserver=!0),document.querySelector("body").setAttribute("data-aos-easing",T.easing),document.querySelector("body").setAttribute("data-aos-duration",T.duration),document.querySelector("body").setAttribute("data-aos-delay",T.delay),T.startEvent==="DOMContentLoaded"&&["complete","interactive"].indexOf(document.readyState)>-1?D(!0):T.startEvent==="load"?window.addEventListener(T.startEvent,function(){D(!0)}):document.addEventListener(T.startEvent,function(){D(!0)}),window.addEventListener("resize",(0,c.default)(D,T.debounceDelay,!0)),window.addEventListener("orientationchange",(0,c.default)(D,T.debounceDelay,!0)),window.addEventListener("scroll",(0,u.default)(function(){(0,h.default)(C,T.once)},T.throttleDelay)),T.disableMutationObserver||m.default.ready("[data-aos]",L),C)};n.exports={init:F,refresh:D,refreshHard:L}},function(n,r){},,,,,function(n,r){(function(o){function i(E,F,A){function V(Q){var Oe=te,$t=De;return te=De=void 0,zt=Q,ae=E.apply($t,Oe)}function B(Q){return zt=Q,pe=setTimeout(O,F),Bt?V(Q):ae}function X(Q){var Oe=Q-$e,$t=Q-zt,dc=F-Oe;return wt?L(dc,ge-$t):dc}function M(Q){var Oe=Q-$e,$t=Q-zt;return $e===void 0||Oe>=F||Oe<0||wt&&$t>=ge}function O(){var Q=_();return M(Q)?I(Q):void(pe=setTimeout(O,X(Q)))}function I(Q){return pe=void 0,ne&&te?V(Q):(te=De=void 0,ae)}function U(){pe!==void 0&&clearTimeout(pe),zt=0,te=$e=De=pe=void 0}function W(){return pe===void 0?ae:I(_())}function de(){var Q=_(),Oe=M(Q);if(te=arguments,De=this,$e=Q,Oe){if(pe===void 0)return B($e);if(wt)return pe=setTimeout(O,F),V($e)}return pe===void 0&&(pe=setTimeout(O,F)),ae}var te,De,ge,ae,pe,$e,zt=0,Bt=!1,wt=!1,ne=!0;if(typeof E!="function")throw new TypeError(f);return F=d(F)||0,a(A)&&(Bt=!!A.leading,wt="maxWait"in A,ge=wt?D(d(A.maxWait)||0,F):ge,ne="trailing"in A?!!A.trailing:ne),de.cancel=U,de.flush=W,de}function s(E,F,A){var V=!0,B=!0;if(typeof E!="function")throw new TypeError(f);return a(A)&&(V="leading"in A?!!A.leading:V,B="trailing"in A?!!A.trailing:B),i(E,F,{leading:V,maxWait:F,trailing:B})}function a(E){var F=typeof E>"u"?"undefined":c(E);return!!E&&(F=="object"||F=="function")}function l(E){return!!E&&(typeof E>"u"?"undefined":c(E))=="object"}function u(E){return(typeof E>"u"?"undefined":c(E))=="symbol"||l(E)&&T.call(E)==v}function d(E){if(typeof E=="number")return E;if(u(E))return m;if(a(E)){var F=typeof E.valueOf=="function"?E.valueOf():E;E=a(F)?F+"":F}if(typeof E!="string")return E===0?E:+E;E=E.replace(y,"");var A=h.test(E);return A||p.test(E)?g(E.slice(2),A?2:8):x.test(E)?m:+E}var c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(E){return typeof E}:function(E){return E&&typeof Symbol=="function"&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},f="Expected a function",m=NaN,v="[object Symbol]",y=/^\s+|\s+$/g,x=/^[-+]0x[0-9a-f]+$/i,h=/^0b[01]+$/i,p=/^0o[0-7]+$/i,g=parseInt,S=(typeof o>"u"?"undefined":c(o))=="object"&&o&&o.Object===Object&&o,P=(typeof self>"u"?"undefined":c(self))=="object"&&self&&self.Object===Object&&self,C=S||P||Function("return this")(),k=Object.prototype,T=k.toString,D=Math.max,L=Math.min,_=function(){return C.Date.now()};n.exports=s}).call(r,function(){return this}())},function(n,r){(function(o){function i(_,E,F){function A(ne){var Q=de,Oe=te;return de=te=void 0,$e=ne,ge=_.apply(Oe,Q)}function V(ne){return $e=ne,ae=setTimeout(M,E),zt?A(ne):ge}function B(ne){var Q=ne-pe,Oe=ne-$e,$t=E-Q;return Bt?D($t,De-Oe):$t}function X(ne){var Q=ne-pe,Oe=ne-$e;return pe===void 0||Q>=E||Q<0||Bt&&Oe>=De}function M(){var ne=L();return X(ne)?O(ne):void(ae=setTimeout(M,B(ne)))}function O(ne){return ae=void 0,wt&&de?A(ne):(de=te=void 0,ge)}function I(){ae!==void 0&&clearTimeout(ae),$e=0,de=pe=te=ae=void 0}function U(){return ae===void 0?ge:O(L())}function W(){var ne=L(),Q=X(ne);if(de=arguments,te=this,pe=ne,Q){if(ae===void 0)return V(pe);if(Bt)return ae=setTimeout(M,E),A(pe)}return ae===void 0&&(ae=setTimeout(M,E)),ge}var de,te,De,ge,ae,pe,$e=0,zt=!1,Bt=!1,wt=!0;if(typeof _!="function")throw new TypeError(c);return E=u(E)||0,s(F)&&(zt=!!F.leading,Bt="maxWait"in F,De=Bt?T(u(F.maxWait)||0,E):De,wt="trailing"in F?!!F.trailing:wt),W.cancel=I,W.flush=U,W}function s(_){var E=typeof _>"u"?"undefined":d(_);return!!_&&(E=="object"||E=="function")}function a(_){return!!_&&(typeof _>"u"?"undefined":d(_))=="object"}function l(_){return(typeof _>"u"?"undefined":d(_))=="symbol"||a(_)&&k.call(_)==m}function u(_){if(typeof _=="number")return _;if(l(_))return f;if(s(_)){var E=typeof _.valueOf=="function"?_.valueOf():_;_=s(E)?E+"":E}if(typeof _!="string")return _===0?_:+_;_=_.replace(v,"");var F=x.test(_);return F||h.test(_)?p(_.slice(2),F?2:8):y.test(_)?f:+_}var d=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(_){return typeof _}:function(_){return _&&typeof Symbol=="function"&&_.constructor===Symbol&&_!==Symbol.prototype?"symbol":typeof _},c="Expected a function",f=NaN,m="[object Symbol]",v=/^\s+|\s+$/g,y=/^[-+]0x[0-9a-f]+$/i,x=/^0b[01]+$/i,h=/^0o[0-7]+$/i,p=parseInt,g=(typeof o>"u"?"undefined":d(o))=="object"&&o&&o.Object===Object&&o,S=(typeof self>"u"?"undefined":d(self))=="object"&&self&&self.Object===Object&&self,P=g||S||Function("return this")(),C=Object.prototype,k=C.toString,T=Math.max,D=Math.min,L=function(){return P.Date.now()};n.exports=i}).call(r,function(){return this}())},function(n,r){function o(d){var c=void 0,f=void 0;for(c=0;c<d.length;c+=1)if(f=d[c],f.dataset&&f.dataset.aos||f.children&&o(f.children))return!0;return!1}function i(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function s(){return!!i()}function a(d,c){var f=window.document,m=i(),v=new m(l);u=c,v.observe(f.documentElement,{childList:!0,subtree:!0,removedNodes:!0})}function l(d){d&&d.forEach(function(c){var f=Array.prototype.slice.call(c.addedNodes),m=Array.prototype.slice.call(c.removedNodes),v=f.concat(m);if(o(v))return u()})}Object.defineProperty(r,"__esModule",{value:!0});var u=function(){};r.default={isSupported:s,ready:a}},function(n,r){function o(f,m){if(!(f instanceof m))throw new TypeError("Cannot call a class as a function")}function i(){return navigator.userAgent||navigator.vendor||window.opera||""}Object.defineProperty(r,"__esModule",{value:!0});var s=function(){function f(m,v){for(var y=0;y<v.length;y++){var x=v[y];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(m,x.key,x)}}return function(m,v,y){return v&&f(m.prototype,v),y&&f(m,y),m}}(),a=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,l=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,u=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i,d=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,c=function(){function f(){o(this,f)}return s(f,[{key:"phone",value:function(){var m=i();return!(!a.test(m)&&!l.test(m.substr(0,4)))}},{key:"mobile",value:function(){var m=i();return!(!u.test(m)&&!d.test(m.substr(0,4)))}},{key:"tablet",value:function(){return this.mobile()&&!this.phone()}}]),f}();r.default=new c},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=function(s,a,l){var u=s.node.getAttribute("data-aos-once");a>s.position?s.node.classList.add("aos-animate"):typeof u<"u"&&(u==="false"||!l&&u!=="true")&&s.node.classList.remove("aos-animate")},i=function(s,a){var l=window.pageYOffset,u=window.innerHeight;s.forEach(function(d,c){o(d,u+l,a)})};r.default=i},function(n,r,o){function i(u){return u&&u.__esModule?u:{default:u}}Object.defineProperty(r,"__esModule",{value:!0});var s=o(12),a=i(s),l=function(u,d){return u.forEach(function(c,f){c.node.classList.add("aos-init"),c.position=(0,a.default)(c.node,d.offset)}),u};r.default=l},function(n,r,o){function i(u){return u&&u.__esModule?u:{default:u}}Object.defineProperty(r,"__esModule",{value:!0});var s=o(13),a=i(s),l=function(u,d){var c=0,f=0,m=window.innerHeight,v={offset:u.getAttribute("data-aos-offset"),anchor:u.getAttribute("data-aos-anchor"),anchorPlacement:u.getAttribute("data-aos-anchor-placement")};switch(v.offset&&!isNaN(v.offset)&&(f=parseInt(v.offset)),v.anchor&&document.querySelectorAll(v.anchor)&&(u=document.querySelectorAll(v.anchor)[0]),c=(0,a.default)(u).top,v.anchorPlacement){case"top-bottom":break;case"center-bottom":c+=u.offsetHeight/2;break;case"bottom-bottom":c+=u.offsetHeight;break;case"top-center":c+=m/2;break;case"bottom-center":c+=m/2+u.offsetHeight;break;case"center-center":c+=m/2+u.offsetHeight/2;break;case"top-top":c+=m;break;case"bottom-top":c+=u.offsetHeight+m;break;case"center-top":c+=u.offsetHeight/2+m}return v.anchorPlacement||v.offset||isNaN(d)||(f=d),c+f};r.default=l},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=function(i){for(var s=0,a=0;i&&!isNaN(i.offsetLeft)&&!isNaN(i.offsetTop);)s+=i.offsetLeft-(i.tagName!="BODY"?i.scrollLeft:0),a+=i.offsetTop-(i.tagName!="BODY"?i.scrollTop:0),i=i.offsetParent;return{top:a,left:s}};r.default=o},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=function(i){return i=i||document.querySelectorAll("[data-aos]"),Array.prototype.map.call(i,function(s){return{node:s}})};r.default=o}])})})(Bv);var FE=Bv.exports;const zE=Fl(FE);zE.init();cm.render(b.jsx(ht.StrictMode,{children:b.jsx(ew,{children:b.jsx(IE,{})})}),document.getElementById("root"));export{cm as $,iv as B,wg as L,_g as M,qu as P,ht as R,As as V,qi as _,DP as a,xe as b,Vt as c,BE as d,Pm as e,K as f,qg as g,yn as h,Re as i,b as j,Du as k,iy as l,Co as m,HE as n,Yg as o,WE as p,Qg as q,w as r,Gg as s,DE as t,QP as u};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
