<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <link
      rel="icon"
      type="image/png"
      href="app/favicon/favicon-48x48.png"
      sizes="48x48"
    />
    <link rel="icon" type="image/svg+xml" href="app/favicon/favicon.svg" />
    <link rel="shortcut icon" href="app/favicon/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="app/favicon/apple-touch-icon.png"
    />
    <meta name="apple-mobile-web-app-title" content="Gradients" />
    <link rel="manifest" href="app/favicon/site.webmanifest" />

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Gradient Colors Collection</title>

    <link
      href="https://api.fontshare.com/v2/css?f[]=author@300,400,500,600,700,1&display=swap"
      rel="stylesheet"
    />
    <!-- Meta tags for SEO -->
    <meta
      name="description"
      content="Discover and explore beautiful gradient colors for your projects. GradientsCSS offers a curated collection of gradient palettes for web design and creative work."
    />
    <meta
      name="keywords"
      content="gradients, CSS gradients, color palettes, gradient colors, web design"
    />

    <meta name="author" content="GradientsCSS" />
    <meta name="robots" content="index, follow" />
    <meta name="referrer" content="origin" />
    <meta property="og:title" content="Gradient Colors Collection" />
    <meta
      property="og:description"
      content="Discover and explore beautiful gradient colors for your projects. GradientsCSS offers a curated collection of gradient palettes for web design and creative work."
    />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/app/favicon/banner.jpg" />
    <meta property="og:image:alt" content="Gradient Colors Collection" />
    <meta property="og:site_name" content="GradientsCSS" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Gradient Colors Collection" />
    <meta
      name="twitter:description"
      content="Discover and explore beautiful gradient colors for your projects. GradientsCSS offers a curated collection of gradient palettes for web design and creative work."
    />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
