import React from "react";
import ReactDOM from "react-dom";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import App from "./App.tsx";
import "../app/css/globals.css";
import "./index.css";

import AOS from "aos";
import "aos/dist/aos.css";
AOS.init();

ReactDOM.render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>,
  document.getElementById("root"),
);
